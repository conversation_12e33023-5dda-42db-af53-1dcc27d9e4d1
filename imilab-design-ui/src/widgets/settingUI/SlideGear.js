import PropTypes from 'prop-types';
import {ViewPropTypes} from 'deprecated-react-native-prop-types'
import React from 'react';
import {Animated, PanResponder, StyleSheet, View,Text,Image} from 'react-native';
import Block from "./Block";
import {XText} from "react-native-easy-app";
import {imiThemeManager} from "../../style/ThemeManager";
//import {imiThemeManager} from "../../..";

/**
 * @description 容器和滑块的圆角类型
 * @enum {string}
 */
const TYPE = {
    /*圆形*/
    CIRCLE: 'circle',
    /* 方形*/
    RECTANGLE: 'rectangle'
};
Object.freeze(TYPE);
const DEFAULT_SIZE = 40; // 滑块默认大小
const MIN_MARGIN = 5; // 滑块和容器的最小间距
const MAX_MARGIN = 10; // 滑块和容器的最大间距
const DEFAULT_HEIGHT = 26; // 容器默认高度
const DEFAULT_BLOCK_COLOR = '#fff'; // 滑块默认颜色
const INDICATOR_TYPE = {
    /**
     * 外部档位式指示器
     */
    OUTER: 'outer',
    /**
     * 内部
     */
    INNER: 'inner'
}
Object.freeze(INDICATOR_TYPE);
/**
 * @export
 * @module SlideGear
 * @description 档位控件，滑动选择
 * @property {TYPE} type - 容器和滑块的圆角类型
 * @property {INDICATOR_TYPE} indicatorType - 指示器的类型
 * @property {array<string>} options - 档位可选项，以字符串数组表示，必填
 * @property {array<number>} indicatorTextArray,滑动条上的档位指示文字，以数组表示。
 * @property {style} indicatorContainerStyle - 滑动条上方指示文字的容器的样式，
 * @property {style} indicatorTextStyle - 滑动条上方指示文字的样式，
 * @property {style} containerStyle - 容器样式，设置背景颜色无效
 * @property {style} blockStyle - 滑块样式，尺寸始终比容器小
 * @property {style} sliderGearStyle - 当数字指示器为为内部样式时，用来控制SlideGear的宽度和定位，不传，宽度默认100%
 * @property {string} minimumTrackTintColor - 滑块左侧填充颜色
 * @property {string} maximumTrackTintColor - 滑块右侧填充颜色
 * @property {number} value - 被选择档位的数组下标, `0<=value<=options.length -1`
 * @property {boolean} disabled - 是否禁用交互，默认`false`
 * @property {boolean} stopAtIndicatorText - 滑块只能停靠在档位指示文字下，默认为false
 * @property {function} onValueChange - 滑动时的回调函数
 */
export default class SlideGear extends React.Component {
    static propTypes = {
        type: PropTypes.oneOf([TYPE.CIRCLE, TYPE.RECTANGLE]),
        indicatorType: PropTypes.oneOf([INDICATOR_TYPE.OUTER,INDICATOR_TYPE.INNER]),
        options: PropTypes.array.isRequired,
        indicatorTextArray: PropTypes.array,
        indicatorContainerStyle: PropTypes.object,
        indicatorTextStyle: PropTypes.object,
        containerStyle: PropTypes.object,
        blockStyle: PropTypes.object,
        sliderGearStyle:PropTypes.object,
        innerIndicatorTextStyle: PropTypes.object,
        minimumTrackTintColor: PropTypes.string,
        maximumTrackTintColor: PropTypes.string,
        value: PropTypes.number,
        disabled: PropTypes.bool,
        stopAtIndicatorText: PropTypes.bool,
        onValueChange: PropTypes.func,
        onSlidingComplete: PropTypes.func.isRequired,
        source: PropTypes.any,//滑块图片
        textValue: PropTypes.string,//文案
        textValueStyle: PropTypes.object,
        isSurrounded: PropTypes.bool,//按钮是否有外层包裹
        ...ViewPropTypes
    }
    static defaultProps = {
        type: TYPE.CIRCLE,
        indicatorType:INDICATOR_TYPE.OUTER,
        options: [],
        indicatorContainerStyle:{},
        indicatorTextStyle:{},
        containerStyle: {},
        blockStyle: {},
        sliderGearStyle:{},
        innerIndicatorTextStyle: null,
        minimumTrackTintColor: imiThemeManager.theme.primaryColor,
        maximumTrackTintColor: '#00000019',
        stopAtIndicatorText:false,
        value: 0,
        disabled: false,
        source: null,
        textValue: null,//文案
        textValueStyle:{},
        isSurrounded: false,//按钮是否有外层包裹
    }
    // /**
    //  * @description 容器和滑块的圆角类型
    //  * @enum {string}
    //  */
    // static TYPE = TYPE
    /**
     * @description 指示器的样式
     * @enum {string}
     */
    static INDICATOR_TYPE = INDICATOR_TYPE

    constructor(props, context) {
        super(props, context);

        this.options = this.props.options;
        this.length = this.props.options.length;

        const {margin, blockWidth, blockHeight, containerHeight} = this.getCorrectLayout();
        this.margin = margin;
        this.blockWidth = blockWidth;
        this.blockHeight = blockHeight;
        this.containerHeight = containerHeight;

        console.log(`滑块高度: ${blockHeight}\n滑块宽度: ${blockWidth}\n滑块周围间距: ${margin}\n容器高度: ${containerHeight}`);
        this.state = {
            pan: new Animated.Value(0),
            moveX: new Animated.Value(0),
            value: this.props.value,
            dragToValueMin: 0,
            dragToValueMax: 0,
        };
        this.maxValue = this.props.options[this.length-1]; //最大档位
        this.translateX = 0; // 记录拖拽距离
        this.offset = 0; // 手势触摸点和中心左边偏差值
        this.constructPanResponder(props);
    }

    /**
     * 根据传参动态创建手势控制器
     * @param {object} props
     */
    constructPanResponder(props) {
        this.panResponder = PanResponder.create({
            onStartShouldSetPanResponder: () => true ,//用户开始触摸屏幕的时候，是否愿意成为响应者,默认为false
            onStartShouldSetPanResponderCapture: () => false,
            onMoveShouldSetPanResponder: () => !props.disabled,
            onMoveShouldSetPanResponderCapture: () => !props.disabled,
            onShouldBlockNativeResponder: () => true,
            onPanResponderTerminationRequest: () => false,
            onPanResponderGrant: this._onPanResponderGrant.bind(this), //开始手势操作
            onPanResponderMove: Animated.event([null, {dx: this.state.pan, moveX: this.state.moveX}], {useNativeDriver: false}),
            onPanResponderRelease: this._onPanResponderRelease.bind(this),
            onPanResponderTerminate: this._onPanResponderRelease.bind(this)
        });
    }

    /**
     * 接收 options / value 动态变化
     * @param {object} newProps
     */
    UNSAFE_componentWillReceiveProps(newProps) {
        if (this.sliding) {  // 为了避免不必要的冲突，在滑动时，拒绝一切外部状态更新
            return;
        }
        const {value, disabled} = newProps;
        if (disabled !== this.props.disabled) {
            this.constructPanResponder(newProps);
        }
        const {options} = newProps;
        if ((value === this.state.value) && this.isSameArray(options, this.props.options)
            &&!this.props.stopAtIndicatorText) // 没有变化,终止逻辑。如果是stopAtIndicatorText为true，必须继续，走到calculateCoord来重新刷新滑块位置
            return;

        if (!this.isSameArray(options, this.props.options)) { // options 变化
            if (!(options instanceof Array) || options.length === 0) { // 更新后的 options 不是数组或者是空数组
                console.log('options 不是数组或者是空数组');
                this.showNothing = true;
                return;
            } else { // options 正确更新
                this.showNothing = false;
                this.options = options;
                this.length = options.length;
            }
        }
        if (value !== this.props.value) { // value 变化
            if (value < 0 || value >= this.length) { // 更新后的 value 越界
                console.log('value 不在 options 范围内');
                this.state.value = 0; // 如果越界，设置一个默认值
            } else {
                this.state.value = value; // value 正确更新
            }
        }

        this.calculateCoord(this.containerLayout); // 根据更新后的 options 和 value 重新计算 滑块坐标  和各个选项坐标或者间隙d
    }

    /**
     * 判断两个数组是否完全相等
     * @param {array} arr1
     * @param {array} arr2
     */
    isSameArray(arr1, arr2) {
        if (!(arr1 instanceof Array) || !(arr2 instanceof Array)) return false;
        if (arr1.length !== arr2.length) return false;
        for (let i = 0; i < arr1.length; i++) {
            if (arr1[i] !== arr2[i]) return false;
        }
        return true;
    }

    UNSAFE_componentWillMount() {
        // 拖拽变化值监听
        this.state.pan.addListener(e => {
            const {dragToValueMin: min, dragToValueMax: max} = this.state;
            if (e.value >= min && e.value <= max) {
                this.translateX = e.value;
            } else {
                this.translateX = e.value < min ? min : max;
            }
            this._background.setNativeProps({
                width: this.translateX + (this.props.isSurrounded?0:this.margin * 2) + this.blockWidth - this.state.dragToValueMin
            })
        });
        // 拖拽手势坐标监听
        this.state.moveX.addListener(e => {
            const index = this.getClosetIndex(e.value); // 滑块滑动后的物理值（options数组情况下是数组的下标，option为范围时是范围内的具体值）
            if (this.props.onValueChange || this.props.indicatorType == INDICATOR_TYPE.INNER) { //显示在组件内部的value要实时标识目前拖拽的进度
                this.props.onValueChange && this.props.onValueChange(index);
                this.setState({value:index});
            }
        });
    }

    /**
     * @description
     * options数组情况下：获取距离拖拽元素最近的选项下标
     *                  根据释放时的绝对坐标和各个选项的绝对坐标距离做对比
     * options范围情况下：根据step计算出范围内的某一值
     */
    getClosetIndex(moveX) {
        const adjustCoord = moveX - this.offset; // 拖拽过程中Block的中心点坐标
        console.log("拖拽过程中Block的中心点坐标adjustCoord: ", adjustCoord)

        const diffs = this.coords.map(coord => Math.abs(coord - adjustCoord));
        return diffs.indexOf(Math.min(...diffs));

    }

    /**
     * @description 手势开始回调
     */
    _onPanResponderGrant(e, gesture) {
        // 每次拖拽手势开始时，需要重置
        this.sliding = true; // 表示正在滑动交互
        this.state.pan.setOffset(this.translateX);
        this.state.pan.setValue(0);
        // 为了准确确定释放位置，需要在起手的时候，计算出手势触摸点和中心点的偏差
        const {pageX} = e.nativeEvent;
        this.offset = pageX - this.currentCoord;
        console.log(`滑块中心坐标: ${this.currentCoord}\n触摸点坐标: ${pageX}\nthis.translateX: ${this.translateX}`);
    }

    /**
     * @description 手势释放回调
     */
    _onPanResponderRelease(e, gesture) {
        const coord = gesture.moveX - this.offset;
        console.log("计算currentCoord时的coord：", coord)

        const min = this.coords[0];
        const max = this.coords[this.length - 1];
        if (coord >= min && coord <= max) {
            this.currentCoord = coord;
        } else {
            this.currentCoord = coord < min ? min : max;
        }
        const index = this.getClosetIndex(gesture.moveX);
        this.state.value = index;
        if (this.props.onSlidingComplete) {
            this.props.onSlidingComplete(index);
            this.state.value = index;
        }
        this.offset = 0;
        console.log(`手势结束坐标: ${coord}\n滑块最终坐标: ${this.currentCoord}\n离滑块最近的选项下标: ${index}`);
        console.log('⬆️⬆️⬆️⬆️⬆️⬆️⬆️滑动结束⬆️⬆️⬆️⬆️⬆️⬆️⬆️');
        this.sliding = false;
    }

    /**
     * @description 根据选项的宽度、间距和 maxWidth ，计算容器实际宽度，选项实际宽度，实际间距
     * @returns {{margin,blockWidth, blockHeight,containerHeight, containerWidth}}
     */
    getCorrectLayout() {
        const containerHeight = this.props.containerStyle.height || DEFAULT_HEIGHT; // 容器高度 50
        const blockWidth = this.props.blockStyle.width || DEFAULT_SIZE; // 滑块宽度
        // 重新计算
        let margin = ~~(containerHeight / 10); //5
        margin = margin > MAX_MARGIN ? MAX_MARGIN : margin;  //5
        margin = margin < MIN_MARGIN ? MIN_MARGIN : margin; //5
        const blockHeight = containerHeight - margin * 2; //50 - 10
        return {
            margin,
            blockWidth: this.props.type === TYPE.CIRCLE ? blockHeight : blockWidth,
            blockHeight,
            containerHeight,
        };
    }

    _onLayout() {
        this._container.measure((x, y, w, h, px, py) => {
            this.calculateCoord({x, y, w, h, px, py});
        })
    }

    /**
     * @description 计算整个容器的大小和在屏幕上的位置，从而确定每个选项的圆心坐标
     */
    calculateCoord(obj) {
        if (!obj) {
            return;
        }
        const {x, y, w, h} = obj;
        this.containerLayout = obj;
        const offset = (this.props.isSurrounded?0:this.margin * 2) + this.blockWidth;
        const startCoord = x + offset / 2;
        const d =  (w - offset) / (this.length - 1);
        this.d = d;
        console.log(`容器起始坐标: ${x}\n实际宽度: ${w}\n各选项中心坐标间距: ${d}`);
        if (d <= 0) {
            console.log('容器实际宽度 < 滑块宽度，滑块无法移动，请仔细检查 containerStyle 或者增加容器的宽度');
        }
        this.coords = this.options.map((v, i) => d > 0 ? (startCoord + d * i) : 0);
        console.log('各选项中心坐标', this.coords);
        this.currentCoord = this.coords[this.state.value];

        this.totalWidth = w;
        this.getDragRange();
    }

    /**
     * @description 计算可拖拽的范围
     */
    getDragRange(callback) {
        this.setState({
            dragToValueMin: this.coords[0] - this.currentCoord || 0,
            dragToValueMax: this.coords[this.length - 1] - this.currentCoord || 0,
        }, _ => {
            console.log(`滑块中心坐标: ${this.currentCoord}\n可滑动范围: ${this.state.dragToValueMin} ~ ${this.state.dragToValueMax}`);
            callback && callback();
            this.state.pan.setOffset(0);
            this.state.pan.setValue(0);
        });

    }

    /**
     * @description 滑块
     */
    renderDraggable() {
        const {dragToValueMin: min, dragToValueMax: max} = this.state;
        // 在没有找到自我定位的时候，要在舞台后面低调
        if (min === undefined) return null;
        // 可拖拽元素初始绝对定位
        const position = {
            position: 'absolute',
            // left: -min,
            left: this.props.isSurrounded?0:(-min-this.margin),
            top: -this.margin
        }
        // 显示区域
        const innerCircle = this.props.type === TYPE.CIRCLE
            ? {
                width: this.blockHeight,
                height: this.blockHeight,
                borderRadius: this.blockHeight / 2,
            }
            : {
                width: this.blockWidth,
                height: this.blockHeight,
                borderRadius: this.props.isSurrounded?6.67:0,
                shadowOffset: {
                    width: 5,
                    height: 5
                },
                shadowOpacity:0.3,
                shadowRadius:6.67,
            }
        // 手势响应区域
        const touchArea = {
            // width: this.blockWidth + this.margin * 2, //40 + 10
            width: this.blockWidth+(this.props.isSurrounded?0:this.margin * 4) , //40 + 20
            // height: this.containerHeight, //50
            height: this.containerHeight + this.margin * 2, //50 + 10
            backgroundColor: 'transparent',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: this.props.isSurrounded?6.67:0,
        }
        // 动效
        const panStyle = {
            transform: [
                {
                    translateX: this.state.pan.interpolate({
                        inputRange: [min - 1, min, max, max + 1],
                        outputRange: [min, min, max, max],
                    })
                }
            ]
        }
        return (
            <Block
                panHandlers={this.panResponder.panHandlers}
                style={[position, panStyle]}
            >
                <View style={touchArea}>
                    {this.props.source?<Image
                        source={this.props.source}
                        style={[
                            {backgroundColor: DEFAULT_BLOCK_COLOR},
                            innerCircle,
                            this.props.blockStyle
                        ]}
                    />:<View
                        style={[
                            {backgroundColor: DEFAULT_BLOCK_COLOR},
                            this.props.blockStyle,
                            innerCircle
                        ]}
                    />}
                </View>
            </Block>
        )
    }

    /**
     * 滑块左侧背景
     */
    renderBackground() {
        const {dragToValueMin: min, dragToValueMax: max} = this.state;
        // 在没有找到自我定位的时候，要在舞台后面低调
        if (min === undefined) return null;
        return (
            <Animated.View
                ref={background => this._background = background}
                style={{
                    position: 'absolute',
                    // width: this.margin * 2 + this.blockWidth - (this.state.dragToValueMin || 0),
                    width: this.state.pan.interpolate({
                        inputRange: [min - 1, min, max, max + 1],
                        outputRange: [(this.props.isSurrounded?0:this.margin * 2) + this.blockWidth, (this.props.isSurrounded?0:this.margin * 2) + this.blockWidth, this.totalWidth, this.totalWidth]
                    }),
                    height: this.containerHeight,
                    borderRadius: this.props.type === TYPE.CIRCLE ? this.containerHeight / 2 : (this.props.isSurrounded?6.67:0),
                    backgroundColor: this.props.minimumTrackTintColor,
                    alignItems:'flex-end',
                    justifyContent:"center"
                }}
            >
                {this.props.indicatorType == INDICATOR_TYPE.INNER ?
                    <Text numberOfLines={1} ellipsizeMode={'head'}
                           style={[{marginRight: 38, fontSize: 12, color: "#B2B2B2"}, this.props.innerIndicatorTextStyle]}
                    >{this.state.value > 1 ? this.state.value : null}</Text>: null}
            </Animated.View>
        )
    }

    render() {
        if (this.showNothing) return null;
        const containerStyle = {
            height: this.containerHeight,
            borderRadius: this.props.type === TYPE.CIRCLE ? this.containerHeight / 2 : 0,
            backgroundColor: this.props.maximumTrackTintColor,
            marginLeft: 14,
            marginRight: 14,
            justifyContent:"center"
        };
        const opacity = this.props.disabled ? 0.3 : 1;
        return (
            <View style={[{backgroundColor:'transparent',width:"100%"},this.props.indicatorType == INDICATOR_TYPE.INNER&&this.props.sliderGearStyle?this.props.sliderGearStyle:{}]}>
                {this.renderIndicatorText()}
                <View
                    onLayout={_ => this._onLayout()}
                    ref={container => this._container = container}
                    style={[
                        containerStyle,
                        this.props.containerStyle,
                        {opacity}
                    ]}
                >
                    {this.props.indicatorType == INDICATOR_TYPE.INNER ? <Text style={[{
                        //height: this.containerHeight,
                       // textAlignVertical: "center",
                        position: "absolute",
                        right: 8,
                        fontSize: 12,
                        color: "#B2B2B2"
                    },
                        this.props.innerIndicatorTextStyle]}>{this.maxValue}</Text>: null}
                    <View style={{width:"100%",height:"100%",position:"absolute"}}>
                        {this.renderBackground()}
                        {this.renderDraggable()}
                        {this.props.textValue?<View pointerEvents="box-none" style={{position: "absolute",right: 0,left:0,top:0,bottom:0,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[{
                                //height: this.containerHeight,
                                // textAlignVertical: "center",
                                textAlign:'center',
                                fontSize: 15,
                                color: imiThemeManager.theme.primaryColor
                            },
                                this.props.textValueStyle]}>{this.props.textValue}</Text>
                        </View>: null}
                    </View>

                </View>
            </View>


        );
    }

    renderIndicatorText() {
        return (<View style={[{marginHorizontal: 22, flexDirection: 'row', justifyContent: 'space-between'},this.props.indicatorContainerStyle]}>
            {this.props.indicatorTextArray&&this.props.indicatorTextArray.map((value) => {
                return (<XText key={'indicator'+value} style={[{color: '#B2B2B2', fontSize: 12},this.props.indicatorTextStyle]} text={value.toString()}/>)
            })}
        </View>);

    }
}
const styles = StyleSheet.create({
    textContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    text: {
        fontSize: 15,
        width: '100%',
        textAlign: 'center'
    }
})
