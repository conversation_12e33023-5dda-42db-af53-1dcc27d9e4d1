import PropTypes from 'prop-types';
import React from 'react';
import {Dimensions, Modal, Platform, StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';
import RoundedButtonView from './RoundedButtonViewNew';
import I18n from '../../../../globalization/Localize';
import {imiThemeManager} from "../../style/ThemeManager";
//import { getLanguages } from 'react-native-i18n';
const {width, height} = Dimensions.get('window');
const MODAL_WIDTH = width;
/**
 * 按钮
 * @typedef {Object} Button
 * @property {string} text - 按钮的文字
 * @property {style} style - 按钮的样式
 * @property {function} callback - 点击按钮的回调函数
 */
/**
 * @param {string} animationType - modal 显示动效, 默认`'fade'`，参考 https://facebook.github.io/react-native/docs/0.54/modal#animationtype
 * @param {bool} visible - 是否显示 modal, 默认`false`，参考 https://facebook.github.io/react-native/docs/0.54/modal#visible
 * @param {style} style - modal 的自定义样式
 * @param {string} title - 标题
 * @param {string} subtile - 副标题

 * @param {string} message - 提示信息文字，可显示单行或者多行，最多**15**行
 * @param {ViewPropTypes.style} messageStyle - 提示信息文字自定义样式
 * @param {bool} showTitle - 是否显示标题，默认`true`
 * @param {bool} canDismiss - 是否允许点击蒙层背景隐藏 Modal，默认`true`
 * @param {Button[]} buttons - 按钮数组，定义底部按钮的属性，只能显示1～2个按钮，多传将失效。默认左取消右确定，左灰右绿，点击回调都是隐藏 Modal
 * @param {bool} showButton - 是否显示按钮，默认`true`
 * @param {function} onDismiss - 点击`Modal`内容外面/取消按钮/确定按钮，Modal隐藏时的回调函数
 * @param {style} messageContainerStyle - 提示信息容器自定义样式
*/
export default class MessageDialog extends React.Component {
  static propTypes = {
    animationType: PropTypes.string,
    visible: PropTypes.bool,
    style: PropTypes.oneOfType([PropTypes.object, PropTypes.number]),
    title: PropTypes.string,
    message: PropTypes.string,
    messageStyle: PropTypes.object,
    showTitle: PropTypes.bool,
    canDismiss: PropTypes.bool,
    buttons: PropTypes.arrayOf(PropTypes.object),
    showButton: PropTypes.bool,
    onDismiss: PropTypes.func,
    bottomStyle: PropTypes.object,
    subtile: PropTypes.string,
    messageContainerStyle: PropTypes.object,


  };
  static defaultProps = {
    animationType: 'fade',
    visible: false,
    showTitle: true,
    canDismiss: true,
    buttons: [
      {
        text: I18n.t('cancel'),
        btnStyle: {
          backgroundColor: imiThemeManager.theme.primaryColor,
        },
      },
      {
        text: I18n.t('ok_button'),
        style: {
          color: '#FFFFFFE5',
        },
      },
    ],
    showButton: true,
    messageContainerStyle:{}

  };
  constructor(props, context) {
    super(props, context);
    this.state = {
      visible: this.props.visible,
    };
    /*getLanguages().then(languages => {
            console.log("------------------iiiiiiiiiiiiiiii:"+languages); // ['en-US', 'en']
        });*/
  }
  UNSAFE_componentWillReceiveProps(newProps) {
    if (newProps.visible !== this.state.visible) {
      this.setState({visible: newProps.visible});
    }
  }
  /**
   * 标题部分
   */
  renderTitle() {
    if (!this.props.showTitle) {
      return null;
    }
    const {titleHeightFat, titleHeightThin} = styles.dialogTitle;
    let height = {
      height: this.props.showSubtitle ? titleHeightFat : this.props.showTitle ? titleHeightThin : 20,
    };
    const marginBottom = this.props.showSubtitle ? {marginBottom: 22} : {};
    /*let language = Host.locale.language;
        let titleLines = 1;
        if (language !== 'zh') {
            //当前米家 app 语言不是中文
            titleLines = 3;
            height.maxHeight = 86;
        }*/
    //只给安卓手机设置字体为空字符串
    let fontFamily = {};
    if (Platform.OS === 'android') {
      //Android 设备或模拟器
      fontFamily.fontFamily = '';
    } else {
      fontFamily.fontFamily = null;
    }
    const newWidth = width > Dimensions.get('window').width ? width : Dimensions.get('window').width
    return (
      <View style={[styles.titleContainer, {maxHeight: 120}]}>
        <Text
          numberOfLines={3}
          style={[
            {
              width: newWidth * 0.75,
              textAlign: 'center',
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333333',
              marginVertical: 22,
              lineHeight: 22,
              marginBottom:this.props.subtile?2:22
            },
            /*  marginBottom,
                        fontFamily*/
          ]}>
          {this.props.title || ''}
        </Text>
         {this.props.subtile? <Text
          numberOfLines={3}
          style={[
            {
              width: newWidth * 0.75,
              textAlign: 'center',
              fontSize: 14,
              fontWeight: 'bold',
              color: 'rgba(0,0,0,0.6)',
            
              lineHeight: 21,
            },
            /*  marginBottom,
                        fontFamily*/
          ]}>
          {this.props.subtile || ''}
        </Text>:null}
      </View>
    );
  }
  /**
   * 中间内容
   */
  renderContent() {
    if (this.props.children) {
      return this.props.children;
    }
    const newWidth = width > Dimensions.get('window').width ? width : Dimensions.get('window').width
    return (
      <View>
        <View style={{...styles.dialogContent, width: newWidth}}>
          <Text numberOfLines={15} style={[styles.message, this.props.messageStyle]}>
            {this.props.message || ''}
          </Text>
        </View>
      </View>
    );
  }
  /**
   * 不显示标题距离底部view
   */
  renderBottomView() {
    if (this.props.showTitle) {
      return null;
    }
    return <View style={{height: 20}} />;
  }
  /**
   * 底部按钮
   */
  renderButtonGroup() {
    if (!this.props.showButton) {
      return null;
    }
    const buttons = this.props.buttons;
    if (!(buttons instanceof Array)) {
      return null;
    }
    if (buttons.length === 1) {
      return this.renderOneButton(buttons);
    }
    if (buttons.length === 2) {
      return this.renderTwoButtons(buttons);
    } else {
      console.log('只允许设置1～2个按钮');
      return null;
    }
  }
  /**
   * 一个按钮
   * @param {object[]} buttons
   */
  renderOneButton(buttons) {
    const {width} = Dimensions.get('window');
    const button0 = buttons[0];
    if (typeof button0 !== 'object') {
      return null;
    }
    let callback = button0.callback;
    if (callback === undefined || !(callback instanceof Function)) {
      callback = _ => this.dismiss();
    }
    return (
      <View style={styles.dialogButtons}>
        <RoundedButtonView
          buttonText={button0.text || I18n.t('cancel')}
          buttonStyle={[
            {
              width: width - 42,
              backgroundColor: button0.backgroundColor ? button0.backgroundColor : imiThemeManager.theme.primaryColor,
              margin: 14,
            },
            button0.btnStyle,
          ]}
          buttonTextStyle={[{color: '#FFFFFFE5'}, button0.style]}
          onPress={callback}
        />
      </View>
    );
  }
  /**
   * 两个按钮
   * @param {object[]} buttons
   */
  renderTwoButtons(buttons) {
    const button0 = buttons[0],
      button1 = buttons[1];
    if (typeof button0 !== 'object' || typeof button1 !== 'object') {
      return null;
    }
    let callback0 = button0.callback;
    let callback1 = button1.callback;
    if (callback0 === undefined || !(callback0 instanceof Function)) {
      callback0 = _ => this.dismiss();
    }
    if (callback1 === undefined || !(callback1 instanceof Function)) {
      callback1 = _ => this.dismiss();
    }
    const newWidth = width > Dimensions.get('window').width ? width : Dimensions.get('window').width
    return (
      <View style={styles.dialogButtons}>
        <RoundedButtonView
          buttonText={button0.text || I18n.t('cancel')}
          buttonStyle={[
            {
              width: (newWidth - 42) / 2,
              backgroundColor: button0.backgroundColor ? button0.backgroundColor : '#F2F3F5',
              margin: 14,
            },
            ,
            button0.btnStyle,
          ]}
          buttonTextStyle={[{color: '#7F7F7F'}, button0.style]}
          onPress={callback0}
        />

        <RoundedButtonView
          buttonText={button1.text || I18n.t('ok_button')}
          buttonStyle={[
            {
              width: (newWidth - 42) / 2,
              backgroundColor: button1.backgroundColor ? button1.backgroundColor : imiThemeManager.theme.primaryColor,
              margin: 14,
            },
            ,
            button1.btnStyle,
          ]}
          buttonTextStyle={[{color: '#FFFFFFE5'}, button1.style]}
          onPress={callback1}
        />
      </View>
    );
  }
  render() {
    const newWidth = width > Dimensions.get('window').width ? width : Dimensions.get('window').width
    return (
      <Modal
        supportedOrientations={['portrait', 'landscape']}
        animationType={this.props.animationType}
        transparent={true}
        visible={this.state.visible}
        onRequestClose={_ => this.dismiss()}>
        <View style={styles.dialogBackground}>
          <TouchableWithoutFeedback onPress={_ => this.dismiss()}>
            <View style={{newWidth, height}} />
          </TouchableWithoutFeedback>
          <View style={{...styles.dialogModal, ...this.props.bottomStyle,...this.props.messageContainerStyle, width: newWidth}}>
            {this.renderTitle()}
            {this.renderContent()}
            {this.renderBottomView()}
            {this.renderButtonGroup()}
            
          </View>
        </View>
      </Modal>
    );
  }
  /**
   * 隐藏 Modal
   */
  dismiss() {
    if (this.props.canDismiss) {
      this.setState({visible: false});
      this.props.onDismiss && this.props.onDismiss();
    }
  }
}
const styles = StyleSheet.create({
  titleContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  dialogBackground: {
    // 蒙层背景
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)', // 蒙层背景色
  },
  dialogModal: {
    // 弹窗
    position: 'absolute',
    bottom: 0, // 距离屏幕底部的边距
    width: MODAL_WIDTH, // 宽度
    maxHeight: height, //对话框的最大高度为屏幕的高度（适配平板）
    borderTopLeftRadius: 20, // 圆角
    borderTopRightRadius: 20, // 圆角
    backgroundColor: '#FFFFFF', // 内容背景色
  },
  dialogTitle: {
    // 标题容器
    /* titleHeightThin: 66, // 头部单行标题容器宽度
        titleHeightFat: 85, // 头部双行标题容器宽度*/
  },
  message: {
    fontSize: 15,
    color: '#666',
    lineHeight: 22,
    fontWeight: '400',
    marginHorizontal: 14,
    //textAlign:"center"
  },
  dialogSubtitle: {
    // 副标题
    width: MODAL_WIDTH * 0.75,
    textAlign: 'center',
    fontSize: 13,
    color: '#666',
  },
  dialogButtons: {
    // 按钮容器
    // height: 50, // 底部按钮的高度
    flex: 1,
    flexDirection: 'row',
    backgroundColor: 'transparent',
    justifyContent: 'space-around',
  },
  dialogButton: {
    // 单个按钮
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogButtonText: {
    // 按钮文字
    fontSize: 14,
    lineHeight: 19,
    color: '#666',
    fontWeight: 'bold',
  },

  dialogContent: {
    width: MODAL_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
