import PropTypes from 'prop-types';
import React from 'react';
import {
    Dimensions,
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableHighlight,
    View
} from 'react-native';
import {ViewPropTypes} from 'deprecated-react-native-prop-types'

const {width,height} = Dimensions.get('window');
const screen_width = width>height?height:width;
const THIN_HEIGHT = 50;
const PADDING = 24;
const ICON_SIZE = Platform.select({android: 25, ios: 23}); // 当android设置24的时候，图形会挤压形成锯齿
/**
 * @description 普通列表项
 * @property {string} title - 左侧主标题
 * @property {string} subtitle - 右侧副标题
 * @property {string} value - 右侧文案
 * @property {function} onPress - 点击事件
 * @property {function} onLongPress - 长按事件
 * @property {bool} disabled - 是否禁用点击，默认值 false
 * @property {bool} hideArrow - 是否隐藏右侧箭头图片，默认值 `false`
 * @property {style} containerStyle - 列表项的自定义样式
 * @property {style} titleStyle - 标题的自定义样式
 * @property {style} subtitleStyle - 副标题的自定义样式
 * @property {style} valueStyle - 右侧文案的自定义样式
 * @property {boolean} hide - 是否隐藏此组件
 */
export default class ListItem extends React.Component {
    static propTypes = {
        title: PropTypes.string.isRequired,
        subtitle: PropTypes.string,
        value: PropTypes.string,
        onPress: PropTypes.func.isRequired,
        onLongPress: PropTypes.func,
        disabled: PropTypes.bool,
        hideArrow: PropTypes.bool,
        containerStyle: PropTypes.object,
        titleStyle: PropTypes.object,
        subtitleStyle: PropTypes.object,
        valueStyle: PropTypes.object,
        showCircle:PropTypes.bool,
        hide: PropTypes.bool,
        accessibilityLabel: PropTypes.string,
        ...ViewPropTypes
    };
    static defaultProps = {
        title: '',
        subtitle: '',
        value: '',
        onPress: _ => {
        },
        onLongPress: _ => {
        },
        disabled: false,
        hideArrow: false,
        containerStyle: {},
        titleStyle: {},
        subtitleStyle: {},
        valueStyle: {},
        showCircle:false,
        hide: false,
        accessibilityLabel: "",
    };

    constructor(props, context) {
        super(props, context);
        this.lastClickTime = 0
    }

    render() {
        if(this.props.hide){
            return null;
        }
        let extraContainerStyle = {
            height: this.props.subtitle ? 70 : 60,
        };
        if (this.props.subtitle) {
            extraContainerStyle = {
                paddingVertical: 8,
            }
        }
        let extraRightStyle = {
            flex: 0,
        }
        if (this.props.value) {
            extraRightStyle.flex = 8;
        }
        // 如果不设置英文字体，那么外文字符串将显示不全（Android）
        /*let fontFamily = {fontFamily: ""};
        if (Platform.OS === 'android') {
            valueStyle.height = THIN_HEIGHT;
        }*/
        return (
            <View>
                <TouchableHighlight
                    disabled={this.props.disabled}
                    underlayColor={this.props.hideArrow?'#fff':'rgba(0,0,0,0.25)'}
                    onPress={this.onPress.bind(this)}
                    onLongPress={this.props.onLongPress}
                    accessibilityLabel={this.props.accessibilityLabel}
                >
                    <View style={[{
                        width: screen_width,
                        minHeight:68,
                        backgroundColor: '#fff', paddingHorizontal: 15,
                        flexDirection: 'row', alignItems: 'center'
                    }, this.props.containerStyle]}>
                        <View style={{maxWidth:this.props.value?screen_width*0.7:screen_width*0.83,marginRight:7,paddingVertical: this.props.subtitle?14:20}}>
                            <View>
                                <Text
                                    numberOfLines={2}
                                    style={[{
                                        fontSize: 15,
                                        fontWeight: 'bold',
                                        color: this.props.disabled ? '#3333334C' : '#333333',
                                        fontFamily: Platform.OS === 'android' ? "" : null
                                    }, this.props.titleStyle]}
                                >
                                    {this.props.title}
                                </Text>
                            </View>
                            {this.props.subtitle ?
                                <Text
                                    numberOfLines={4}
                                    style={[{
                                        fontSize: 12,
                                        color: this.props.disabled ?"#8080804C":"#808080",
                                        marginTop: 3,
                                    }, this.props.subtitleStyle]}
                                >
                                    {this.props.subtitle}
                                </Text>
                                : null
                            }
                        </View>


                        <View style={[styles.right, {
                            flex: 1,
                            paddingRight: this.props.value && this.props.hideArrow ? 7 : 0
                        },this.props.valueStyle]}>
                            {this.props.value ?
                                <Text
                                    numberOfLines={4}
                                    style={{flex:1,fontSize: 15, paddingLeft:4,color: this.props.disabled ?"#8080804C":"#808080",textAlign: "right"}}
                                >
                                    {this.props.value}
                                </Text>
                                : this.props.showCircle? <View style={styles.circle}/>:null
                            }
                            {!this.props.hideArrow
                                ? <Image style={styles.icon}
                                         source={require('../../../resources/images/right_arrow.png')}/>
                                : null
                            }
                        </View>

                    </View>

                </TouchableHighlight>
            </View>
        );
    }

    onPress() {
        const clickTime = Date.now()
        if (!this.lastClickTime || Math.abs(this.lastClickTime - clickTime) > 800) {  //350的时间可以延长，根据需要改变
            this.lastClickTime = clickTime
            if(this.props.onPress){
                this.props.onPress()
            }else{
                return ''
            }

        }
    }

}
const styles = StyleSheet.create({
    container: {
        width: screen_width,
        backgroundColor: '#fff',
        paddingHorizontal: PADDING,
        flexDirection: 'row',
        alignItems: 'center',
    },
    left: {
        flex: 8,
    },
    right: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end'
    },
    icon: {
        width: ICON_SIZE,
        height: ICON_SIZE,
    },
    circle:{
        backgroundColor: 'red', // <---------------------------
        borderRadius: 5,
        width: 10,
        height: 10,
        textAlign: "right",
       // alignSelf: 'stretch',
       // flex: 1,
    }
});
