'use strict';
import PropTypes from 'prop-types';
import React from 'react';
import {Dimensions, Platform, StyleSheet, Text, TouchableHighlight, View} from 'react-native';
import Switch from './Switch';

const {width} = Dimensions.get('window');
const THIN_HEIGHT = 50;
const PADDING = 24;
/**
 * @description 带开关的列表项
 * @property {string} title - 左侧主标题
 * @property {string} subtitle - 左侧副标题，主标题下方
 * @property {bool} value - 开关状态，默认值 false
 * @property {bool} disabled - 是否禁用开关，默认值 false
 * @property {function} onPress - 列表项点击事件，不传则不具有点击态（disabled）
 * @property {function} onValueChange - 开关切换事件
 * @property {boolean} hide - 是否隐藏此组件
 * @property {style} valueStyle - 右侧文案的自定义样式
 */
export default class ListItemWithSwitch extends React.Component {
    static propTypes = {
        title: PropTypes.string.isRequired,
        subtitle: PropTypes.string,
        value: PropTypes.bool,
        disabled: PropTypes.bool,
        onPress: PropTypes.func,
        onValueChange: PropTypes.func.isRequired,
        hide: PropTypes.bool,
        accessibilityLabel:PropTypes.array,
        valueStyle:PropTypes.object,
    }
    static defaultProps = {
        title: '',
        subtitle: '',
        value: false,
        disabled: false,
        hide: false,
        accessibilityLabel:[],
        valueStyle:{},
    }
    constructor(props, context) {
      super(props, context);
      this.state = {
        value: this.props.value,
      }
    }
    render() {
        if(this.props.hide){
            return null;
        }
        let extraStyle = {}
        if (this.props.valueText) {
            extraStyle.maxWidth = (width - PADDING * 2) * 0.4;
            if (this.props.containerStyle.width) {
                extraStyle.maxWidth = (this.props.containerStyle.width - PADDING * 2) * 0.4;
            }
        }
        let accessibilityLabel = "";
        if (this.props.accessibilityLabel && this.props.accessibilityLabel.length>0){
            if (this.props.accessibilityLabel.length>1){
                let index = this.state.value?1:0
                accessibilityLabel = this.props.accessibilityLabel[index];
            }else {
                accessibilityLabel = this.props.accessibilityLabel[0];
            }
        }
        return (
            <View style={{backgroundColor: '#fff'}}>
                <TouchableHighlight
                    disabled={!this.props.onPress}
                    underlayColor={'rgba(0,0,0,0.25)'}
                    onPress={this.props.onPress}
                >
                    <View style={[styles.container, {
                        paddingHorizontal: 15,
                        paddingVertical: this.props.subtitle ? 14 : 20,
                    }]}>
                        <View style={{flex: 7}}>
                            <View style={{paddingVertical: 2,marginRight:5}}>
                                <Text
                                    numberOfLines={3}
                                    ellipsizeMode='tail'
                                    style={[{
                                        fontSize: 15,
                                        fontWeight: 'bold',
                                        color: this.props.disabled ? '#3434344C' : '#343434',
                                        fontFamily: Platform.OS === 'android' ? "" : null
                                    }, extraStyle]}
                                >
                                    {this.props.title}
                                </Text>

                                {this.props.subtitle ?
                                    <Text
                                        numberOfLines={6}
                                        ellipsizeMode='tail'
                                        style={{fontSize: 12, color: this.props.disabled ?"#8080804C" :"#808080",lineHeight:15, marginTop: 3,marginRight:8}}>
                                        {this.props.subtitle}
                                    </Text>
                                    : null
                                }
                            </View>
                        </View>

                        <View style={[styles.right,this.props.valueStyle]}>

                            <Switch
                                value={this.state.value}
                                disabled={this.props.disabled}
                                onValueChange={value => this._onValueChange(value)}
                                accessibilityLabel={accessibilityLabel}
                                
                            />
                        </View>
                    </View>
                </TouchableHighlight>
            </View>
        );
    }

    // // 父组件更新数据
    UNSAFE_componentWillReceiveProps(nextProps) {
      if (nextProps.value !== this.state.value) {
        this.setState({ value: nextProps.value });
      }
    }
    _onValueChange(value) {
        this.setState({ value });
        if (this.props.onValueChange) {
            this.props.onValueChange(value);
        }
    }
}
var styles = StyleSheet.create({
    container: {
        width: width,
        backgroundColor: '#fff',
        flexDirection: 'row',
        alignItems: 'center',
    },
    left: {
        flex: 4,
    },
    right: {
        flex: 1,
        alignItems: 'flex-end',
    },
    up: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    separatorCol: {
        height: 14,
        width: 0.5,
        marginHorizontal: 5,
        backgroundColor: 'rgba(0,0,0,0.2)',
    },
});
