import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import {Image, Platform, StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';
import CheckButton from './CheckButton';
import I18n from '../../../../globalization/Localize';

const ICON_SIZE = 25; // 当android设置24的时候，图形会挤压形成锯齿

const BACKGROUNDCOLOR = '#4A6EE019';
const UNCHECKED_BACKGROUNDCOLOR = '#F2F3F5';

const UNCHECKED_TITLECOLOR = '#343434';
const CHECKED_TITLECOLOR = '#12AA9C';

const UNCHECKED_SUBTITLECOLOR = '#808080';
const CHECKED_SUBTITLECOLOR = '#12AA9C';

/**
 * @description 带有勾选按钮的设置项
 * @property {string} title - 主标题
 * @property {string} subtitle - 子标题
 * @property {bool} checked 设置项是否勾选
 * @property {style} containerStyle - 列表项的自定义样式
 * @property {style} titleStyle - 主标题的自定义样式
 * @property {style} subtitleStyle - 子标题的自定义样式
 * @property {function} onValueChange - 设置项被勾选的回调函数
 * @property {bool} showChoiceArray - 是否显示4选1选项(专为逗留时长)
 * @property {number[]} selectIndexArray - 子标题的自定义样式
 * @property {function} onSelectValueChange - 设置项被勾选的回调函数
 * @property {function} onPress - 设置项被点击的回调函数
 * @property {bool} hideRightIcon - 隐藏右侧图标
 */
const ChoiceItemNew = (props) => {
    const [checked, setChecked] = useState(props.checked);
    const [selectIndexArray, setSelectIndexArray] = useState(props.selectIndexArray);

    // 替代 componentWillReceiveProps
    useEffect(() => {
        console.log("newProps------", props);
        if (props.checked !== checked) {
            setChecked(props.checked);
        }
    }, [props.checked]);

    useEffect(() => {
        if (props.showChoiceArray && props.selectIndexArray.toString() !== selectIndexArray.toString()) {
            setSelectIndexArray(props.selectIndexArray);
        }
    }, [props.selectIndexArray, props.showChoiceArray]);

    const itemColor = props.backgroundColor ? props.backgroundColor : (checked ? BACKGROUNDCOLOR : UNCHECKED_BACKGROUNDCOLOR);
    const titleColor = props.titleColor ? props.titleColor : (checked ? CHECKED_TITLECOLOR : UNCHECKED_TITLECOLOR);
    const subtitleColor = props.subtitleColor ? props.subtitleColor : (checked ? CHECKED_SUBTITLECOLOR : UNCHECKED_SUBTITLECOLOR);
    const imageSource = checked ? props.selectIcon : props.unselectIcon;

    const handlePress = () => {
        if (props.onlyChecked && checked) {
            return;
        }
        const checkValue = !checked;
        setChecked(checkValue);
        if (props.onValueChange) {
            props.onValueChange(checkValue);
        }

        if (props.showChoiceArray) {
            setSelectIndexArray([0, 0, 1, 0]);
            setChecked(true);
            if (props.onValueChange && props.onSelectValueChange) {
                props.onValueChange(true);
                props.onSelectValueChange(2);
            }
        }
        if (props.onPress) {
            props.onPress();
        }
    };

    const handleCheckButtonChange = (index, value) => {
        if (value) {
            const newArray = [0, 0, 0, 0];
            newArray[index] = 1;
            setSelectIndexArray(newArray);
            setChecked(true);
            if (props.onValueChange && props.onSelectValueChange) {
                props.onValueChange(value);
                props.onSelectValueChange(index);
            }
        } else {
            const array = [...selectIndexArray];
            array[index] = 0;
            if (array.indexOf(1) === -1) {
                setSelectIndexArray([0, 0, 0, 0]);
                setChecked(false);
                if (props.onValueChange) {
                    props.onValueChange(false);
                }
            } else {
                setSelectIndexArray(array);
            }
        }
    };

    return (
        <View>
            <TouchableWithoutFeedback
                disabled={props.disabled}
                onPress={handlePress}
                accessibilityLabel={props.accessibilityLabel ? props.accessibilityLabel : ""}
            >
                <View
                    style={[styles.container, {backgroundColor: itemColor}, props.containerStyle]}>
                    {props.headIcon ?
                        <View style={[styles.headIconView, {flex: 0}]}>
                            <Image style={styles.headIcon} source={props.headIcon}/>
                        </View>
                        : null
                    }
                    <View style={{flex: 8}}>
                        <View style={{paddingVertical: 2}}>
                            <Text
                                numberOfLines={3}
                                style={{
                                    fontSize: 15,
                                    fontWeight: 'bold',
                                    color: titleColor,
                                    fontFamily: Platform.OS === 'android' ? "" : null
                                }}
                            >
                                {props.title}
                            </Text>
                        </View>
                        {props.subtitle ? <Text
                            numberOfLines={6}
                            style={[{fontSize: 12, color: subtitleColor, marginTop: 3, lineHeight: 15}, props.subtitleStyle]}
                        >
                            {props.subtitle}
                        </Text> : null
                        }

                        {props.showChoiceArray && props.checked ?
                            <View style={styles.fourChoice}>
                                <CheckButton 
                                    buttonText={5 + I18n.t('cruise_seconds')}
                                    checked={selectIndexArray[0] === 1}
                                    onlyChecked={true}
                                    buttonStyle={{marginRight: 18, paddingHorizontal: 5, marginTop: 8}}
                                    onValueChange={(value) => {
                                        console.log('value--值', value);
                                        handleCheckButtonChange(0, value);
                                    }}
                                />
                                <CheckButton 
                                    buttonText={10 + I18n.t('cruise_seconds')}
                                    checked={selectIndexArray[1] === 1}
                                    onlyChecked={true}
                                    buttonStyle={{marginRight: 18, paddingHorizontal: 5, marginTop: 8}}
                                    onValueChange={(value) => {
                                        handleCheckButtonChange(1, value);
                                    }}
                                />
                                <CheckButton 
                                    buttonText={15 + I18n.t('cruise_seconds')}
                                    checked={selectIndexArray[2] === 1}
                                    onlyChecked={true}
                                    buttonStyle={{marginRight: 18, paddingHorizontal: 5, marginTop: 8}}
                                    onValueChange={(value) => {
                                        handleCheckButtonChange(2, value);
                                    }}
                                />
                                <CheckButton 
                                    buttonText={20 + I18n.t('cruise_seconds')}
                                    checked={selectIndexArray[3] === 1}
                                    onlyChecked={true}
                                    buttonStyle={{paddingHorizontal: 5, marginTop: 8}}
                                    onValueChange={(value) => {
                                        handleCheckButtonChange(3, value);
                                    }}
                                />
                            </View>
                            : null}
                    </View>

                    <View style={[styles.right, {flex: 0}]}>
                     {props.hideRightIcon?<View style={styles.icon}/>:   <Image style={styles.icon} source={imageSource}/>}
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </View>
    );
};

// PropTypes 定义
ChoiceItemNew.propTypes = {
    title: PropTypes.string.isRequired,
    subtitle: PropTypes.string,
    checked: PropTypes.bool,
    onlyChecked: PropTypes.bool,
    containerStyle: PropTypes.object,
    titleStyle: PropTypes.object,
    subtitleStyle: PropTypes.object,
    showChoiceArray: PropTypes.bool,
    headIcon: PropTypes.any,
    selectIcon: PropTypes.any,
    unselectIcon: PropTypes.any,
    backgroundColor: PropTypes.string,
    titleColor: PropTypes.string,
    subtitleColor: PropTypes.string,
    selectIndexArray: PropTypes.arrayOf(PropTypes.number),
    accessibilityLabel: PropTypes.string,
    onPress: PropTypes.func
};

// 默认属性
ChoiceItemNew.defaultProps = {
    title: '',
    subtitle: '',
    checked: false,
    onlyChecked: false,
    containerStyle: {},
    titleStyle: {},
    subtitleStyle: {},
    showChoiceArray: false,
    selectIcon: require('../../../resources/images/icon_selected.png'),
    unselectIcon: require('../../../resources/images/icon_unselect.png'),
    selectIndexArray: [0, 0, 0, 0],
    accessibilityLabel: ""
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 10,
        paddingVertical: 18,
        paddingHorizontal: 14,
        flexDirection: 'row',
        alignItems: 'center'
    },
    left: {
        flex: 8,
    },
    right: {
        marginLeft: 18,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    fourChoice: {
        flexDirection: 'row',
        flexWrap: 'wrap'
    },
    icon: {
        width: ICON_SIZE,
        height: ICON_SIZE,
    },
    headIconView: {
        flex: 8,
        marginRight: 10,
    },
    headIcon: {
        width: 40,
        height: 40,
    },
});

export default ChoiceItemNew;
