/*
 * 作者：sunhongda
 * 文件：IMIToast.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import Toast from "react-native-root-toast";
import {RRCToast} from "../widgets/overlayer";
const TYPE = {
    TOP: Toast.positions.TOP,
    BOTTOM: Toast.positions.BOTTOM,
    CENTER: Toast.positions.CENTER,
}
Object.freeze(TYPE);

let toast = null;

function isEmpty(obj) {
    if (obj === undefined || obj == null) {
        return true;
    }
    if (Array.isArray(obj) && obj.length === 0) {//数组
        return true;
    } else {
        if (typeof obj === 'string' && obj.trim() === '') {
            return true;
        }//字符串
    }
    return false;
}

export function showToast(content, position = TYPE.CENTER, durationTime = 2000) {
    if (isEmpty(content)) {
        console.log("新版Toast>>>>量产5-12——imitoast------内容为空，不显示")
        return;
    }
    // if (toast) {//隐藏已经存在的toast
    //     console.log("toast------已经存在，不显示")
    //     setTimeout(() => Toast.hide(toast), 0);
    // }
    // console.log("新版Toast>>>>量产5-12——imitoast------直接显示")
    // RRCToast.show(content,Toast.positions.CENTER,durationTime);
    setTimeout(() => {
        toast = Toast.show(content, {
            animation: true,
            position: position
        });
    }, 0);
}

export function hideToast() {
    if (toast) {//隐藏已经存在的toast
        setTimeout(() => Toast.hide(toast), 0);
    }
}


export default class IMIToast {
    static TYPE = TYPE;
    static showToast = showToast;
    static hideToast = hideToast;
}
