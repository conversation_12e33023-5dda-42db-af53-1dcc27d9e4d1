{"name": "imihome_rn", "version": "1.22.19", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@babel/plugin-transform-private-methods": "^7.24.7", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/art": "^1.2.0", "@react-native-community/geolocation": "^3.3.0", "@react-native-community/image-editor": "^4.2.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^9.3.7", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "bcryptjs": "^3.0.2", "buffer": "^6.0.3", "command": "^1.1.1", "crypto-js": "^3.1.9-1", "curve25519-js": "0.0.4", "deprecated-react-native-listview": "^0.0.8", "deprecated-react-native-prop-types": "^4.2.3", "i18n-js": "3.5.1", "image-editor": "^0.2.0", "imilab-rn-sdk": "file:imilab-rn-sdk", "imilab-sdk-test-first": "file:imilab-sdk-test-a", "lodash.memoize": "4.1.2", "metro-react-native-babel-preset": "^0.77.0", "native-base": "^2.13.14", "react": "18.2.0", "react-native": "0.74.3", "react-native-audio": "^4.3.0", "react-native-calendars": "^1.1294.0", "react-native-devsettings": "^1.0.5", "react-native-easy-app": "^1.7.27", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.9.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-linear-gradient": "^2.6.2", "react-native-localize": "^3.2.1", "react-native-message-bar": "^2.1.0", "react-native-modal-translucent": "^5.0.0", "react-native-network-info": "^5.2.1", "react-native-orientation": "^3.1.3", "react-native-reanimated": "^3.14.0", "react-native-root-toast": "^3.4.1", "react-native-router-flux": "^4.3.1", "react-native-safe-area-context": "^4.5.0", "react-native-screens": "^3.33.0", "react-native-slider": "^0.11.0", "react-native-sound": "^0.11.2", "react-native-storage": "^1.0.1", "react-native-svg": "^15.4.0", "react-native-swipeout": "^2.3.6", "react-native-swiper": "^1.6.0", "react-native-ui-kitten": "^4.4.1", "react-native-video": "^5.2.1", "react-native-webview": "^11.26.1", "rmc-date-picker": "^6.0.10", "rn-fetch-blob": "^0.12.0", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "latest", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "jest": {"preset": "react-native"}, "overrides": {"react-native-gesture-handler": "2.15.0", "react-native-reanimated": "^3.14.0"}}