const localeConfig = {
    calendarLocaleConfig: {
        formatAccessibilityLabel: 'dddd d \'of\' MMMM \'of\' yyyy',
        monthNames: [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ],
        monthNamesShort: [
            'jan',
            'feb',
            'mar',
            'apr',
            'may',
            'jun',
            'jul',
            'aug',
            'sep',
            'oct',
            'nov',
            'dec'
        ],
        dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
        dayNamesShort: ['Sun.', 'Mon.', 'Tue.', 'Wed.', 'Thur.', 'Fri.', 'Sat.']
    }
};

export default {
    ...localeConfig,
    Sunday: "Sunday",
    Monday: "Monday",
    Tuesday: "Tuesday",
    Wednesday: "Wednesday",
    Thursday: "Thursday",
    Friday: "Friday",
    Saturday: "Saturday",
    SundayShort: "Sun.",
    MondayShort: "Mon.",
    TuesdayShort: "Tue.",
    WednesdayShort: "Wed.",
    ThursdayShort: "Thur.",
    FridayShort: "Sat.",
    SaturdayShort: "Fri.",
    MonthAndDay: "M/D",
    popo_setting_camera_text: "Camera settings",
    alarmSettingText: "Home surveillance settings",
    playBackText: "Playback",
    cameraTestTipsTitle2: "Network speed test",
    help_callback: "Help and Feedback",
    quality_360: "360P",
    quality_25k_v2: "2.5K",
    sd_360p: "360p",
    hd_1080p: "2k",
    quality_1080: "1080P",
    resolution_sd: "SD",
    resolution_qhd: "QHD",
    quality_1440p: "1440P",
    quality_sd_new: "360P",
    quality_uhd: "2K",
    quality_sd: "SD",
    quality_fhd: "HD",
    quality_common_sd: "480P",
    quality_fhd_3k: "3K HD",
    quality_common_3K: "3K",
    quality_2k: "2K",
    all_events_str: "All events",
    peopleDetectStr: "Human shape event",
    bellEventStr: "Doorbell event",
    detectDistanceModalSubStr033: "When adjusting the sensitivity, the LED will blink orange when motion is detected by PIR.",
    doorkeyAreaDetectAlarmSubStr: "When someone is detected in the key area, the doorbell will give an acousto-optic alarm for 10 seconds.",
    doorbellSetting: "Doorbell settings",
    record_files_sdcard: "Micro SD card",
    gatewaySettingTitle: "Hub settings",
    wifiSettingText: "Wi-Fi settings",
    gateWayLightEffectText: "Hub LEDs description",
    quality_low: "Smooth",
    network_not_connected: "Abnormal network connection, please check the connection and try again",
    device_offline: "Device offline",
    power_off: "Device in sleep mode",
    onlyDoInLive: "Please open the live streaming before the operation",
    imi_speaking_block: "Can’t use during call",
    screen_recording: "This feature cannot be used while recording",
    saved_system_album: "Saved to the phone album",
    action_fail: "Operation failed",
    storage_permission_denied: "No storage permission, please set it manually",
    save_system_album_failed: "Recording time is too short",
    save_album_failed: "Video recording failed",
    commLoadingText: "Loading, please wait...",
    error_code_common_retry: "Tap to retry",
    error_help: "View Help",
    camera_calling: "Can’t use during call",
    call_busy_error: "Call occupied",
    call_busy_tips: "Other device is in a call, please try again later",
    call_connect_error: "Network error, please try again later",
    net_connect_error: "Network error",
    device_offline_aready: "Device is offline,",
    offlineTime: "Offline time:",
    connect_err: "Connection failed",
    connect_error_help_one: "1. Please check whether the network connection of mobile phone and camera is working properly, it is recommended to restart the router and camera.",
    offline_help_tip_reconect: "reconnect",
    dot_for_modal: ".",
    connect_error_help_two: "2. If the WiFi password has been modified or the network has been changed, please ",
    connect_error_help_three: "3. If the above can not be solved, please ",
    connect_error_help_four: "4. If the above does not solve the issue, please",
    offline_help_tip_feed_question: "feedback",
    know_button: "Got it",
    offline_help_tip_one: "1. Please check whether the network connection of the camera is working properly, it is recommended to restart the router and the camera.",
    offline_help_two: "2. If power supply is normal, check the indicator light:",
    offline_help_two_first: "Blue light blinking slowly/quickly: Please check if the camera is connected to the network. If the WLAN name or password has been changed, please",
    offline_help_two_two: "Yellow light blinking quickly: The camera has been reset, please",
    offline_help_tip_bind: "Rebind",
    offline_help_two_three: "Yellow light blinking slowly: The camera is upgrading, please wait until the upgrade is complete before accessing. Do not power off.",
    offline_three1: "3. Please try to keep the device as close as possible to the router and reduce obstacles such as walls. The closer the two, the higher the device's",
    offline_three2: "network RSSI value",
    offline_three3: "theoretically resulting in a stronger network signal. It is recommended that the RSSI value be greater than -50.",
    offline_help_tip_the_first: "Solid yellow: please ",
    offline_help_tip_connect_service: "contact the customer service",
    offline_help_tip_the_thd: " to obtain a software upgrade package.",
    offline_help_tip_the_sec: "Solid blue/blinking blue: please ",
    offline_help_tip_forth: "4. Please check the camera ",
    offline_help_tip_rssi: "network information ",
    offline_help_tip_forth_first: "RSSI value, if the value is lower than -50, please try to move it closer to the router.",
    offline_help_four: "4. If the above can not be solved, please",
    offline_help_five: "5. If the above does not solve the issue, please",
    offline_help_tip_the: "3. Please check the camera indicator:",
    offline_help_tip_fth: "5. If the above can not be solved, please ",
    alarm_turn_on_str: "Please enable home surveillance",
    fees_for_renewal: "Renew",
    toolbar_text_resolution: "Resolution",
    toolbar_text_sound: "Sound",
    toolbar_text_snap: "Snapshot",
    toolbar_text_record: "Record",
    toolbar_text_fullscreen: "Fullscreen",
    select_all: "Select all",
    unselect_all: "Deselect all",
    cancel: "Cancel",
    commWaitText: "Please wait...",
    ok_button: "OK",
    dialog_never_show: "Don't show again",
    cruise_seconds: "s",
    am: "AM",
    pm: "PM",
    year: "Y",
    month: "M",
    day: "D",
    hour: "H",
    minute: "M",
    give_away: "Free",
    cloud_give_price_str: "Value ",
    cloud_give_ok: "Get it",
    picker_start_time: "Start",
    picker_end_time: "End",
    alarm_direction_custom: "Custom time",
    step_preview: "Back",
    account_next: "Next",
    stayDetectStr: "Motion detection",
    video_downloading: "Downloading...  ",
    noAlarmVideo: "No video available",
    cloud_time_out_str: "Smart plan already expired",
    buy_again: "Renewal now",
    cloudExpireShowImageHint: "Purchase cloud storage to enable cloud video backup >",
    network_available_need_check: "Network unavailable. Please check the network settings",
    housekeeping_no_event: "No content yet",
    commLoadingFailText: "Failed to load, try again",
    commLoadingMoreDataText: "Loading more data, please wait...",
    commNoMoreDataText: "All data has been loaded",
    commLoadingClickText: "Tap to refresh",
    abnormal_event_fire: "Your door detects fire",
    abnormal_event_damaged: "Your door has been pried",
    abnormal_event_anti_password: "Anti-hijacking password alert",
    abnormal_event_anti_fingerprint: "Anti-hijacking fingerprint alert",
    abnormal_event_password: "Trying password to unlock alert",
    abnormal_event_fingerprint: "Trying fingerprints to unlock alert",
    abnormal_event_nfc: "Trying NFC to unlock alert",
    abnormal_event_face: "Trying face to unlock alert",
    abnormal_event_multiple: "Trying double validation to unlock alert",
    abnormal_event_door_time_out: "The door is not closed long time",
    abnormal_event: "Abnormal event",
    lingerChangeText: "Person event",
    alarm_change_string: "The detection area has changed",
    doorbellPressTitle: "Someone presses the doorbell",
    downloadingDeleteError: "Downloading, please try it later",
    delete_success: "Delete successfully",
    delete_failed: "Delete failed",
    video_downloaded: "Download completed",
    save_failed: "Save failed",
    delete: "Delete",
    chargeGuide: "Charging prompt",
    category_doorbell: "doorbell",
    category_camera: "Camera",
    chargeGuideSubtitle: "It will take about 5 hours to fully charged the camera with a 5V/2A power adapter.",
    normalCharge: "Charging method 1",
    hubCharging: "Charging method 2",
    hubConnectHintTitle: "In order to have a good signal, it's recommended that:",
    hubConnectHintSubtitle: "1. Try placing the hub higher off the ground. \n2. When the hub is connected via Ethernet, place it about 0.5m away from the router. \n3. Place the hub about 0.5m away from walls. \n4. Avoid placing the hub in corners.",
    button_finish: "Done",
    button_add_device: "Add more devices",
    cameraTestTips2: "If the network environment at the installation location is poor, it may cause connection failures or video glitching issues",
    noSignalText: "No signal",
    perfectText: "Perfect",
    strongText: "Strong",
    goodText: "Good",
    weakText: "Weak",
    cameraTestStrong: "The current location is good for installing %{code}",
    cameraTestGood: "The current location is suitable for installing %{code}",
    cameraTestWeak: "The signal in the current location is weak, please place the %{code} closer to the hub",
    cameraTestNoSignal: "The current location doesn't have signal. Please take the %{code} closer to your hub",
    no_sd_card_go_buy_cloud: "Purchase Cloud Storage",
    downloadTip: "Play the video to be downloaded",
    alarm_download_downloading: "Downloading...",
    storage_no_sdcard_please_buy: "No Micro SD card, please insert or purchase",
    no_sd_card_tips_text: "Video in Cloud will be more secure.",
    cameraTestWifiSpeedTitle: "Installation instructions",
    cameraTestTipsTitle1: "Installation guide",
    cameraTestTips1: "Place the %{code} as close to the hub as possible, Carry your phone and %{code} for signal test before installation.",
    re_play: "Replay",
    doorbellIndicatorLightPageTitle: "Doorbell indicator description",
    doorbellPressLight: "Outside doorbell is ringing",
    doorbellPressSubtitle: "Click on the doorbell key, the doorbell key edge indicator appears running light effect, and you can hear the doorbell ringing",
    cameraOnSubtitle: "Press and hold the sync button for 2 seconds until you see the blue light, then you will hear the power-on music.",
    cameraOffSubtitle: "Press and hold the sync button for 8 seconds until red light blinks once, then you will hear the power-off music.",
    doorbellUsbChargeTitle: "Use USB to charge the doorbell",
    usbChargeSubtitle: "Red blinks three times and then starts charging",
    doorbefullChargeTitle: "Doorbell charging complete (fully charged)",
    fullChargeSubtitle: "Solid green",
    cameraResetSubTitle: "Press and hold the sync button for 5 seconds until the indicator blinks blue , release the button and start resetting. The camera will reboot during the reset process.",
    reset_success_text: "Reset successfully",
    resetSuccessSubtitle: "Blinking blue",
    pairedSuccessTitle: "Paired successfully",
    pairedSuccessSubtitle: "Solid blue for 5 seconds",
    pairedFailTitle: "Pairing failed",
    pairedFailSubtitle: "Blue and orange blinks alternately for 3 times",
    pirSensitivityAdjustSubtitle: "When adjusting PIR sensitivity, the indicator will blink orange once each time PIR detects someone. If someone is detected for a while, the indicator will blink orange continuously.",
    pirGetPeopleTitle: "when camera detects a motion",
    pirGetPeopleSubtitle: "Orange blinks once",
    pirAlwaysGetPeopleTitle: "when camera continuously detects motions",
    pirAlwaysGetPeopleSubtitle: "Orange blinks continuously",
    gateWayLightPageTitle: "Hub LEDs Description",
    light_blue: "Blue indicator",
    light_blue_flash: "Blinking blue",
    light_blue_flash_case: "Network connection is in progress (not connected)",
    light_blue_on: "Solid blue",
    light_blue_on_case: "Network connection is successful (connected)",
    light_orange: "Orange indicator",
    light_orange_flash: "Blinking orange",
    light_orange_flash_case: "Waiting for connection (device and app binding in progress)",
    light_orange_on: "Solid orange",
    light_orange_on_case1: "Booting up",
    light_orange_on_case3: "A firmware update is in progress",
    light_blue_orange_mix_head: "Blue/",
    light_blue_orange_mix_tail: "Orange indicator",
    light_blue_orange_mix_flash: "Blue and orange blinks alternately for 3 times",
    light_blue_orange_mix_flash_case: "Firmware update failed",
    gateway_reset: "Hub reset",
    gateway_reset_hint1: "During the hub binding process, if the indicator light has not shown blinking orange, please reset the hub.",
    gateway_reset_hint2: "Press and hold the reset button on the back of the hub for 5 seconds, until the indicator light changes to solid orange, then release the button. During the reset process, the hub will restart. When the indicator starts blinking orange, the reset is successful and you can start binding.",
    indicatorLightPageTitle: "Camera LEDs Description",
    cameraOnOffLight: "Camera power on/off",
    cameraOnTitle: "Camera power on",
    cameraOffTitle: "Camera power off",
    cameraChargeTitle: "Camera charging",
    usbChargeTitle: "Use USB to charge the camera",
    fullChargeTitle: "Camera is fully charged.",
    sunChargeTitle: "Use the solar panel to charge the camera",
    cameraResetTitle: "Camera reset",
    pirSensitivityAdjustTitle: "Motion detection sensitivity adjustment",
    injectSuccess: "Popped up successfully",
    sdcard_format_success: "Formatted successfully",
    sdcard_format_fail: "Format failed",
    sdCardFormating: "SD card formatting",
    injectFailed: "Failed to pop up",
    noSdCardTitle: "No SD card currently",
    injectSdCardTitle: "Pop up the SD card safely",
    injectSdCardSubtitle: "Pop up the SD card safely to prevent the recorded file from being damaged",
    sdCardFormat: "Format SD card",
    sdCardDamaged: "Memory card error",
    injectSdCardHint: "Confirm to pop up the SD card safely?",
    formatTitle: "Confirm to format SD card?",
    formatMessage: "After formatting, the SD card contents will be emptied",
    stayTimeStr: "Detection Mode",
    houseDetectedPeopleStr: "When someone is detected in front of the house",
    peopleRecordPowerMoreStr: "Record immediately",
    peopleRecordPowerMoreSubStr: "Record once motion detection is triggered.  (high power consumption)",
    peopleRecordPowerCommonStr: "Record after the set time",
    peopleRecordPowerCommonSubStr: "Record only when someone stays for a long time. (general power-consuming)",
    peopleRecordPowerLessStr: "Turn off",
    peopleRecordPowerLessSubStr: "Motion detection is turned off (less power consumption)",
    firstSetStayTimeFailed: "The default settings have been used, please go to [Home Surveillance Settings] to adjust",
    commTitleSettingText: "Settings",
    feature_set: "Function setting",
    comm_setting_title: "General settings",
    device_name: "Device name",
    check_update: "Check for update",
    shareUser_tip: "No permission to share devices",
    shared_setting: "Shared devices",
    confirm_deletion_device: "Confirm to delete the device?",
    preset_sleep_set: "Sleep perspective setting",
    comm_setting_remove_device: "Delete device",
    update_device_name: "Modify device name",
    input_name: "Please enter a name",
    settings_set_success: "Set successfully",
    play_back_text_all: "Playback all videos ",
    wake_up: "Wake up",
    operationFailed: "Failed to set",
    people_event: "Human detection",
    move_event: "Motion detection",
    alarm_loud_switch: "Sound detection",
    no_human_event: "Unattend Detection",
    fence_detect_switch: "Fence detecion",
    cry_event: "Crying detection",
    keyAreaDetectStr: "Activity zone detection",
    moveEvent: "Motion detection",
    peopleEvent: "Human detection",
    soundEvent: "Loud noise detection",
    bottom_house_keeping: "Events",
    str_housekeeping_tip_guide_people: "o Human detected in the surveilled area",
    str_housekeeping_tip_guide_sound: "o Abnormal sound detected",
    str_housekeeping_tip_guide_fence: "o Fence detection in the surveilled area",
    str_housekeeping_tip_guide_nobody: "o No one appeared in the surveilled area",
    str_housekeeping_tip_guide_move: "o Changes detected in the surveilled area",
    str_housekeeping_tip_guide_cry: "o Crying is detected",
    str_housekeeping_tip_guide_important: "o Human detected in key area",
    str_housekeeping_tip_title: "Once home surveillance is enabled, camera will save alert pictures and send notifications to you",
    go_to_open: "Turn it on",
    delete_alert: "Confirm deletion?",
    delete_title_loading: "Deleting...",
    cloudTip: "Activate cloud storage for unlimited storage, providing more event recognition",
    x_flat_list_no_more_data: "All data has been loaded",
    downLoadTitle: "download",
    select_tip: "Select files",
    delete_title: "delete",
    save_success: "Saved successfully",
    buy_cloud_for_info: "Subscribe IMILAB smart plan",
    download_system_album: "Download successfully",
    video_download_fail: "Download failed",
    delete_alert_tip: "This video contains",
    delete_alert_tip1: "event(s), continue deleting?",
    bottom_house_video_keeping: "Video details",
    currentPlay: "Currently playing",
    loadMsgInfo: "Messages",
    loadMsgInfoError: "Loading messages",
    bottom_video_album: "Album",
    select_title_1: "Select",
    album_video_play: "Play",
    album_video_pause: "Pause",
    album_video_mute: "Mute",
    album_video_voice: "Sound",
    album_video_full_screen: "Fullscreen",
    video_download_fail_warning: "Video uploading, please try again later",
    bottom_cloud_storage: "IMI Cloud",
    change_event_str: "Switch event",
    open_setting: "Set",
    no_video_data_new: "No video available",
    playback_no_video_data_tip: "Oops, there is no video today. Try another day",
    date_format_yyyy_mm_dd: "yyyy-MM-dd",
    panoramicSuccess: "Panoramic success",
    panoramicError: "Panoramic picture generate fail, please retry later",
    direction_end_009: "I can't turn anymore",
    isDataUsageTip: "Pausing,you\\'re using mobile data.",
    Panoramic_loading: "Panoramic picture generating……",
    set_onekey_tit: "set one-button alarm now？",
    set_onekey_msg: "After setting, you can press the button to trigger the alarm light and prompt sound.",
    set_onekey_sure: "set now",
    popo_setting_storage_text: "Storage settings",
    sdcard_error_out: "The Micro SD card is ejected,Please reinsert or pull out the Micro SD card.!",
    audio_permission_denied: "No authorization to record audio",
    panoramicing_tip: "Panoramic picture generating……",
    camera_guide_for_zoomed_str: "Double tap/pinch\n to enlarge/shrink the screen",
    camera_guide_for_panoramic_str: "Enter panorama setting",
    expiredHint: "   Your cloud storage package is getting expired today, you will not be able to view or record videos afterwards",
    sdcard_tip_cancel: "OK",
    sd_need_format_no_index_massage: "Format the SD card to record normally, whether to format and empty the SD card now?",
    sd_need_format_has_old_index_massage: "The device software has been upgraded, format the SD card to get better playback experience, whether to format and empty the SD card now?",
    is_the_calibration: "Calibrating...",
    calibration_completed: "Camera calibration successfully",
    netWorkError: "Network connection interrupted",
    operate_time_out: "Operation timeout",
    hotspot_connect_hint: "Please connect to the “imi_xxxxxx” then return",
    goto_wifi: "Select WiFi",
    play_back_text: "Playback",
    sdcard_status7: "Memory card not initialized",
    toolbar_text_sleep: "Sleep",
    quality_auto: "Auto",
    sdcard_format_title_tips: "Please format the SD card!",
    sdcard_format_title_tips_content: "Memory card not initialized, it needs to be formatted before normal use. Formatting will erase all data on the memory card. Do you want to proceed with formatting?",
    upgrade_state_content: "Latest firmware version is detected on the device",
    upgrade_state_content_end: "Upgrade now",
    sleepTipTitle: "Device sleep",
    sleepTipContent: "The camera will stop working and recording video information",
    cloud_will_time_out_str: "Smart plan is about to expire",
    cloud_time_out_effect_str: "Smart plan service will not be available once expired",
    temporarily_not: "Temporarily not",
    formatting_btn: "Formatting",
    storageCardFormating: "Micro SD card is being formatted...",
    waitFailedTip: "Please try again later",
    targetPushTitle_subtitle: "click me to solve",
    targetPush_sdcard_format: "The SD card needs to be initialized before use!",
    sdcard_status_error: "Memory card error",
    get_success: "Obtained Successfully",
    Panoramic_tip: "You can control the camera view by clicking  the panorama",
    Panoramic_title: "Generate panorama",
    Panoramic_title_reset: "Do you want to take the panorama again?",
    storage_services: "Cloud storage service",
    storage_services_content: "What is cloud storage?\n",
    storage_services_title: "Cloud storage privileges",
    imi_cloud_storage_rights_title1: "24x7 Care",
    imi_cloud_storage_rights_detail1: "Video download",
    imi_cloud_storage_rights_title2: "Smart Detection",
    imi_cloud_storage_rights_detail2: "No detail spared",
    imi_cloud_storage_rights_title3: "Cloud storage",
    imi_cloud_storage_rights_detail3: "Playback at anytime",
    imi_cloud_storage_rights_title4: "Encrypted upload",
    imi_cloud_storage_rights_detail4: "Privacy safeguards",
    storage_after: "Later",
    imi_cloud_experience_now: "Experience",
    play_back_tit: "Video playback",
    play_back_change_time: "Switch time",
    alarmText: "Events",
    goto_live_view: "Live",
    str_housekeeping_tip_value: "o people detected in the surveillance area\no screen changes detected in the surveillance area\no Abnormal sound detected",
    common_error: "Failed to load, check device status",
    sdCardName: "SD card",
    no_video_data_failed: "Failed to retrieve video file",
    retry_connect: "Reconnect",
    collapse: "Collapse",
    noData: "No data, please reselect",
    playback_no_event: "No event",
    keyArea: "Activity zone",
    change_date: "Switch date",
    todayTitle: "Today",
    preDayTitle: "Yesterday",
    sdcard_status0: "Good",
    sdcard_status1: "No Micro SD card",
    sdcard_status6: "Memory card full",
    sdcard_status3: "Error",
    sdcard_status4: "Formatting",
    sdcard_status5: "Eject the memory card",
    sdcard_status9: "Fixing",
    sdcard_status10: "Ejecting",
    sdcard_status2: "Not enough storage",
    sdcard_status_normal_new1: "Memory card status",
    play_back_text_all_title: "All videos",
    max_download_limit: "Only one download supported",
    delete_failed_limit: "Up to 50 operations at a time",
    delete_connect_failed: "Device connection disconnected",
    delete_failed_inPlay: "Videos in playback cannot be deleted",
    sensitivity_for_high_tit: "High sensitivity",
    sensitivity_for_low_tit: "Low sensitivity",
    alarm_sensitivity: "Alert sensitivity",
    sensitivity_for_high_subtit: "Detects motion from people or objects,not a single movement will be missed",
    sensitivity_for_low_subtit: "Alarm when big sounds are detected",
    date_picker_time_title: "Please select the start and end time",
    alarm_time_set_time: "Select surveillance period",
    time_equal: "End time cannot be equal to start time",
    alarm_time_night: "Nighttime surveillance",
    alarm_time_all: "24-hour surveillance",
    alarm_time_day: "Daytime surveillance",
    alarm_time_set: "Monitoring time",
    full_color_vision_title: "Color night vision",
    alarm_time_24: "24 hours",
    alarm_time_info: "Alert when motion or human is detected",
    alarm_time_208: "20:00 - 08:00 next day",
    fullColor_smart_tit: "Intelligent night vision",
    alarm_time_820: "8:00-20:00",
    noDIYTimeTip: "Please select a custom time",
    voice_for_wu: "NO",
    voice_for_warning: "Alarm sound",
    voice_for_dingdong: "Doorbell sound",
    voice_for_welcome: "Hello,welcome",
    voice_for_area: "You have entered the monitoring area",
    voice_for_closedoor: "Close the door after you, please. Thank you",
    voice_for_safe: "Please be safe",
    voice_for_stairs: "Up and down the stairs,please pay attention to safety",
    voice_for_dangerArea: "Dangerous area,please leave",
    allDay_time: "24-hour surveillance",
    potlight_flash: "Light flashing",
    potlight_not_bright: "No light",
    potlight_long_bright: "Light on",
    day_time: "Daytime surveillance",
    night_time: "Nighttime surveillance",
    voice_for_custom: "Custom",
    soundLightAlarm: "Acousto-optic alarm",
    only_people_detected: "Only detect human",
    effective_time: "Select a surveillance period",
    tip_voice_selected: "Select prompt sound",
    potlight_alarm_mode: "Alarm lamp settings",
    potlight_contain_time: "Time of duration",
    click_warning_tit: "One-button alarm",
    local_device_connect_wifi: "Select WiFi",
    comm_setting_faq: "FAQ",
    angelMoreSetting: "More settings >>",
    voice_for_enter_name: "Please enter a name",
    max_device_name: "Name can contain up to 15 characters",
    customer_phone: "Phone Number：************",
    customer_email: "Email: <EMAIL>",
    customer_wx: "WeChat Official Accounts：创米数联",
    customer_web: "Website: www.imilabglobal.com",
    voice_for_add: "Add one",
    voice_for_edit: "Edit",
    imi_save: "Save",
    voice_for_name: "Name",
    imi_input_text_tip: "This character isn't supported",
    voice_for_tip_tit_time: "Alarm sound selection",
    voice_for_re_record: "Rerecord",
    voice_for_click_record: "Hold and Talk",
    nursingTimeSetting: "Effective time setting ",
    show_upgrade_app: "Please upgrade the app to the latest version",
    imi_fence_tit: "When an object/human crosses the fence line in the direction of the arrow, it is regarded as a fence detection event.",
    imi_switch_dection: "Arrow reversing",
    alarm_event_tit: "Home surveillance event",
    move_track: "Motion tracking",
    settings_alarm_human_track_title: "Human tracking",
    no_human: "Unattend Detection",
    alarm_event_manager: "Home surveillance management",
    alarmTimeSetting: "Interval of pushing notifications",
    time_minutes: "mins",
    settings_alarm_push_time_sub: "If you feel there is interference, please increase the time interval",
    message_push_manager: "Event notification management",
    message_push_phone: "Push to your phone, when motion is detected.",
    tip_time_minute: "minute",
    recordType: "Recording mode",
    recordType1: "Image",
    recordType2: "Video",
    recordTypeSub: "Activate cloud storage to switch to video mode",
    alarm_info_set: "Alarm type settings",
    alarm_sound_detection: "Loud noise detection",
    detection_sensitivity: "Detection sensitivity",
    alarm_info_push: "Alarm message push",
    message_push_switch: "Alarm message push switch",
    preset_opened: "Opened",
    preset_closed: "Close",
    audio_broadcast: "Voice reminder",
    black_white_vision_title: "Black and white night vision",
    settings_light_title: "Status light",
    Data_usage_warning: "Flow protection",
    data_usage_warning_intro: "Video pauses when using mobile data",
    intelligent_cruise: "AI Cruise",
    settings_switch_off: "OFF",
    cruise_all_view: "Panorama",
    cruise_favorite: "Preset",
    sleep_set: "Sleep settings",
    setttings_infared: "Night vision settings",
    settings_flip_title: "Rotate image",
    settings_flip_subtitle: "Please enable this option when camera is upside down",
    imageSetting: "Image settings",
    setting_picture_setting: "More image settings",
    imi_camera_correct_pos: "Camera Calibration",
    setting_reset: "Restart device",
    calibration_to_continue_30: "Camera calibration will take about 30 seconds to complete. Continue?",
    calibration_to_continue: "Camera calibration will take about 25 seconds to complete. Continue?",
    calibration_failure: "Camera calibration failed",
    setting_reset_msg: "Restarting the device will require some time. Confirm to restart?",
    settings_watermark_title: "Watermark",
    pictureCorrection: "Lens distortion correction",
    pictureCorrectionSubtitle: "Once turned on, the distortion will be reduced, but the visual range is narrowed.",
    wdrMode: "Wide Dynamic Range (WDR) Mode",
    audio_broadcast_switch: "Voice reminder",
    rn_version: "Plug-in version",
    network_info: "Network info",
    wifi_signal_0: "New WiFi has no signal",
    wifi_signal_2: "Device network status is poor",
    wifi_signal_5: "Device network status is good",
    wifi_name: "WiFi name",
    wifi_strength: "WiFi strength",
    wifi_rssi: "RSSI",
    wifi_loss: "Packet loss rate",
    wifi_mode: "Current mode",
    wifi_mode_type2: "LAN connection",
    wifi_mode_type1: "WAN connection",
    wifi_ip_address: "IP address",
    wifi_mac_address: "MAC address",
    settings_light_full_color_title: "Low light full color",
    settings_light_full_color_subtitle: "When the ambient light is low, color images can still be seen",
    fullColor_title3: "Switch automatically",
    fullColor_subTit: "LED light at night, 24 hours of color image monitoring",
    fullColor_title2: "On",
    fullColor_black_subTit: "infrared image will be used when there is not enough light",
    fullColor_title1: "Off",
    fullColor_smart_subTit: "Night infrared image, but switch to full color night vision when human is detected",
    empty_start_end_tips: "Please select a start time and an end time",
    add_time_period: "Add time period",
    not_set: "To be set",
    plug_timer_repeat: "Repeat",
    do_once: "Run once",
    do_everyday: "Daily",
    do_weekday: "Weekdays",
    do_weekend: "Weekend",
    do_custom: "Custom",
    nobody_detect_name: "Span name",
    date_picker_time_hint: "Start time cannot be equal to or later than end date",
    nobody_time_no_more_than_10: "Time periods cannot exceed 10",
    nobody_push_warning: "* Be sure to turn on the push function of imilab Home. No one appears during this period and push messages to the mobile phone.",
    delete_time_warning: "Delete this time period?",
    save_the_open: "Save and enable",
    presetAngleSleep: "Sleep perspective ",
    Preset_Sleep_subTitle: "Device sleeps at this preset location",
    preset_sleep_set_holder: "There is no  sleep perspective at present, \nclick the PTZ to start setting",
    save_current_angle_sleep: "Save and enable current sleep perspective?",
    action_success: "Operation succeeded",
    Preset_Sleep: "Preset Sleep",
    closeStr: "Off",
    settings_switch_on: "ON",
    voice_for_max: "Support up to 5 custom audio",
    alarm_only_people_detected: "Human detection",
    settings_alarm_push: "Motion Detection notification",
    alarm_loud_push: "Sound detection push",
    fence_detect_push: "Fence detecion",
    alarm_crying_push: "Crying detection notification",
    setting_record_model_always: "Always Record",
    setting_record_model_close: "Disable recording",
    storageCardHinting: "Memory card ejecting",
    sd_suspended: "Pause",
    sdcard_status_normal_new: "Normal",
    sdcard_status_more_new: "Memory space remained",
    sd_storage_switch: "Micro SD card storage",
    setting_record_model: "Recording mode",
    record_quality: "Record resolution",
    storageCardFormat: "Format Micro SD card",
    sd_card_use_hint_062: "Memory card usage tips\n1. Support Class 4 or higher speed memory card\n2. The memory card format is FAT323. Be sure to use genuine memory cards, compatibility is not guaranteed for copycat, inferior and refurbished cards\n4. The maximum memory card capacity supports 256G",
    sdcard_tip1_new: "1.Eject the Micro SD card before removing it.",
    sdcard_tip2_new: "2.Videos on the Micro SD card are not encrypted in order to view local videos easily, please safely store your Micro SD card.",
    sdcard_out_already: "Micro SD card is unmounted",
    sdcard_status_abnormal: "Abnormal",
    sdcard_status8: "Not enough space, please change card",
    setting_record_model_always_title: "Keep all recordings stored in the memory card",
    setting_record_model_close_title: "Camera won't record and save videos to memory card after you end recording",
    storageCardHint: "Confirm to pop up the SD card safely?",
    setting_record_model_move: "Only record when motion is detected",
    sdcard_status_more: "Memory space remained",
    sdcard_exit: "Unmount Micro SD card",
    sdcard_tip1: "1.Please eject the SD card before unplugging it",
    sdcard_tip2: "2.Videos on the Micro SD card are not encrypted in order to view local videos easily, please safely store your Micro SD card.",
    setting_record_model_move_title: "To save storage, videos will be record only when motion is detected",
    more_store_setting: "Storage management",
    settings_sdcard_title: "Memory card status",
    sleep_title: "Sleep",
    Preset_Sleep_subTitle2: "Device performs the Preset Sleep",
    timer_sleep_title: "Sleeping schedule",
    upload_grade_success: "Upgraded successfully",
    list_item_curr_version: "Current version",
    list_item_latest_version_now: "The current version is the latest",
    getError: "Failed to retrieve, please try again later",
    upgrade_button: "Upgrade now",
    list_item_version_status_4: "Installing",
    list_item_latest_version_uploading_title: "Updating…",
    list_item_latest_version_uploading: "Updating , do not turn off or use it before the update is completed",
    list_item_latest_version_upload_finish: "After the update, the device will restart",
    upload_grade_timeout: "Upgrade timeout",
    upload_grade_error: "Upgrade failed",
    list_item_latest_version: "Latest version",
    light_blue_orange_mix_flash_success: "OTA upgraded successfully",
    list_item_latest_version_log: "Update log",
    comm_config_wifi_title: "Select working WIFI for the device",
    wifiSettingTipsText: "The device only supports 2.4GHz Wi-Fi connections. Only English characters and numbers can be used in WIFI name",
    comm_config_wifi_ssid_hint1: "Wi-Fi not set now",
    comm_config_wifi_password_hint: "Enter Wi-Fi password",
    local_device_bind_wifi_failed: "The device is bound failed，please reboot the device and retry later",
    local_device_bind_wifi_success: "The device is bound successfully, and the plugin is about to exit",
    local_device_bind_wifi_binding: "Binding device...",
    wdrFunctionHint: "The dim and overexposed parts of the video can retain more details when WDR mode is on.",
    wdr_before: "WDR off",
    wdr_after: "WDR on",
    wdrHint: "Note: \n1.WDR does not work in B&W night vision mode. \n2. WDR will affect watermark coloring.",
    sdCardRemain: "Used: %{code}GB",
    sdCardTotal: "Total: %{code}GB",
    sdCardLeft: "It is expected to record in a loop for %{code} days.",
    onPlayErrorText: "Live stream error (%{code})",
    onPlayErrorMaxText: "The device has exceeded the maximum connection limit (%{code}).",
    expiredCountdownHint: "Your cloud storage package will expire in %{code} days, and you will not be able to view or record videos after it expires",
    on_live_play_error: "Failed to open camera (%{code}), please try again later!",
    select_title_3: "%{code} items selected",
    click_too_fast: "Tapped too often",
    privacy_area_protection: 'Privacy Area Protection',
    area_privacy_edit: 'Area Edit',
    algorithm_desc: 'Algorithm Description',
    ai_key_area_desc: 'When privacy area protection is enabled, the picture in the selected area will be hidden to protect user privacy.',
    ai_key_area_attention: 'Note: After enabling privacy area protection, other AI detection functions will not work properly in the privacy area.',
    privacy_area_preview: 'Privacy Area Preview',
    private_open_msg: 'After enabling privacy area protection, functions such as human detection, motion detection, and virtual fence may be affected.',
    tips: 'Tips',
    drag_to_adjust_area: 'Drag to adjust privacy area range',
    privacy_area_style: 'Privacy Area Style',
    area_privacy_style_lovely: 'Cute Style 1',
    area_privacy_style_lovely2: 'Cute Style 2',
    area_privacy_style_pure: 'Simple Style 1',
    area_privacy_style_pure2: 'Simple Style 2',
    save_privacy_area: 'Save Privacy Area',
    confirm_save_privacy_area: 'Confirm to save the current privacy area settings?',
    no_changes_to_save: 'No changes to save',
    camera_volume:'Camera external volume adjustment',
    humanoid_detection:'human detection',
    virtual_Fence:'virtual fence',
    privacy_area_protection:'Privacy Zone Protection',
    area_privacy_edit:'Area editing',
    algorithm_desc:'Algorithm Description',
    ai_key_area_desc:'After enabling privacy zone protection, the screens within the selected area will be hidden to protect user privacy',
    ai_key_area_attention:'Attention: After enabling privacy zone protection, other AI detection functions will not work properly within the privacy zone.',
    privacy_area_preview:'Privacy Zone Preview',
    private_open_msg:'After enabling privacy zone protection, functions such as humanoid detection, motion detection, and virtual fencing may be affected.',
    drag_to_adjust_area:'Drag and drop to adjust the privacy area range',
    privacy_area_style:'Privacy Area Style',
    area_privacy_style_lovely:'Cute Style 1',
    area_privacy_style_lovely2:'Cute Style 2',
    area_privacy_style_pure:'Minimalist Style 1',
    area_privacy_style_pure2:'Minimalist Style 2',
    save_privacy_area:'Save privacy zone',
    confirm_save_privacy_area:'Are you sure to save the current privacy zone settings?',
    no_changes_to_save:'No modifications need to be saved',
    family_protection: "Family protection",
    family_protection_desc: "After turning on the switch, if no one moves during the set time period, a message will be pushed to the phone at the end of the time period",
    detect_nobody_in_morning: "No one appeared in the morning",
    nobody_have_lunch: "No one is home at noon",
    detect_nobody_in_day: "No one is at home during the day",
    plug_timer_onetime: "Execute once",
    plug_timer_everyday: "every day",
    setting_monitor_next_day: "the next day",
    delete_files: "delete",
    c_set_success: "Setup successful",
    c_set_fail: "Setup failed",
    c_get_fail: "Failed to obtain",
    idm_empty_tv_device_tips: "No item selected",
    edit: "edit",
    cruise_control:'Automatic Cruise',
    enter_Name:'input name',
    cruise_time_period:'Cruise time period',
    common_angles:'Common angles',
    all_day:'all day',
    custom_time_period:'Customize time period',
    custom_repeat:'Custom repetition',
    plug_timer_sef_define:'customize',
    picker_start_time_1:'Select start time',
    picker_end_time_1:'Select end time',
    workday:'weekday',
    weekend:'weekend',   
    every_10_minutes:'Every 10 minutes',
    every_30_minutes:'Every 30 minutes',
    every_60_minutes:'Every 1 hour',
    every_120_minutes:'Every 2 hours',
    every_720_minutes:'Every 24 hours',
    every_360_degrees:'360 ° Cruise',
    every_360_degrees_subtitle:'360 ° all-round cruising',
    fixed_point_cruise:'Fixed-point cruise',
    fixed_point_cruise_subtitle:'Regularly observe the position and take turns cruising',
    every_minutes :'Cruise every {{minute}} minutes',
    every_hours_every_minutes :'Cruise every {{hour}} {{minute}} hours',
    cruise_time:'Cruise time',
    cruise_frequency:'Cruise frequency',
    cruise_mode:'Cruise mode',
    custom_cruise_frequency:'Customize cruise frequency',
    add_favorite:'Add frequently viewed locations',
    empty_not_support: 'Cannot be empty',
    name_already_exists: "The name already exists",
    that_position_exists:'Repeat addition',
    go_add:'Go add it',
    common_angles_less_than_2: 'Common angles less than 2, unable to initiate fixed-point cruise control',
    setting_in_progress: 'Please wait for the settings',
    rename: 'Rename',
    add_common_angle: 'Add common angles',
    cruise_min_interval: 'The minimum cruising interval is 5 minutes',
    limit_reached_v6: 'The maximum limit of 6 has been reached. Please delete it before adding again',
    add_to_favorites: 'Add as frequently viewed',
    add_ing:'Adding in ..',
    del_ing:'Deleting ..',
    add_error:'Failed to add',
    cruise_ing:'Cruising in progress'
};
