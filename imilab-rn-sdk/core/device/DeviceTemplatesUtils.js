import {Category} from "../../../config/configConstant";

import {
    configCameraProjectDefault,
    configProject as configCameraProject
} from "../../../config/camera/configProject";

import {configProject as configGateWayProject} from "../../../config/gateway/configProject";
import {configProject as configDoorbellProject} from "../../../config/doorbell/configProject";
import {configProject as configWearProject} from "../../../config/wear/configProject";
import {configProject as configDoorDefault} from "../../../config/door/configProject";
import {configProject as configZigbeeDefault} from "../../../config/zigbee/configProject";

import {RN_ROUTE_PAGE_TAG} from "../../../config/configConstant";

/**
 *  读取设备配置模板 工具类 解析设备参数信息操作
 */
export default class DeviceTemplatesUtils {

    /**
     * 读取获得工程配置文件内容
     * @returns {PropertyPreview[] | Array<ObjectMethod | ObjectProperty | SpreadElement> | Array<RestElement | ObjectProperty> | Array<ObjectTypeProperty | ObjectTypeSpreadProperty> | Array<ObjectProperty | ObjectMethod | SpreadElement> | Object | *|Array}
     * @param LetDevice 设备
     */
    static parseProjectJson(LetDevice) {
        //写死
        let configProject = this.getConfigProject('camera', 'a1FKrifIRwH');
        if (!configProject?.hasOwnProperty('category')) {
            console.error(" category can not be null !");
            return [];
        }
        //读取不支持的数组
        return configProject?.notSupportMethod.properties;
    }

    static parseDevicePropertiesJson(configProject) {
        if (!configProject.hasOwnProperty('method')) {
            console.error(" method can not be null !")
            return [];
        }
        console.log('parseDevicePropertiesJson' + configProject);
        //读取属性数组
        return configProject.method.property;
    }

    /**
     * 获取 Components 名称 获取不到则默认首页
     * @param LetDevice
     * @param pageTag
     * @returns {*}
     */
    static parseDeviceComponentsName(LetDevice, pageTag) {
        let configProject = this.getConfigProject('camera', 'a1FKrifIRwH');
        let components = configProject.components[pageTag];
        console.log("jsahfhdsjdsdsffhd",components,pageTag);
        
        if (!components){
            components = configProject.components[RN_ROUTE_PAGE_TAG.launcher];
        }
        return components;
    }


    static parseDeviceStorageJson(LetDevice) {
        console.log('parseDeviceStorageJson   -----------------LetDevice ----' + LetDevice.category);
        let configProject = this.getConfigProject('camera', 'a1FKrifIRwH');

        console.log('parseDeviceStorageJson   ---------------------' + configProject);

        return configProject?.storage;
    }
    static getConfigProject(category, model) {
        if(!this.configProject) {
            console.log("getConfigProject category : " + category,",model = ",model);
            if (category === Category.Camera) {
                 
                this.configProject = configCameraProject;
            } else if (category === Category.GateWay) {
                this.configProject = configGateWayProject;
            } else if (category === Category.doorbell) {
                this.configProject = configDoorbellProject;
            } else if (category === Category.wear) {
                this.configProject = configWearProject;
            } else if (category === Category.Door) {
                this.configProject = configDoorDefault;
            } else if (category === Category.ZigBee) {
                this.configProject = configZigbeeDefault;
            } else {
                //default 先随便给一个
                this.configProject = configCameraProjectDefault;
            }
            console.log("getConfigProject configProject" + JSON.stringify(this.configProject));
        }
        
        return this.configProject[model];
    }
}
