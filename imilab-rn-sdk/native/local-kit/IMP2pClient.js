import {NativeEventEmitter, NativeModules, requireNativeComponent} from 'react-native';

const NativeP2pClient = NativeModules.AKRCTP2pClient;
//const NativeP2pClient = requireNativeComponent('AKRCTP2pClient');
const P2pClientEmitter = new NativeEventEmitter(NativeP2pClient);

export default class IMP2pClient {
  /**
   * 连接
   */
  // static connect() {
  //   return NativeP2pClient.connect();
  // }
  /**
   * 连接
   */
  // static disconnect() {
  //   return NativeP2pClient.disconnect();
  // }
  /**
   * 获取通道连接状态
   * @param callBack Callback 返回字符串
   *  0,//正在连接
   *  1,//已打开
   *  2,//正在关闭
   *  3;//已关闭
   */
  static getChannelState(callback) {
    return NativeP2pClient.getChannelState(callback);
  }
  /**
   *   p2p文件操作，该接口必须在p2p通道建立成功才可使用，即onP2pSendState方法的回调状态
   *
   *   @param cmdType [AKFileOperation]
   *  * 0：拉取索引文件
   *  * 1：获取一个一分钟视频
   *  * 2、删除一个一分钟视频
   *  * 3、拉取图片
   *  @cameraId 摄像头id
   *   @param timeStamps  查询回看数据列表文件传listOf(String) 其他操作需要传时间戳String集合
   */
  static operationFile(cameraId, cmdType, timeStamps, picLoc, operateTimeStamps) {
    console.log('NativeP2pClient.operationFile', NativeP2pClient.operationFile);

    return NativeP2pClient.operationFile(cameraId, cmdType, timeStamps , picLoc || [], operateTimeStamps || '0');
  }
  /**
   * 发送p2p消息，该接口必须在p2p通道建立成功才可使用，即onP2pSendState方法的回调状态
   * @param args String
   */
  static sendP2pData(cmdType, msgData) {
     
    return NativeP2pClient.sendP2pData(cmdType, msgData);
  }

  /**
   *开启手机音频录制
   * @param args String
   */
  // static startAudioRecord() {
  //   return NativeP2pClient.startAudioRecord();
  // }

  /**
   *关闭手机音频录制
   * @param args String
   */
  // static stopAudioRecord() {
  //   return NativeP2pClient.stopAudioRecord();
  // }
  /**
   * 设备p2p发送通道状态
   */
  static onP2pSendStateAddListener(callback) {
    return P2pClientEmitter.addListener('onP2pSendState', e => callback(e));
  }
  /**
   * * 设备文件传输
   */
  static onFileOperateAddListener(callback) {
    // console.log(22322323323, P2pClientEmitter.addListener);

    // onKcpDataRes
    return P2pClientEmitter.addListener('onKcpDataRes', e => {
      // console.log('zhixingle', callback);
      callback(e);
    });
  }
  /**
   * p2p发送数据
   */
  static onSendDataAddListener(callback) {
    return P2pClientEmitter.addListener('onSendData', e => callback(e));
  }

  // static downloadPlaybackFile(fileData, savePath, fileState) {
  //   return NativeP2pClient.downloadPlaybackFile(fileData, savePath, fileState ? fileState : '0');
  // }

  static downloadPlaybackImage(fileData, savePath, fileState) {
    return NativeP2pClient.downloadPlaybackImage(fileData, savePath, fileState ? fileState : '0');
  }

  static downloadPlaybackVideo(fileData, savePath, fileState) {
    return NativeP2pClient.downloadPlaybackVideo(fileData, savePath, fileState ? fileState : '0');
  }

  static checkPlaybackFile(pathList) {
    return NativeP2pClient.checkPlaybackFile(pathList);
  }
}
