import {DeviceEventEmitter, NativeModules, NativeEventEmitter, Platform} from 'react-native';
import DeviceTemplatesUtils from '../../core/device/DeviceTemplatesUtils';
import {LetIProperties} from './IMIProperties';
import {isObj} from '../../utils/TypeUtils';
import IMIPackage from '../local-kit/IMIPackage';
import { showToast } from '../../../imilab-design-ui';
import { stringsTo } from '../../../globalization/Localize';

// const rtcDevice = NativeModules.IMIDevice;
if (!NativeModules.AKRCTDevice) {
  console.error('NativeModules.AKRCTDevice is null');
} else {
  console.log('NativeModules.AKRCTDevice is not null');
}
const rtcDevice = NativeModules.AKRCTDevice;
const rtcDeviceEmitter = new NativeEventEmitter(rtcDevice);

export const DEV_INFO_CALLBACK = {
  /**
   *设备信息变化监听器 {@link DEVICE_INFO_KEY}
   */
  infoListener: 'infoListener',
  /**
   * 注册一个设备事件、属性变化监听器
   */
  // deviceEventChangeListener: 'deviceEventChangeListener',
};
Object.freeze(DEV_INFO_CALLBACK);

export const DEVICE_INFO_KEY = {
  /**
   *设备昵称 如果没有设置过,默认返回 产品名称
   */
  NickName: 'nickName',
  /**
   *离线状态发生变化
   */
  Online: 'isOnline',

  /**
   *房间发生变化
   */
  RoomName: 'roomName',

  /**
   *是否是本地直连模式
   */
  IsLocalDevice: 'isLocalDevice',
};
Object.freeze(DEVICE_INFO_KEY);

export const ERROR_CODE = {
  /**
   *设备不在线
   * android-> 错误 Error: {"code":9201,"id":"aad5bf11-e5a0-4f05-ad5a-0e9dca118555","localizedMsg":"设备不在线","message":"device offline"}
   * ios->
   */
  DEV_OFFLINE: 9201,
};
Object.freeze(DEV_INFO_CALLBACK);

export const PROPERTY_CALLBACK = {
  /**
   *动作变更
   */
  actionListener: 'actionListener',
  /**
   *事件变更
   */
  evetnListener: 'evetnListener',
  /**
   * 属性变化监听器
   */
  onPropertiesChange: 'propertyListener',
};
Object.freeze(PROPERTY_CALLBACK);

/**
 * @Author: byh
 * @Date: 2024/8/17
 * @explanation:
 * 设备：属性、方法
 * AKRCTDevice（原有的IMIDevice，原有的IMIProperties接口及event已迁移至此）
 *********************************************************/
export default class IMIDevice {
  constructor() {
    /* 初始默认值  **/
    this._nickName = rtcDevice.customName;
    this._isOnline = rtcDevice.isOnline;
    this._offlineTime = rtcDevice.offlineTime;
    this._roomName = rtcDevice.roomName;
    this._isLocalDevice = rtcDevice.isLocalDevice;

    //注册刷新letDevice 内属性
    this.registerInfoChangeListener(data => {
      // data结构 {value:true,key:isOnline}
      let {key, value} = data;
      if (key === IMIDevice.NickName) {
        this._nickName = value;
      } else if (key === IMIDevice.Online) {
        this._isOnline = value;
      } else if (key === IMIDevice.IsLocalDevice) {
        this._isLocalDevice = value;
      }
      console.log(
        `_doDevInfoText setState BaseDeviceComponent: ${data} + data ${JSON.stringify(data)}` +
          '  LetDevice.devNickName ' +
          LetDevice.devNickName,
      );
    });
  }

  set devNickName(value) {
    this._nickName = value;
  }

  /**
   * RN修改设备名称刷新使用
   * @param value
   */
  setDevNickName(value) {
    // console.log(` enter devNickName():  value`,value);
    this._nickName = value;
  }

  setRoomName(value) {
    this._roomName = value;
  }

  static getInstance() {
    if (!this.instance) {
      this.instance = new IMIDevice();
    }
    return this.instance;
  }

  /**
   * 初始化当前设备
   * @param project 工程相关配置文件
   * @param devConfig 设备相关配置文件
   */
  init(project) {
    parseProjectJson = DeviceTemplatesUtils.parseProjectJson(this);
  }

  /**
   * 设备展示名称 设备底层默认如果不存在设备昵称则自动返回产品默认名称
   * @returns {*}
   */
  get devNickName() {
    // console.log(` enter deviceID():   ${rtcDevice.nickName}`);
    return this._nickName;
  }

  /**
   *获取设备 id，每一个真实设备都有一个唯一的 id
   * @return {string}
   * @readonly
   *
   */
  get deviceID() {
    // console.log(` enter deviceID():   ${rtcDevice.did}`);
    // return rtcDevice.did;

    return rtcDevice.iotId;
  }

  // 获取激活时间
  get latestCreateTime() {
    // console.log(` enter deviceID():   ${rtcDevice.did}`);
    // return rtcDevice.did;

    return rtcDevice.latestCreateTime;
  }
  /**
   * 获取设备所属房间名称
   * @return {string}
   * @readonly
   *
   */
  get roomName() {
    return this._roomName;
  }

  /**
   * 获取设备的 model,设备类型的唯一标识
   * @return {string}
   * @readonly
   *
   */
  get model() {
    // console.log(` enter model():   ${rtcDevice.model}`);
    return rtcDevice.productId;
  }

  /**
   * 获取设备在线状态
   * @return {boolean}
   * @readonly
   *
   */
  get isOnline() {
    // console.log(` enter isOnline():   ${rtcDevice.isOnline}`);
    return this._isOnline;
  }


  setIsOnline(value) {
    // console.log(` enter devNickName():  value`,value);
    this._isOnline = value;
  }

  get offlineTime() {
    return this._offlineTime
  }

  setOfflineTime(value) {
    // console.log(` enter devNickName():  value`,value);
    this._offlineTime = value;
  }

  /**
   * 是否是本地直连模式
   * @return {boolean}
   * @readonly
   */
  get isLocalDevice() {
    // console.log(` enter isLocalDevice():   ${rtcDevice.isLocalDevice}`);
    return this._isLocalDevice;
  }

  /**
   * 获取设备名称
   * @return {string}
   * @readonly
   */
  get deviceName() {
    // console.log(` enter isLocalDevice():   ${rtcDevice.isLocalDevice}`);
    return rtcDevice.customName;
  }

  /**
   * 获取设备分享状态
   * @return {boolean}
   * @readonly
   *
   */
  get isShareUser() {
    // console.log(` enter isShareUser():   ${rtcDevice.isShareUser}`);
    return rtcDevice.isShareUser;
  }

  /***
     *   获取设备产品类型
     *   export const Category = {
     *      /!*** 摄像机产品类型标识 ***!/
        Camera: "Camera",
        /!*** 网关产品类型标识 ***!/
        GateWay: "GateWay",
    };***/
  get category() {
    // console.log(` enter category():   ${rtcDevice.category}`);
    return rtcDevice.category;
  }

  /**
   * 判断当前是否是阿里设备
   * @return {string}
   * @readonly
   *
   */
  get isAL() {
    return rtcDevice.isAL;
  }

  /**
   * ========================================================================================
   * ===================================== 方 法 =============================================
   * ========================================================================================
   */

  /**
   * 注册一个属性变化监听器  (具有唯一性 会被其他调用者覆盖)
   * @param callback
   */
  registerPropertyChangeListener(callback) {
    console.log(' registerPropertyChangeListener   ');
    this.propEventEmitter = rtcDeviceEmitter.addListener(PROPERTY_CALLBACK.onPropertiesChange, event => {
      callback(event);
    });
  }

  /**
   * 注册一个属性变化监听器 (此方式不会被其他调用者冲掉)
   * @param callback
   */
  addPropertyChangeListener(callback) {
    console.log(' registerPropertyChangeListener   ');
    return rtcDeviceEmitter.addListener(PROPERTY_CALLBACK.onPropertiesChange, event => {
      callback(event);
    });
  }

  /**
   * 移除掉属性变化监听器
   */
  removePropertyChangeListener() {
    if (this.propEventEmitter) {
      this.propEventEmitter.remove();
    }
  }

  /**
   * 获取设备所有属性
   */
  getAllProperties(isSyn, iotId) {
    return rtcDevice.getAllProperties(isSyn, iotId);
  }
  /**
   * @Author: byh
   * @Date: 2024/8/17
   * @explanation:
   *     public func sendAction(iotId: NSString, thingId: NSNumber, cmd: NSString, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock)
   *********************************************************/
  sendAction(isSyn, iotId, thingId, paramJson) {
    return rtcDevice.sendAction(isSyn, iotId, thingId, paramJson);
  }

  setProperties(isSyn, iotId, thingId, valuesJson,noToast=false) {
    return rtcDevice.setProperties(isSyn, iotId, thingId, valuesJson).then(resp => {
     if(!noToast){
 showToast(stringsTo('settings_set_success'))
     }
     
      return resp;
   });
  }

  /**
   * 设备直连模式发送指令(属性、服务)给设备
   * @param identifier 物模型参数
   * @param cmd 直连设备TCP请求命令id，获取物模型、设置物模型均为272
   * @param reqMode get、set、service
   * @param paramsMap 请求参数
   * @param promise
   */
  sendLocalDeviceCommand(identifier, cmd, reqMode, paramsMap) {
    return rtcDevice.sendLocalDeviceCommand(identifier, cmd, reqMode, paramsMap);
  }

  /**
   * 访问设备服务请求
   * @param identifier 请求标识
   * @param {Object|String }  params
   * @param {Object|String }  params
   * @param {Object }  isRespObj 返回格式是否返回对象 默认=false
   *  @return  Promise   -> resolve :{String}  reject:{"code":-1,""message":"fail"}
   *  由于ios返回数据是{data:{...}} android是{...} 过做一次兼容返回{data:{...},...}
   */
  sendDeviceServerRequest(identifier, params, isRespObj = false) {
    if (isObj(params)) {
      params = JSON.stringify(params);
    }
    console.log('sendDeviceServerRequest' + 'identifier ' + identifier + ' params' + params);

    // @param {String }  params  参数map
    return rtcDevice.sendDeviceServerRequest(identifier, params).then(resp => {
      let respJson = JSON.parse(resp);
      if (respJson.data) {
        respJson = {data: respJson.data, ...respJson.data};
      } else {
        respJson = {data: respJson, ...respJson};
      }
      if (isRespObj) {
        return respJson;
      } else {
        return JSON.stringify(respJson);
      }
    });
  }

  /**
   * 主动获取房间名称信息
   * 从sdk10010开始支持
   * @param did 设备id
   * @param isRespObj 返回数据类型  true返回对象  false返回string
   * @return {*} 设备信息
   */
  getDeviceRoomNameAsync(did, isRespObj = false) {
    return rtcDevice.getDeviceRoomNameAsync(did).then(resp => {
      if (isRespObj) {
        return JSON.parse(resp);
      }
      return resp;
    });
  }

  sendDeviceServerRequestWithDid(did, identifier, params, isRespObj = false) {
    if (isObj(params)) {
      params = JSON.stringify(params);
    }
    console.log('sendDeviceServerRequestWithDid: ', identifier, ' params:', params);

    // @param {String }  params  参数map
    return rtcDevice.sendDeviceServerRequestWithDid(did, identifier, params).then(resp => {
      console.log('sendDeviceServerRequestWithDid onSuccess: ', resp);
      let respJson = JSON.parse(resp);
      if (respJson.data) {
        respJson = {data: respJson.data, ...respJson.data};
      } else {
        respJson = {data: respJson, ...respJson};
      }
      if (isRespObj) {
        return respJson;
      } else {
        return JSON.stringify(respJson);
      }
    });
  }

  /**
   * 设置设备属性 {-- 使用设备中提供的设置属性可以 进行本地属性的变化监听-- }
   * @param {String}params 参数
   */
  setPropertyCloud(params) {
    return rtcDevice.setPropertyCloud(this.deviceID, params);
  }

  /**
   *获取设备属性 {-- 使用设备中提供的设置属性可以 进行本地属性的变化监听-- }
   * @param key 属性名字
   */
  getPropertyCloud(key) {
    return LetIProperties.getPropertyCloud(this.deviceID, key);
  }

  getSingleProperty(key) {
    return LetIProperties.synGetProperty(this.deviceID, key);
  }
  /**
   * ios兼容处理，10005版本加入，以前版本ios值返回与Android不一致
   * 获取设备属性 {-- 使用设备中提供的设置属性可以 进行本地属性的变化监听-- }
   * 需要对sdk的版本进行兼容处理，小于10005使用老的方法，大于使用新的方法
   * @param key 属性名字
   */
  getAssignedPropertyCloud(key) {
    return LetIProperties.getPropertyCloud(this.deviceID, key);
  }

  /**
   * 更新全部设备属性
   */
  updateAllPropertyCloud() {
    return LetIProperties.updateAllPropertyCloud(this.deviceID);
  }

  /**
   * 注册一个属性变化监听器 iOS已经处理
   * @param callback
   */
  registerInfoChangeListener(callback) {
    // onInfoChange改为infoListener
    this.propEventEmitter = rtcDeviceEmitter.addListener(DEV_INFO_CALLBACK.infoListener, event => {
      callback(event);
    });
  }

  /**
   * 注册一个属性变化监听器  (此方式不会被其他调用者冲掉)
   * @param callback
   */
  addInfoChangeListener(callback) {
    console.log(' registerInfoChangeListener  ');
    return rtcDeviceEmitter.addListener(DEV_INFO_CALLBACK.infoListener, event => {
      callback(event);
    });
  }

  /**
   * 移除掉属性变化监听器
   */
  removeInfoChangeListener() {
    if (this.propEventEmitter) {
      this.propEventEmitter.remove();
    }
  }

  /**
   * 注册一个设备事件、属性变化监听器  (具有唯一性 会被其他调用者覆盖)
   * @param callback
   */
  registerDeviceEventChangeListener(callback) {
    this.deviceEventEmitter = rtcDeviceEmitter.addListener(DEV_INFO_CALLBACK.deviceEventChangeListener, event =>
      callback(event),
    );
  }

  /**
   * 注册一个设备事件、属性变化监听器  (此方式不会被其他调用者冲掉)
   * @param callback
   */
  addDeviceEventChangeListener(callback) {
    return rtcDeviceEmitter.addListener(DEV_INFO_CALLBACK.deviceEventChangeListener, event => callback(event));
  }

  /**
   * 移除掉设备事件、属性变化监听器
   */
  removeDeviceEventChangeListener() {
    if (this.deviceEventEmitter) {
      this.deviceEventEmitter.remove();
    }
  }

  /**
   * 判断是否支持某个功能
   * @param method 某个功能的
   * @link{../../config/configProject}
   */
  isSupportMethod(method) {
    console.log('isSupportMethod :  method -> ' + method + 'parseProjectJson:->  ' + parseProjectJson);
    return !parseProjectJson.includes(method);
  }

  /**
   * 设置  Off 命令
   * @private
   */
  propertyOff(method) {
    return LetIProperties.propertyOff(this.deviceID, method);
  }

  /**
   * 设置 On 命令
   * @private
   */
  propertyOn(method) {
    return LetIProperties.propertyOn(this.deviceID, method);
  }
}
IMIDevice.NickName = DEVICE_INFO_KEY.NickName;
IMIDevice.Online = DEVICE_INFO_KEY.Online;
IMIDevice.IsLocalDevice = DEVICE_INFO_KEY.IsLocalDevice;

export let parseProjectJson;

/**
 * @export 导出Device
 */

// export const LetDevice = new IMIDevice();
export const LetDevice = IMIDevice.getInstance();
