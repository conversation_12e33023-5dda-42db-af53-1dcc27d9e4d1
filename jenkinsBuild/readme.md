新项目配置jenkins自动化打包上传云端（可参考031camera）
1.在jenkinsBuild下建立一个项目代号的文件夹(例:031camera)
2.在jenkinsBuild下建立一个index.js，内部放入该项目RN入口代码
3.在jenkinsBuild下建立project.json，云端RNtar包里需要的配置文件
4.把第一步的文件名(例:031camera)给严敏,由严敏在http://120.92.112.45:8080/view/IMI-APP/job/IMI_APP_RN/configure中配置
5.jenkins平台上IMI-APP里IMI_APP_RN构建时PROJECT_NAME选择第一步的文件夹名(例:031camera)

此脚本神奇之处可本地执行，在imihome_rn目录执行:
sh jenkinsBuild/jenkinsBuild.sh [第一步的文件夹名] 
                                [是否上传云端true/false] 
                                [是否上传国服true/false] 
                                [是否上传美服true/false]
                                [是否上传新加坡服true/false]
                                [是否上传欧服true/false]
                                [是否上传测试服国服true/false]
                                [是否上传测试服美服true/false]
                                [是否上传测试服新加坡服true/false]
                                [是否上传测试服欧服true/false]
案例:
上传测试服
sh jenkinsBuild/jenkinsBuild.sh 031camera true false false false false true true true true
上传正式服
sh jenkinsBuild/jenkinsBuild.sh 031camera true true true true true false false false false
本地打包不上传
sh jenkinsBuild/jenkinsBuild.sh 031camera false

