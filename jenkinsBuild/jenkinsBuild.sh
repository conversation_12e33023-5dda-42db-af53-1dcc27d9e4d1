export PATH=$PATH:/usr/local/bin
#控制台可以访问python，但这个脚本里面不能访问，需要单独引入个别名或者直接使用python3 -c
# alias python='/usr/bin/python3'
uploadFun() {
    echo "==> uploadFun"
    echo "==> uploadFun $1 $2 $3"
    deployType=$1 filePath=$2 isForceUpgrade=$3 node uploadFile.mjs
}

PACKAGE_ROOT_PATH=$(pwd)
echo PACKAGE_ROOT_PATH:$PACKAGE_NAME

PACKAGE_NAME=$1
echo PACKAGE_NAME:$PACKAGE_NAME

PROJECT_UPLOAD=$2
echo PROJECT_UPLOAD:$PROJECT_UPLOAD

PROJECT_Type=$3
echo PROJECT_UPLOAD:$PROJECT_Type
PROJECT_isForceUpgrade=$4
echo PROJECT_isForceUpgrade:$PROJECT_isForceUpgrade


PACKAGE_NAME_PATH=jenkinsBuild/$PACKAGE_NAME
echo PACKAGE_NAME_PATH:$PACKAGE_NAME_PATH

if [ -e "$PACKAGE_NAME_PATH/project.json" ]; then
  echo "==> find project.json"
else
  echo "==> no project.json"
  exit 1
fi

if [ -e "$PACKAGE_NAME_PATH/index.js" ]; then
  echo "==> find index.js"
else
  echo "==> no index.js"
  exit 1
fi

#python2 print是个关键字  python3 print是个函数
#PACKAGE_TAG=$(cat $PACKAGE_NAME_PATH/project.json | python -c "import json; import sys; obj=json.load(sys.stdin); print obj['tag'].encode('utf-8')")
PACKAGE_TAG=$(cat $PACKAGE_NAME_PATH/project.json | python -c "import json; import sys; obj=json.load(sys.stdin); print(obj['tag'])")
#PACKAGE_TAG="a1kWMNz9FH1"
#060
#PACKAGE_TAG="a1o26TMbbCU"
#PACKAGE_TAG="a1eHjsGDM0K"
#040
#PACKAGE_TAG="a1XvULslRkB"

#test
#PACKAGE_TAG="a1tl2FZfn7t"
echo PACKAGE_TAG:$PACKAGE_TAG

PACKAGE_TAG_PATH=$PACKAGE_NAME_PATH/$PACKAGE_TAG
echo PACKAGE_TAG_PATH:$PACKAGE_TAG_PATH

#rm -rf index.js
yarn install
echo "==> yarn install over"

if [ -e "node_modules/react-native" ]; then
  echo "==> find node_modules/react-native"
else
  echo "==> no node_modules/react-native"
  exit 1
fi

cp -af $PACKAGE_NAME_PATH/index.js index.js
echo "==> copy index.js over"

rm -rf $PACKAGE_TAG_PATH
mkdir -p $PACKAGE_TAG_PATH
cp -af $PACKAGE_NAME_PATH/project.json $PACKAGE_TAG_PATH/project.json
echo "==> copy project.json over"

mkdir -p $PACKAGE_TAG_PATH/IOS
pwd
echo "==> start build ios"
react-native bundle --platform ios --entry-file index.js --bundle-output $PACKAGE_TAG_PATH/IOS/main.jsbundle --assets-dest $PACKAGE_TAG_PATH/IOS --dev false
./node_modules/react-native/sdks/hermesc/win64-bin/hermesc -emit-binary -out $PACKAGE_TAG_PATH/IOS/main.jsbundle.hbc  -output-source-map  $PACKAGE_TAG_PATH/IOS/main.jsbundle
# ./node_modules/react-native/sdks/hermesc/osx-bin/hermesc -emit-binary -out $PACKAGE_TAG_PATH/IOS/main.jsbundle.hbc  -output-source-map  $PACKAGE_TAG_PATH/IOS/main.jsbundle
# ./node_modules/react-native/sdks/hermesc/linux64-bin/hermesc -emit-binary -out $PACKAGE_TAG_PATH/IOS/main.jsbundle.hbc  -output-source-map  $PACKAGE_TAG_PATH/IOS/main.jsbundle
rm -rf $PACKAGE_TAG_PATH/IOS/main.jsbundle
rm -rf $PACKAGE_TAG_PATH/IOS/main.jsbundle.hbc.map
mv $PACKAGE_TAG_PATH/IOS/main.jsbundle.hbc $PACKAGE_TAG_PATH/IOS/main.jsbundle
echo "==> end build ios"
if [ -e "$PACKAGE_TAG_PATH/IOS/main.jsbundle" ]; then
  echo "==> find $PACKAGE_TAG_PATH/IOS/main.jsbundle"
else
  echo "==> no $PACKAGE_TAG_PATH/IOS/main.jsbundle"
  exit 1
fi
cd $PACKAGE_TAG_PATH
pwd
zip -r IOS.zip IOS
echo "==> zip IOS.zip over"
if [ -e "IOS.zip" ]; then
  echo "==> find IOS.zip"
else
  echo "==> no IOS.zip"
  exit 1
fi
cd $PACKAGE_ROOT_PATH
pwd
rm -rf $PACKAGE_TAG_PATH/IOS
echo "==> remove IOS over"

mkdir -p $PACKAGE_TAG_PATH/Android
echo "==> start build Android"
react-native bundle --platform android --entry-file index.js --bundle-output $PACKAGE_TAG_PATH/Android/index.android.bundle --assets-dest $PACKAGE_TAG_PATH/Android --dev false
./node_modules/react-native/sdks/hermesc/win64-bin/hermesc -emit-binary -out $PACKAGE_TAG_PATH/Android/index.android.bundle.hbc  -output-source-map  $PACKAGE_TAG_PATH/Android/index.android.bundle
# ./node_modules/react-native/sdks/hermesc/osx-bin/hermesc -emit-binary -out $PACKAGE_TAG_PATH/Android/index.android.bundle.hbc  -output-source-map  $PACKAGE_TAG_PATH/Android/index.android.bundle
# ./node_modules/react-native/sdks/hermesc/linux64-bin/hermesc -emit-binary -out $PACKAGE_TAG_PATH/Android/index.android.bundle.hbc  -output-source-map  $PACKAGE_TAG_PATH/Android/index.android.bundle
rm -rf $PACKAGE_TAG_PATH/Android/index.android.bundle
rm -rf $PACKAGE_TAG_PATH/Android/index.android.bundle.hbc.map
mv $PACKAGE_TAG_PATH/Android/index.android.bundle.hbc $PACKAGE_TAG_PATH/Android/index.android.bundle
echo "==> end build Android"
if [ -e "$PACKAGE_TAG_PATH/Android/index.android.bundle" ]; then
  echo "==> find $PACKAGE_TAG_PATH/Android/index.android.bundle"
else
  echo "==> no $PACKAGE_TAG_PATH/Android/index.android.bundle"
  exit 1
fi
cd $PACKAGE_TAG_PATH
pwd
zip -r Android.zip Android
echo "==> zip Android.zip over"
if [ -e "Android.zip" ]; then
  echo "==> find Android.zip"
else
  echo "==> no Android.zip"
  exit 1
fi
cd $PACKAGE_ROOT_PATH
pwd
rm -rf $PACKAGE_TAG_PATH/Android
echo "==> remove Android over"

cd $PACKAGE_NAME_PATH
if [ -e "$PACKAGE_TAG.tar.gz" ]; then
  echo "==> find $PACKAGE_TAG.tar.gz will rm"
  rm -rf $PACKAGE_TAG.tar.gz
else
  echo "==> no $PACKAGE_TAG.tar.gz will tar"
fi
tar -zcvf $PACKAGE_TAG.tar.gz $PACKAGE_TAG
echo "==> tar $PACKAGE_TAG.tar.gz over"
if [ -e "$PACKAGE_TAG.tar.gz" ]; then
  echo "==> find $PACKAGE_TAG.tar.gz"
else
  echo "==> no $PACKAGE_TAG.tar.gz"
  exit 1
fi
rm -rf $PACKAGE_TAG
echo "==> remove TAG over"
ls

if [ $PROJECT_UPLOAD == true ]; then
  filePath=$(pwd)/$PACKAGE_TAG.tar.gz
  echo "==> need upload $filePath"
  cd ..
  uploadFun $PROJECT_Type $filePath $PROJECT_isForceUpgrade
else
  echo "==> no need upload"
fi

cd $PACKAGE_ROOT_PATH
pwd

echo "==> build end"
