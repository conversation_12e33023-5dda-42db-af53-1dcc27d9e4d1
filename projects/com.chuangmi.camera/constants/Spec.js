/**
 * 摄像机控制
 */
export const CAMERA_CONTROL = {
  // 属性
  POWER: {PIID: '10001', NAME: 'power'},
  IMAGE_ROLLOVER: {PIID: '10002', NAME: 'image-rollover'},
  NIGHT_SHOT: {PIID: '10003', NAME: 'night-shot'},
  TIME_WATERMARK: {PIID: '10004', NAME: 'time-watermark'},
  GLIMMER_FULL_COLOR: {PIID: '10005', NAME: 'glimmer-full-color'},
  IMAGE_DISTORTION_CORRECTION: {PIID: '10006', NAME: 'image-distortion-correction'},
  WDR_MODE: {PIID: '10007', NAME: 'wdr-mode'},
  PRESET_SLEEP: {PIID: '10008', NAME: 'preset-sleep'},
  LED_SWITCH: {PIID: '10009', NAME: 'led-switch'},
  TIMEZONE: {PIID: '10011', NAME: 'timezone'},
  TRACK_SWITCH: {PIID: '10027', NAME: 'track-switch'},
  FAV_AREA: {PIID: '10028', NAME: 'fav-area'},
  ACTIVE_FAV_AREA: {PIID: '10029', NAME: 'active-fav-area'},
  SPEAKER_VOLUME: {PIID: '10030', NAME: 'speaker-volume'},
  SPEAKER_MUTE: {PIID: '10031', NAME: 'speaker-mute'},
  CRUISE_SWITCH: {PIID: '10032', NAME: 'cruise-switch'},
  CRUISE_CYCLE: {PIID: '10033', NAME: 'cruise-cycle'},
  CRUISE_START_TIME: {PIID: '10034', NAME: 'cruise-start-time'},
  CRUISE_END_TIME: {PIID: '10035', NAME: 'cruise-end-time'},
  CRUISE_REPETITION: {PIID: '10036', NAME: 'cruise-repetition'},
  CRUISE_MODE: {PIID: '10037', NAME: 'cruise-mode'},
  CRUISE_POSITION: {PIID: '10038', NAME: 'cruise-position'},
  VIRTUAL_FENCE_SWITCH: {PIID: '100000', NAME: 'virtual-fence-switch'},
  VIRTUAL_FENCE_AREA: {PIID: '100001', NAME: 'virtual-fence-area'},
  PEOPLE_FENCE_IN: {PIID: '100002', NAME: 'people-fence-in'},
  PEOPLE_FENCE_OUT: {PIID: '100003', NAME: 'people-fence-out'},
  VIRTUAL_FENCE_DISPLAY_SWITCH: {PIID: '100004', NAME: 'virtual-fence-display-switch'},
  FAMILY_GUARD_CONFIG: {PIID: '100005', NAME: 'family-guard-config'},
  PRIVATE_AREA_SWITCH: {PIID: '100006', NAME: 'private-area-switch'},
  PRIVATE_AREA_PARAM: {PIID: '100007', NAME: 'private-area-param'},
  DETECTION_AREA_SWITCH: {PIID: '100008', NAME: 'detection-area-switch'},
  DETECTION_AREA_DISPLAY_SWITCH: {PIID: '100009', NAME: 'detection-area-display-switch'},
  DETECTION_AREA_ATTR: {PIID: '100010', NAME: 'detection-area-attr'},
  AUTO_SOUND_AND_LIGHT_WARNING: {PIID: '100011', NAME: 'auto-sound-and-light-warning'},
  MANUAL_SOUND_AND_LIGHT_WARNING: {PIID: '100012', NAME: 'manual-sound-and-light-warning'},
  // 动作
  REBOOT: {AIID: '10010', NAME: 'reboot'},
  // 事件
  CRUISE_STARTED: {EIID: '10039', NAME: 'cruise-started'},
  CRUISE_ENDED: {EIID: '10040', NAME: 'cruise-ended'},
};

/**
 * 白光灯
 */
export const WHITE_LIGHT = {
  // 属性
  ON: {PIID: '100013', NAME: 'on'},
};

/**
 * 声光报警
 */
export const SOUND_AND_LIGHT_WARNING = {
  // 属性
  WARNING_REPETITION: {PIID: '100014', NAME: 'warning-repetition'},
  ENABLE_TIME_PERIOD: {PIID: '100015', NAME: 'enable-time-period'},
  WARNING_LIGHT_SWITCH: {PIID: '100016', NAME: 'warning-light-switch'},
  WARNING_AUDIO_SWITCH: {PIID: '100017', NAME: 'warning-audio-switch'},
  WARNING_ALARM_CONFIG: {PIID: '100018', NAME: 'warning-alarm-config'},
  LIGHT_WARNING_MODE: {
    PIID: '100019',
    NAME: 'light-warning-mode',
    ENUM: {ALWAYS_LIGHT: 1, FAST_FLICKER: 2, SLOW_FLICKER: 3},
  },
  SOUND_TEST: {PIID: '100020', NAME: 'sound-test'},
  TRIGGER_EVENT: {PIID: '100021', NAME: 'trigger-event'},
  // 动作
  WARNING_SOUND_PLAY: {AIID: '100022', NAME: 'warning-sound-play'},
};
