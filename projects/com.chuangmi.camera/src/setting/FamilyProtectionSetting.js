import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import Switch from '../../../../imilab-design-ui/src/widgets/settingUI/Switch';
import {LetDevice} from '../../../../imilab-rn-sdk';
import {showToast, MessageDialog} from '../../../../imilab-design-ui';
import {CAMERA_CONTROL} from "../../constants/Spec";

const MAX_ITEM_NUM = 8;
const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;
const TAG = "FamilyProtectionSetting";

export default class FamilyProtectionSetting extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      listDatas: [],
      editMode: false,
      showBarTitle: false,
      isAllSelect: false,
      showDelDialog: false
    };
    this.didFocusListener = this.props.navigation.addListener(
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.keys = [];
    this.LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks";
    this.selectCount = 0;
    this.deleteArray = [];
    this.deleteAlarmKeys = {};
    this.titleHeight = 38;
  }

  renderTitleBar() {
    let titleStr = this.state.editMode ? stringsTo('edit') : stringsTo('family_protection');

    // 构建右侧按钮
    let rightButtons = [];
    if (this.state.listDatas && this.state.listDatas.length > 0) {
      if (this.state.editMode) {
        // 编辑模式下显示全选/取消全选
        rightButtons.push({
          key: NavigationBar.ICON.CUSTOM,
          n_source: this.state.isAllSelect
            ? require('../../resources/images/selected_all_n_light.png')
            : require('../../resources/images/select_all_n_light.png'),
          p_source: this.state.isAllSelect
            ? require('../../resources/images/selected_all_p_light.png')
            : require('../../resources/images/select_all_p_light.png'),
          onPress: () => {
            if (this.selectCount >= this.state.listDatas.length) {
              this.selectCount = 0;
              this.setState({
                listDatas: this.state.listDatas.map((item, _index) => {
                  return {...item, select: false};
                }),
                isAllSelect: false
              });
            } else {
              this.selectCount = this.state.listDatas.length;
              this.setState({
                listDatas: this.state.listDatas.map((item, _index) => {
                  return {...item, select: true};
                }),
                isAllSelect: true
              });
            }
          }
        });
      } else {
        // 正常模式下显示编辑按钮
        rightButtons.push({
          key: NavigationBar.ICON.CUSTOM,
          n_source: require('../../resources/images/houseKeepingV2/icon_angel_edit.png'),
          onPress: () => {
            this.selectCount = 0;
            this.setState({editMode: true, isAllSelect: false});
          }
        });
      }
    }

    return (
      <NavigationBar
        title={this.state.showBarTitle ? titleStr : " "}
        left={[
          {
            key: this.state.editMode ? NavigationBar.ICON.CUSTOM : NavigationBar.ICON.BACK,
            n_source: this.state.editMode ? require('../../resources/images/close.png') : undefined,
            onPress: () => {
              if (this.state.editMode) {
                this.state.listDatas.forEach((item) => item.select = false);
                this.selectCount = 0;
                this.setState({editMode: false, isAllSelect: false});
              } else {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              }
            }
          }
        ]}
        right={rightButtons}
      />
    );
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.loadData();
  }

  onItemLongClick(item) {
    item.select = true;
    this.selectCount = 1;
    this.setState({editMode: true, isAllSelect: this.state.listDatas.length === 1});
  }

  renderItemView(item, index) {
    return (
      <View style={{display: "flex", flexDirection: "column"}}>
        <View style={{display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20}}>
          <TouchableOpacity
            style={{display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%"}}
            onLongPress={() => this.onItemLongClick(item)}
            onPress={() => {
              if (this.state.editMode) {
                item.select = !item.select;
                item.select ? this.selectCount++ : this.selectCount--;
                this.setState({isAllSelect: this.state.listDatas.length === this.selectCount});
              } else {
                this.props.navigation.navigate('FamilyProtectionTimeSetting',
                  {
                    item: JSON.parse(JSON.stringify(item)),
                    callback: (data) => {
                      Object.assign(item, data);
                      this.onItemCheckChanged();
                      this.forceUpdate();
                    }
                  });
              }
            }}>
            <Text style={{fontSize: 16, color: '#000000', fontWeight: 'bold'}}>{item.name}</Text>
            <Text style={{
              fontSize: 13,
              color: 'rgba(0,0,0,0.6)',
              marginTop: 3
            }}>{this.getDurationText(item.start, item.end)} | {this.getRepeatString(item.repeat)}</Text>
          </TouchableOpacity>

          {!this.state.editMode && (
            <View style={styles.switchContainer}>
              <Switch
                value={item.enable}
                onValueChange={(checked) => {
                  console.log(`${TAG} 开关操作 - 项目"${item.name}"状态变更: ${item.enable} -> ${checked}`);
                  console.log(`${TAG} 开关操作 - 时间段: ${item.start}-${item.end}`);
                  item.enable = checked;
                  this.onItemCheckChanged();
                  this.forceUpdate(); // 强制更新UI
                }}
              />
            </View>
          )}

          {this.state.editMode && (
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                onPress={() => {
                  item.select = !item.select;
                  item.select ? this.selectCount++ : this.selectCount--;
                  this.setState({isAllSelect: this.state.listDatas.length === this.selectCount});
                }}
              >
                <Image
                  style={styles.checkboxIcon}
                  source={item.select
                    ? require('../../../../imilab-design-ui/resources/images/icon_selected.png')
                    : require('../../../../imilab-design-ui/resources/images/icon_unselect.png')
                  }
                />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }

  getDurationText(start, end) {
    let text = `${start}-${end}`;
    let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
    let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
    if (startValue > endValue) {
      text = `${start}-${stringsTo('setting_monitor_next_day')}${end}`;
    }
    return text;
  }

  getRepeatString(repeat) {
    if (repeat === 0) {
      return stringsTo('plug_timer_onetime');
    } else if (repeat === 0b01111111) {
      return stringsTo('plug_timer_everyday');
    } else {
      const weekDays = [
        stringsTo('Sunday'),
        stringsTo('Monday'),
        stringsTo('Tuesday'),
        stringsTo('Wednesday'),
        stringsTo('Thursday'),
        stringsTo('Friday'),
        stringsTo('Saturday')
      ];

      let selectedDays = [];
      let flag = 0b00000001;
      for (let i = 0; i < 7; i++) {
        if ((repeat & flag) !== 0) {
          selectedDays.push(weekDays[i]);
        }
        flag = flag << 1;
      }

      const result = selectedDays.join(' ');
      console.log(`${TAG} 重复显示 - repeat:${repeat} 结果:${result}`);
      return result;
    }
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.setState({showBarTitle: true});
    } else {
      this.showTitle = false;
      this.setState({showBarTitle: false});
    }
  };

  render() {
    let titleStr = this.state.editMode ? stringsTo('edit') : stringsTo('family_protection');
    return (<View style={styles.container}>
      {this.renderTitleBar()}
      <ScrollView scrollEventThrottle={16} onScroll={this.scrollViewScroll}
                  style={{width: "100%", backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF'}}
                  contentContainerStyle={{flexGrow: 1}}>

        <View style={{backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF'}}>
          <View style={{flexDirection: "row", flexWrap: "wrap"}} key={0}
                onLayout={({nativeEvent: {layout: {height}}}) => {
                  this.titleHeight = height - 28;
                }}>
            <Text style={styles.titleText}>
              {titleStr}
            </Text>
          </View>
          {
            !this.state.editMode ?
              <View style={{alignItems: "center", marginHorizontal: 24, marginTop: 0}}>
                <Image style={{width: "100%", height: viewHeight, borderRadius: 9}}
                       source={require("../../resources/images/ai_pic_area.webp")}
                       resizeMode="cover" />
              </View> : null
          }

          {
            !this.state.editMode ?
              <Text
                style={[styles.desc_subtitle, {marginTop: 20}]}>{stringsTo('family_protection_desc')}</Text> : null
          }

          {
            (!this.state.editMode && this.state.listDatas.length > 0) ?
              <View style={[styles.whiteblank, {marginTop: 38}]}/> : null
          }

          <FlatList
            style={{width: "100%", marginBottom: 100}}
            data={this.state.listDatas}
            renderItem={(data) => this.renderItemView(data.item, data.index)}
            contentContainerStyle={[{flexGrow: 1, paddingHorizontal: 12}]}
            refreshing={this.state.isLoading}>
          </FlatList>
        </View>
      </ScrollView>
      {this.state.editMode ?
        <View style={styles.editModeBottomView}>
          <TouchableOpacity
            style={styles.deleteButtonContainer}
            onPress={() => {
              this.onDeleteSelected();
            }}>
            <Image
              style={{width: 25, height: 25}}
              source={require("../../resources/images/icon_delete_white.png")}
            />
            <Text style={{color: "#000000", fontSize: 11}}>
              删除
            </Text>
          </TouchableOpacity>
        </View>
        :
        <TouchableOpacity
          style={{position: 'absolute', right: 20, bottom: 30}}
          disabled={this.state.listDatas.length >= MAX_ITEM_NUM}
          onPress={() => {
            this.onAddItem();
          }}>
          <View style={styles.addButton}>
            <Text style={styles.addButtonText}>+</Text>
          </View>
        </TouchableOpacity>
      }
      {this.renderDeleteDialog()}
    </View>);
  }

  onDeleteSelected() {
    console.log(`${TAG} === 开始删除选中的家人守护项 ===`);

    this.deleteArray = [];
    this.deleteAlarmKeys = {};
    let needDelete = false;
    let selectedCount = 0;
    let remainingCount = 0;

    this.state.listDatas.forEach((item, index) => {
      if (item.select == false) {
        // 保留的项目
        if (item.name) {
          this.deleteAlarmKeys[`${this.LONG_TIME_KEY_PREFIX}${item.clock_idx}`] = item.name;
        }
        this.deleteArray.push({
          enable: item.enable,
          clock_idx: item.clock_idx,
          start: item.start,
          end: item.end,
          repeat: item.repeat,
          name: item.name
        });
        remainingCount++;
        console.log(`${TAG} 删除操作 - 保留项目${remainingCount}: 名称="${item.name}", 时间="${item.start}-${item.end}"`);
      } else {
        // 要删除的项目
        needDelete = true;
        selectedCount++;
        console.log(`${TAG} 删除操作 - 删除项目${selectedCount}: 名称="${item.name}", 时间="${item.start}-${item.end}"`);
      }
    });

    console.log(`${TAG} 删除操作 - 总计: 删除${selectedCount}项, 保留${remainingCount}项`);

    if (needDelete == false) {
      console.log(`${TAG} 删除操作 - 没有选中要删除的项目`);
      showToast(stringsTo('idm_empty_tv_device_tips'));
      return;
    }
    console.log(`${TAG} 删除操作 - 显示删除确认对话框`);
    this.setState({showDelDialog: true});
  }

  onDeleteItem() {
    console.log(`${TAG} === 执行删除家人守护项操作 ===`);

    let arrayStr = JSON.stringify({idx: 2, clock: this.deleteArray});
    console.log(`${TAG} 删除操作 - 删除后剩余数据:`, arrayStr);
    console.log(`${TAG} 删除操作 - 剩余项目数量: ${this.deleteArray.length}`);

    this.putLongTimeAlarmList(arrayStr).then(() => {
      console.log(`${TAG} === 删除家人守护项成功 ===`);
      console.log(`${TAG} 删除操作 - 重置所有编辑状态`);
      this.setState({editMode: false, showDelDialog: false, isAllSelect: false});
      this.selectCount = 0;
      this.loadData();
    }).catch((err) => {
      console.error(`${TAG} 删除操作 - 删除失败:`, JSON.stringify(err));
      showToast(stringsTo("c_set_fail"));
      this.setState({showDelDialog: false});
    });
  }

  renderDeleteDialog() {
    return (
      <MessageDialog
        visible={this.state.showDelDialog}
        title="删除定时"
        message="要删除选中的定时吗？"
        messageStyle={{textAlign: 'center'}}
        canDismiss={true}
        onDismiss={() => this.setState({showDelDialog: false})}
        buttons={[
          {
            text: "取消",
            callback: (_) => {
              console.log(`${TAG} 删除操作 - 用户取消删除`);
              this.setState({showDelDialog: false});
            }
          },
          {
            text: "删除",
            callback: (_) => {
              console.log(`${TAG} 删除操作 - 用户确认删除`);
              this.onDeleteItem();
            }
          }
        ]}
      />
    );
  }

  onAddItem() {
    console.log(`${TAG} === 开始新增家人守护项 ===`);
    console.log(`${TAG} 新增操作 - 当前已有项目数量: ${this.state.listDatas.length}`);
    console.log(`${TAG} 新增操作 - 最大允许项目数量: ${MAX_ITEM_NUM}`);

    this.props.navigation.navigate('FamilyProtectionTimeSetting', {
      existList: this.state.listDatas,
      callback: (data) => {
        console.log(`${TAG} 新增操作 - 用户设置的新项目数据:`, JSON.stringify(data));

        let array = [];
        let key = 0;
        this.state.listDatas.forEach((item, index) => {
          if (key == this.keys[index]) {
            key += 1;
          }
          array.push({
            enable: item.enable,
            clock_idx: item.clock_idx,
            start: item.start,
            end: item.end,
            repeat: item.repeat,
            name: item.name
          });
          console.log(`${TAG} 新增操作 - 保留现有项目${index + 1}: 名称="${item.name}", 时间="${item.start}-${item.end}"`);
        });

        const newItem = {
          enable: true,
          clock_idx: key,
          start: data.start,
          end: data.end,
          repeat: data.repeat,
          name: data.name
        };
        array.push(newItem);

        console.log(`${TAG} 新增操作 - 新增项目: 名称="${newItem.name}", 时间="${newItem.start}-${newItem.end}", 索引=${newItem.clock_idx}`);
        console.log(`${TAG} 新增操作 - 新增后总项目数量: ${array.length}`);

        let arrayStr = JSON.stringify({idx: 2, clock: array});
        console.log(`${TAG} 新增操作 - 准备提交的完整数据:`, arrayStr);

        this.putLongTimeAlarmList(arrayStr).then((res) => {
          console.log(`${TAG} === 新增家人守护项成功 ===`);
          showToast(stringsTo("c_set_success"));
          this.loadData();
        }).catch((err) => {
          console.error(`${TAG} 新增操作 - 新增失败:`, JSON.stringify(err));
          showToast(stringsTo("c_set_fail"));
        });
      }
    });
  }

  onResume() {

  }

  onItemCheckChanged() {
    console.log(`${TAG} === 开始修改家人守护项状态 ===`);

    let array = [];
    this.state.listDatas.forEach((item, index) => {
      array.push({
        enable: item.enable,
        clock_idx: item.clock_idx,
        start: item.start,
        end: item.end,
        repeat: item.repeat,
        name: item.name
      });
      console.log(`${TAG} 修改操作 - 项目${index + 1}: 名称="${item.name}", 启用状态=${item.enable}, 时间="${item.start}-${item.end}"`);
    });

    let data = JSON.stringify({idx: 2, clock: array});
    console.log(`${TAG} 修改操作 - 准备提交的数据:`, data);
    console.log(`${TAG} 修改操作 - 共修改 ${array.length} 个项目的状态`);

    this.putLongTimeAlarmList(data).then((res) => {
      console.log(`${TAG} === 修改家人守护项状态成功 ===`);
    }).catch((err) => {
      console.error(`${TAG} 修改操作 - 修改失败:`, JSON.stringify(err));
      showToast(stringsTo("c_set_fail"));
    });
  }


  putLongTimeAlarmList(data) {
    return new Promise((resolve, reject) => {
      console.log(`${TAG} === 开始保存家人守护数据到设备 ===`);
      console.log(`${TAG} 保存操作 - 设备ID: ${LetDevice.deviceID}`);
      console.log(`${TAG} 保存操作 - 物模型ID: ${CAMERA_CONTROL.FAMILY_GUARD_CONFIG.PIID}`);
      console.log(`${TAG} 保存操作 - 原始数据:`, data);

      // 按照物模型格式构建数据
      const paramJson = JSON.stringify({
        msg_id: CAMERA_CONTROL.FAMILY_GUARD_CONFIG.PIID,
        value: data
      });
      console.log(`${TAG} 保存操作 - 格式化后的物模型数据:`, paramJson);
      console.log(`${TAG} 保存操作 - 数据大小: ${paramJson.length} 字符`);

      // 使用物模型方式设置数据
      LetDevice.setProperties(true, LetDevice.deviceID, CAMERA_CONTROL.FAMILY_GUARD_CONFIG.PIID, paramJson).then((res) => {
        console.log(`${TAG} 保存操作 - 物模型设置成功:`, JSON.stringify(res));
        console.log(`${TAG} === 家人守护数据保存完成 ===`);
        resolve(data);
      }).catch((err) => {
        console.error(`${TAG} 保存操作 - 保存家人守护数据失败:`, JSON.stringify(err));
        console.error(`${TAG} 保存操作 - 错误详情:`, err);
        // 保存失败时重新加载数据
        this.loadData();
        reject(err);
      });
    });
  }

  loadData() {
    console.log(`${TAG} === 开始获取家人守护数据 ===`);
    console.log(`${TAG} 获取操作 - 请求物模型ID: ${CAMERA_CONTROL.FAMILY_GUARD_CONFIG.PIID}`);

    this.setState({
      isLoading: true
    });

    // 使用物模型方式获取数据
    LetDevice.getSingleProperty(CAMERA_CONTROL.FAMILY_GUARD_CONFIG.PIID).then((result) => {
      console.log(`${TAG} 获取操作 - 原始响应数据类型:`, typeof (result));
      console.log(`${TAG} 获取操作 - 原始响应数据:`, JSON.stringify(result));

      if (result?.value?.code == 0 && result.value.value) {
        try {
          let valueObj = JSON.parse(result.value.value);
          let values = valueObj.clock;
          console.log(`${TAG} 获取操作 - 解析成功，获取到 ${values.length} 条家人守护数据`);

          this.keys = [];
          values.forEach((item, index) => {
            item.select = false;
            this.keys.push(item.clock_idx);
            console.log(`${TAG} 获取操作 - 数据项${index + 1}: 名称="${item.name}", 时间="${item.start}-${item.end}", 启用=${item.enable}, 重复=${item.repeat}`);
          });
          this.keys.sort((a1, a2) => {
            return a1 - a2;
          });
          console.log(`${TAG} 获取操作 - 排序后的索引键:`, this.keys);
          console.log(`${TAG} === 获取家人守护数据成功 ===`);

          this.setState({
            isLoading: false,
            listDatas: values
          });
        } catch (error) {
          console.error(`${TAG} 获取操作 - 解析家人守护数据失败:`, error);
          console.error(`${TAG} 获取操作 - 原始数据:`, result.value.value);
          this.productData();
        }
      } else {
        console.log(`${TAG} 获取操作 - 未获取到有效数据，初始化默认数据`);
        console.log(`${TAG} 获取操作 - 响应码:`, result?.value?.code);
        this.productData();
      }
    }).catch((err) => {
      console.error(`${TAG} 获取操作 - 获取家人守护数据失败:`, JSON.stringify(err));
      console.error(`${TAG} 获取操作 - 错误详情:`, err);
      // 如果获取失败，初始化默认数据
      this.productData();
    });
  }

  productData() {
    console.log(`${TAG} === 开始初始化默认家人守护数据 ===`);

    const defaultItems = [
      {
        enable: false,
        clock_idx: 0,
        start: "07:00",
        end: "09:00",
        repeat: 0b01111111,
        name: stringsTo('detect_nobody_in_morning')
      },
      {
        enable: false,
        clock_idx: 1,
        start: "11:00",
        end: "13:00",
        repeat: 0b01111111,
        name: stringsTo('nobody_have_lunch')
      },
      {
        enable: false,
        clock_idx: 2,
        start: "06:00",
        end: "20:00",
        repeat: 0b01111111,
        name: stringsTo('detect_nobody_in_day')
      }
    ];

    defaultItems.forEach((item, index) => {
      console.log(`${TAG} 初始化操作 - 默认项目${index + 1}: 名称="${item.name}", 时间="${item.start}-${item.end}", 启用=${item.enable}`);
    });

    let data = JSON.stringify({
      clock: defaultItems
    });

    console.log(`${TAG} 初始化操作 - 默认数据:`, data);
    console.log(`${TAG} 初始化操作 - 创建 ${defaultItems.length} 个默认项目`);

    this.putLongTimeAlarmList(data).then((res) => {
      console.log(`${TAG} === 默认家人守护数据初始化成功 ===`);
      this.loadData();
    }).catch((err) => {
      console.error(`${TAG} 初始化操作 - 初始化失败:`, JSON.stringify(err));
    });
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.editMode) {
      this.state.listDatas.forEach((item) => item.select = false);
      this.setState({editMode: false});
      this.selectCount = 0;
      return true;
    } else {
      return false;
    }
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  titleText: {
    fontSize: 30,
    color: "rgba(0, 0, 0, 0.80)",
    fontWeight: "300",
    position: "relative",
    marginLeft: 25,
    marginTop: 3,
    marginBottom: 23
  },
  desc_subtitle: {
    textAlign: 'left',
    color: "rgba(0, 0, 0, 0.6)",
    fontSize: 14,
    marginTop: 10,
    lineHeight: 21,
    marginHorizontal: 28
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20
  },
  switchContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 10,
    width: 60,
  },
  checkboxContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 10,
  },
  checkboxIcon: {
    width: 22,
    height: 22,
  },
  editModeBottomView: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    width: "100%",
    position: 'absolute',
    bottom: 15
  },
  deleteButtonContainer: {
    flexDirection: "column",
    alignItems: "center"
  },
  addButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  addButtonText: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  }
});