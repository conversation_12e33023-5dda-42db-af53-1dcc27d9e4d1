import React from 'react';

import {Image, StyleSheet, Text, View, ScrollView } from 'react-native';

import {imiThemeManager, showLoading, showToast} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice, LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import ImageButton from '../../../../imi-rn-commonView/ImageButton/ImageButton';
import NetInfos from '@react-native-community/netinfo';
let WifiImageArray = [
  require('../../resources/images/ic_wifi_signal_0.png'),
  require('../../resources/images/ic_wifi_signal_2.png'),
  require('../../resources/images/ic_wifi_signal_2.png'),
  require('../../resources/images/ic_wifi_signal_5.png'),
  require('../../resources/images/ic_wifi_signal_5.png'),
  require('../../resources/images/ic_wifi_signal_5.png')
];
let WifiSignalArray = [
  stringsTo('wifi_signal_0'),
  stringsTo('wifi_signal_2'),
  stringsTo('wifi_signal_2'),
  stringsTo('wifi_signal_5'),
  stringsTo('wifi_signal_5'),
  stringsTo('wifi_signal_5'),
];

export default class NetInfo extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      netInfo: {},
      error: false,
      loading: true,
      currentNetInfo: {}
    };
  }

  componentWillUnmount() {
    showLoading(false);
  }

  componentDidMount() {
    this.unsubscribe = NetInfos.addEventListener(state => {
      this.setState({
        currentNetInfo: state.details,
      })
    })
    this.getInfo()
  }

  getInfo =() => {
    showLoading(stringsTo('commLoadingText'), true);
    // 设备在线
    if (LetDevice.isOnline) {
      LetDevice.getSingleProperty('1').then(res => {
        this.setState({
          netInfo: res.value,
          loading: false
        })
      }).catch(() => {
        showToast(stringsTo('commLoadingFailText'))
        this.setState({
          error: true,
          loading: false
        })
      }).finally(() => {
        showLoading(false);
      })
      return 
    }

    // 设备离线
    const params1 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 1,
        method: 'sync',
      },
      Method: 'POST',
    };
    LetIMIIotRequest.sendUserServerRequest(params1, true, true).then(res => {
      this.setState({
        netInfo: res.value,
        loading: false
      })
    }).catch(() => {
      showToast(stringsTo('commLoadingFailText'))
      this.setState({
        error: true,
        loading: false
      })
    }).finally(() => {
      showLoading(false);
    })
  }

  ipToInteger = (ip) => {
    const parts = ip.split('.').map(Number);
    return (parts[0] << 24) | (parts[1] << 16) | (parts[2] << 8) | parts[3];
  }

  maskToInteger = (mask) => {
    const parts = mask.split('.').map(Number);
    return (parts[0] << 24) | (parts[1] << 16) | (parts[2] << 8) | parts[3];
  }

  calculateNetworkAddress = (ip, mask) => {
    const ipInt = this.ipToInteger(ip);
    const maskInt = this.maskToInteger(mask);
    return ipInt & maskInt;
  }

 isSameSubnet = (ip1, ip2, subnetMask1,subnetMask2) => {
    const network1 = this.calculateNetworkAddress(ip1, subnetMask1);
    const network2 = this.calculateNetworkAddress(ip2, subnetMask2);
    return network1 === network2;
}

isLocalIP = (ip1, ip2, mask1, mask2) => {
  // 判断是否在同一个局域网
  const result = this.isSameSubnet(ip1, ip2, mask1, mask2);
  return result;
};

calculateSignalLevel(rssi,numLevels){
  let MIN_RSSI = -100;
  let MAX_RSSI = -55;
  if (rssi <= MIN_RSSI) {
      return 0;
  } else if (rssi >= MAX_RSSI) {
      return numLevels - 1;
  } else {
      let inputRange = (MAX_RSSI - MIN_RSSI);
      let outputRange = (numLevels - 1);
      let range = ((rssi - MIN_RSSI) * outputRange / inputRange);
      if(range >=1 && range < 2){
          return  1;
      }else if(range >=2 && range < 3){
          return  2;
      }else if(range >=3 && range < 4){
          return  3;
      }else if(range >=4 && range < 5){
          return  4;
      }else if(range >=5 && range < 6){
          return  5;
      }
  }
  return numLevels - 1;
}

  render() {
    const wifi_signal_level = this.calculateSignalLevel(this.state.netInfo?.rssi, 6);
    const error = this.state.error
    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('network_info')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'more_setting_back',
            },
          ]}
          right={[]}
        />
        <ScrollView style={{display: this.state.loading ? 'none' : ''}}>
          <View style={styles.contain}>
                <ImageButton style={{width: 70, height: 70}} source={WifiImageArray[LetDevice.isOnline && !error ?wifi_signal_level:0]}/>
                {!error && <Text style={{color:'#7F7F7F',textAlign: 'center',fontSize:12, marginTop: 10}}>
                    {LetDevice.isOnline?WifiSignalArray[wifi_signal_level]:stringsTo('device_offline')}
                </Text>}
          </View>
          <View style={{...styles.item}}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_name')}
              value={this.state.netInfo?.wifi_ssid}
              accessibilityLabel={'wifi_name'}
            />
          </View>
          <View style={styles.item}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_strength')}
              value={(this.state.netInfo?.wifi_signal_percent || 0) + '%'}
              accessibilityLabel={'wifi_strength'}
            />
          </View>
          <View style={styles.item}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_rssi')}
              value={this.state.netInfo?.rssi === 0 ? '0' : this.state.netInfo?.rssi}
              accessibilityLabel={'wifi_rssi'}
            />
          </View>
          {/* <View style={styles.item}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_loss')}
              value={''}
              accessibilityLabel={'wifi_loss'}
            />
          </View> */}
          <View style={styles.item}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_mode')}
              value={this.state.netInfo?.ip_addr && this.state.currentNetInfo?.ipAddress
                ? this.isLocalIP(this.state.netInfo?.ip_addr, this.state.currentNetInfo?.ipAddress, this.state.netInfo?.mask, this.state.currentNetInfo?.subnet) 
                ? stringsTo('wifi_mode_type2') 
                : stringsTo('wifi_mode_type1') 
                : !this.state.currentNetInfo?.ipAddress ? stringsTo('wifi_mode_type1') : ''}
              accessibilityLabel={'wifi_mode'}
            />
          </View>
          
          <View style={styles.item}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_ip_address')}
              value={this.state.netInfo?.ip_addr}
              accessibilityLabel={'wifi_ip_address'}
            />
          </View>
          <View style={styles.item}>
            <ListItem
              hideArrow={true}
              title={stringsTo('wifi_mac_address')}
              value={this.state.netInfo?.mac_addr}
              accessibilityLabel={'wifi_mac_address'}
            />
          </View>
        </ScrollView>
      </View >
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
    overflow: 'scroll'
  },
  item: {
    borderBottomColor: '#f0f2f5',
    borderBottomWidth: 1,
  },
  contain: {
    display: 'flex',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column', 
  }
});
