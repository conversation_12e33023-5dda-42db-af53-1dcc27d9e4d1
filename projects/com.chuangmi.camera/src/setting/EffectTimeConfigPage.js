import React from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import DatePicker from '../../../../imilab-design-ui/src/widgets/settingUI/DatePicker';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';

const screen_width = Dimensions.get('window').width;

const _repeatType = {
  EVERY_DAY: 0,
  WORK_DAY: 1,
  WEEKEND: 2,
  ONCE: 3,
  CUSTOM: 4,
};

/**
 * 生效时间页面
 */
export default class EffectTimeConfigPage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props, context) {
    super(props, context);
    this.state = {
      isPickerVisible: false,
      isStartPicker: true,
      startTime: this.props.route.params.startTime,
      endTime: this.props.route.params.endTime,
      repeatValue: this.props.route.params.repeatValue,
      tempRepeatValue: this.props.route.params.repeatValue,
      repeatType: [
        {title: stringsTo('plug_timer_everyday'), value: _repeatType.EVERY_DAY},
        {title: stringsTo('workday'), value: _repeatType.WORK_DAY},
        {title: stringsTo('weekend'), value: _repeatType.WEEKEND},
        {title: stringsTo('do_once'), value: _repeatType.ONCE},
        {title: stringsTo('do_custom'), value: _repeatType.CUSTOM},
      ],
      showRepeatModal: false,
      weekData: [
        {title: stringsTo('Sunday'), value: 7},
        {title: stringsTo('Monday'), value: 1},
        {title: stringsTo('Tuesday'), value: 2},
        {title: stringsTo('Wednesday'), value: 3},
        {title: stringsTo('Thursday'), value: 4},
        {title: stringsTo('Friday'), value: 5},
        {title: stringsTo('Saturday'), value: 6},
      ],
      showWeekPickModal: false,
    };
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentWillUnmount() {
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentDidMount() {}

  onBackHandler = () => {
    console.log('onBackAndroid  navigation.isFocused() ' + navigation.isFocused());
    if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    } else {
    }
    return false;
  };

  _onPressBack = () => {
    this.props.navigation.pop();
  };

  _onSavePress() {
    this.props.navigation.pop();
    if (this.props.route.params.callback) {
      this.props.route.params.callback(this.state.startTime, this.state.endTime, this.state.repeatValue);
    }
  }

  render() {
    global.navigation = this.props.navigation;
    const isAllDay = this.state.startTime === '00:00:00' && this.state.endTime === '23:59:59';
    return (
      <View style={styles.container}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={stringsTo('effective_time_set')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[{key: NavigationBar.ICON.COMPLETE, onPress: () => this._onSavePress()}]}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          {this._renderItemOne()}
          {this._renderItemFour()}
          {!isAllDay && this._renderShowTime()}
          {this._splitLine()}
          {this._repeatView()}
          {this._showDatePicker()}
          {this._repeatTypeModal()}
          {this._repeatDateModal()}
        </ScrollView>
      </View>
    );
  }

  _renderItemOne() {
    const isAllDay = this.state.startTime === '00:00:00' && this.state.endTime === '23:59:59';
    return (
      <View style={{width: '100%', height: 74, backgroundColor: 'white', marginTop: 14, borderRadius: 16}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            this.setState({startTime: '00:00:00', endTime: '23:59:59'});
          }}>
          <View style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', height: 74}}>
            <View>
              <Text
                style={{
                  marginLeft: 20,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 16,
                  textAlign: 'left',
                  height: 'auto',
                  color: isAllDay ? imiThemeManager.theme.primaryColor : '#000000',
                }}>
                {stringsTo('all_day')}
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                right: 20,
                height: '100%',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}>
              <Image
                style={{height: 25, width: 25}}
                source={
                  isAllDay
                    ? require('../../resources/images/icon_radio_select.png')
                    : require('../../resources/images/icon_unselect.png')
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemFour() {
    const isAllDay = this.state.startTime === '00:00:00' && this.state.endTime === '23:59:59';
    const bottomRadius = isAllDay ? 16 : 0;
    return (
      <View
        style={{
          width: '100%',
          height: 74,
          backgroundColor: 'white',
          marginTop: 12,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderBottomLeftRadius: bottomRadius,
          borderBottomRightRadius: bottomRadius,
        }}>
        <TouchableWithoutFeedback
          onPress={_ => {
            if (isAllDay) {
              this.setState({endTime: '23:59:00'});
            }
          }}>
          <View style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', height: 74}}>
            <View>
              <Text
                style={{
                  marginLeft: 20,
                  lineHeight: 58,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 16,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: !isAllDay ? imiThemeManager.theme.primaryColor : '#000000',
                }}>
                {stringsTo('voice_for_custom')}
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                right: 20,
                height: '100%',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}>
              <Image
                style={{height: 25, width: 25}}
                source={
                  !isAllDay
                    ? require('../../resources/images/icon_radio_select.png')
                    : require('../../resources/images/icon_unselect.png')
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderShowTime() {
    const {startTime, endTime} = this.state;
    const startText = startTime.substring(0, 5);
    const endText = endTime.substring(0, 5);
    return (
      <View>
        <ListItem
          titleStyle={{fontSize: 16, fontWeight: 'normal', color: '#000000', paddingLeft: 6}}
          containerStyle={{paddingRight: 20, width: '100%'}}
          title={I18n.t('picker_start_time')}
          hideArrow={false}
          value={startText}
          onPress={() => {
            this.setState({isPickerVisible: true, isStartPicker: true});
          }}
        />
        <ListItem
          titleStyle={{fontSize: 16, fontWeight: 'normal', color: '#000000', paddingLeft: 6}}
          containerStyle={{paddingRight: 20, width: '100%', borderBottomLeftRadius: 16, borderBottomRightRadius: 16}}
          title={I18n.t('picker_end_time')}
          hideArrow={false}
          value={endText}
          onPress={() => {
            this.setState({isPickerVisible: true, isStartPicker: false});
          }}
        />
      </View>
    );
  }

  _splitLine() {
    return (
      <View style={{backgroundColor: '#00000026', height: 1, marginTop: 20, marginBottom: 20, marginHorizontal: 15}} />
    );
  }

  _repeatView() {
    const repeatType = this._getRepeatType(this.state.repeatValue);
    const repeatContent = this.state.repeatType.find(item => item.value === repeatType).title;
    return (
      <ListItem
        titleStyle={{fontSize: 16, fontWeight: 'normal', color: '#000000', paddingLeft: 6}}
        containerStyle={{paddingRight: 20, width: '100%', borderRadius: 16}}
        title={I18n.t('plug_timer_repeat')}
        hideArrow={false}
        value={repeatContent}
        onPress={() => {
          this.setState({showRepeatModal: true, tempRepeatValue: [...this.state.repeatValue]});
        }}
      />
    );
  }

  _showDatePicker() {
    const {isStartPicker, startTime, endTime} = this.state;
    const startArr = startTime.substring(0, 5).split(':');
    const endArr = endTime.substring(0, 5).split(':');
    const timeArr = isStartPicker ? startArr : endArr;
    return (
      <View>
        <DatePicker
          visible={this.state.isPickerVisible}
          title={this.state.isStartPicker ? I18n.t('picker_start_time') : I18n.t('picker_end_time')}
          type={'time24'}
          onDismiss={_ => {}}
          currentSelectTime={timeArr}
          onSelectConfirm={time => {
            const selectValue = time.rawArray[0] + ':' + time.rawArray[1] + ':00';
            if (isStartPicker) {
              console.log('当前开始的时间为-------', selectValue);
              this.setState({isPickerVisible: false, startTime: selectValue});
            } else {
              console.log('当前结束的时间为-------', selectValue);
              this.setState({isPickerVisible: false, endTime: selectValue});
            }
          }}
        />
      </View>
    );
  }

  // 重复弹窗
  _repeatTypeModal() {
    return (
      <View>
        <MessageDialog
          title={I18n.t('plug_timer_repeat')}
          visible={this.state.showRepeatModal}
          canDismiss={false}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelSpotWarning',
              callback: _ => {
                this.setState({showRepeatModal: false, tempRepeatValue: [...this.state.repeatValue]});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okSpotWarning',
              callback: _ => {
                const currentRepeatType = this._getRepeatType(this.state.tempRepeatValue);
                if (currentRepeatType === _repeatType.CUSTOM) {
                  this.setState({showRepeatModal: false, showWeekPickModal: true});
                } else {
                  this.setState({showRepeatModal: false, repeatValue: [...this.state.tempRepeatValue]});
                }
              },
            },
          ]}>
          <FlatList
            data={this.state.repeatType}
            renderItem={this._repeatTypeItem}
            keyExtractor={(item, index) => `key_${index}`}
            extraData={this.state}
            onEndReachedThreshold={0.1}
          />
        </MessageDialog>
      </View>
    );
  }

  _repeatTypeItem = ({item, index}) => {
    const repeatType = this._getRepeatType(this.state.tempRepeatValue);
    return (
      <TouchableWithoutFeedback
        onPress={() => {
          if (item.value === _repeatType.CUSTOM) {
            this.setState({showRepeatModal: false, showWeekPickModal: true});
          } else {
            const tempRepeatValue = this._getRepeatValue(item.value);
            this.setState({tempRepeatValue});
          }
        }}>
        <View
          style={{
            flexDirection: 'column',
            height: 55,
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'white',
          }}>
          <View style={{flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center'}}>
            <Text
              style={{
                flexGrow: 1,
                fontSize: 15,
                marginLeft: 20,
                color: item.value === repeatType ? imiThemeManager.theme.primaryColor : '#333333',
                textAlign: 'left',
              }}>
              {item.title}
            </Text>
            <Image
              style={{height: 20, width: 20, marginRight: 20, resizeMode: 'contain'}}
              source={item.value === repeatType ? require('../../resources/images/icon_select_s.png') : null}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  _getRepeatType(value) {
    const simpleValue = JSON.stringify(value);
    let repeatType = _repeatType.CUSTOM;
    if (simpleValue === JSON.stringify([1, 1, 1, 1, 1, 1, 1])) {
      repeatType = _repeatType.EVERY_DAY;
    } else if (simpleValue === JSON.stringify([1, 1, 1, 1, 1, 0, 0])) {
      repeatType = _repeatType.WORK_DAY;
    } else if (simpleValue === JSON.stringify([0, 0, 0, 0, 0, 1, 1])) {
      repeatType = _repeatType.WEEKEND;
    } else if (simpleValue === JSON.stringify([0, 0, 0, 0, 0, 0, 0])) {
      repeatType = _repeatType.ONCE;
    }
    return repeatType;
  }

  _getRepeatValue(type) {
    let repeatValue = [];
    if (type === _repeatType.EVERY_DAY) {
      repeatValue = [1, 1, 1, 1, 1, 1, 1];
    } else if (type === _repeatType.WORK_DAY) {
      repeatValue = [1, 1, 1, 1, 1, 0, 0];
    } else if (type === _repeatType.WEEKEND) {
      repeatValue = [0, 0, 0, 0, 0, 1, 1];
    } else if (type === _repeatType.ONCE) {
      repeatValue = [0, 0, 0, 0, 0, 0, 0];
    }
    return repeatValue;
  }

  // 重复日期选择弹窗
  _repeatDateModal() {
    return (
      <View>
        <MessageDialog
          title={I18n.t('plug_timer_repeat')}
          visible={this.state.showWeekPickModal}
          canDismiss={false}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelSpotWarning',
              callback: _ => {
                this.setState({showWeekPickModal: false, tempRepeatValue: [...this.state.repeatValue]});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okSpotWarning',
              callback: _ => {
                this.setState({showWeekPickModal: false, repeatValue: [...this.state.tempRepeatValue]});
              },
            },
          ]}>
          <FlatList
            data={this.state.weekData}
            renderItem={this._repeatDateItem}
            keyExtractor={(item, index) => `key_${index}`}
            extraData={this.state}
            onEndReachedThreshold={0.1}
          />
        </MessageDialog>
      </View>
    );
  }

  _repeatDateItem = ({item, index}) => {
    const isSelected = this.state.tempRepeatValue[index] === 1;
    return (
      <TouchableWithoutFeedback
        onPress={() => {
          const tempRepeatValue = this.state.tempRepeatValue;
          tempRepeatValue[index] = !!tempRepeatValue[index] ? 0 : 1;
          this.setState({tempRepeatValue});
        }}>
        <View
          style={{
            flexDirection: 'column',
            height: 55,
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'white',
          }}>
          <View style={{flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center'}}>
            <Text
              style={{
                flexGrow: 1,
                fontSize: 15,
                marginLeft: 20,
                color: isSelected ? imiThemeManager.theme.primaryColor : '#333333',
                textAlign: 'left',
              }}>
              {item.title}
            </Text>
            <Image
              style={{height: 20, width: 20, marginRight: 20, resizeMode: 'contain'}}
              source={
                isSelected
                  ? require('../../resources/images/icon_radio_select.png')
                  : require('../../resources/images/icon_unselect.png')
              }
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBgGrey,
    paddingStart: 12,
    paddingEnd: 12,
  },
  functionSettingStyle: {
    color: '#0000007F',
    fontSize: 12,
    marginTop: 23,
    marginLeft: 14,
  },
  modalTit: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 60,
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    textAlign: 'center',
    // backgroundColor: 'red',
    fontWeight: 'bold',
    justifyContent: 'center',
  },
  text: {
    width: 200,
    height: 40,
    marginTop: 10,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
  },
  pickerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    backgroundColor: 'black',
  },
  pickerViewContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingTop: 30,
  },
  pickerStyle: {
    flex: 1,
  },
});
