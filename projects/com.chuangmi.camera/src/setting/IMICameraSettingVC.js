import React from 'react';

import {StyleSheet, View, ScrollView, Dimensions, DeviceEventEmitter, Text} from 'react-native';

import {imiThemeManager, RoundedButtonViewNew, RoundedButtonView} from '../../../../imilab-design-ui';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';

import {LetDevice, IMIStorage, LetIProperties, LetIMIIotRequest} from '../../../../imilab-rn-sdk';

import {showToast, MessageDialog, showLoading,SlideGear} from '../../../../imilab-design-ui';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import IMIHost from '../../../../imilab-rn-sdk/native/local-kit/IMIHost';

import DeviceHotSpotUtils from '../utils/DeviceHotSpotUtils';
import IMP2pClient from '../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import { isNull } from '../../../../imilab-rn-sdk/utils/TypeUtils';
const deviceHotSpotUtil = DeviceHotSpotUtils.getInstance();
/**
 * 摄像机功能设置页面
 */

 const newWidth =Math.min(Dimensions.get('window').width,Dimensions.get('window').height)

   
export default class IMICameraSettingVC extends BaseDeviceComponent {
  static propTypes = {};

  componentDidMount() {
    console.log('进来了');

    /*
        /*LetDevice.getPropertyCloud('DayNightMode').then(function (data) {
            console.log('getPropertyCloud' + data);
        }).catch(error => {
            console.log(JSON.stringify(error))
        });*/
    /*本页面失去焦点*/
    this._subscribeBlur = this.props.navigation.addListener('blur', () => {
      showLoading(false);
    });
    this.chooseCruiseListener = DeviceEventEmitter.addListener('saveCruiseWaySuccess', res => {
      //刷新数据
      this.setState({cruise_switch: res});
    });
    IMIStorage.load({
      key: LetDevice.deviceID + 'isDataUsageWarning',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({browserPowerSaveHintSwitch: res.isDataUsage});
      })
      .catch(_ => {
        this.setState({browserPowerSaveHintSwitch: false});
      });

    LetDevice.updateAllPropertyCloud()
      .then(data => {
        let dataPackage = JSON.parse(data);
        let stateProps = {};
        dataPackage?.forEach(item => {
          // console.log(item);

          //状态灯
          // if (`${item?.thingId}` === `${10012}`) {
          //   stateProps.statusLightValue = !!item?.value?.value;
          // }
          //夜视
          if (`${item?.thingId}` === `${10003}`) {
            console.log('nightFunctionValue ', item);
            stateProps.nightValue = item?.value?.value;
            let tempValue = item?.value?.value;
            if (tempValue == 0) {
              // 黑白夜视
              stateProps.nightFunctionValue = stringsTo('black_white_vision_title');
            } else if (tempValue == 1) {
              // 全彩夜视
              stateProps.nightFunctionValue = stringsTo('full_color_vision_title');
            } else if (tempValue == 2) {
              // 智能夜视
              stateProps.nightFunctionValue = stringsTo('fullColor_smart_tit');
            }
          }
          //微光全彩
          if (`${item?.thingId}` === `${10006}`) {
            console.log('stateProps.fullColor', item);

            stateProps.fullColor = item?.value?.value;
          }
        });

        /* if (dataPackage.StatusLightSwitch) {
          //056数据类型不对 需要的是bool 返回为number
          if (typeof dataPackage.StatusLightSwitch.value === 'number') {
            if (dataPackage.StatusLightSwitch.value == 1) {
              stateProps.statusLightValue = true;
            } else {
              stateProps.statusLightValue = false;
            }
          } else {
            stateProps.statusLightValue = dataPackage.StatusLightSwitch.value;
          }
        }

        if (dataPackage.ImageFlipState) {
          stateProps.imageFlipState = dataPackage.ImageFlipState.value;
        }

        //微光全彩
        if (dataPackage.FullColor) {
          if (typeof dataPackage.FullColor.value === 'number') {
            if (dataPackage.FullColor.value === 1) {
              stateProps.fullColor = true;
            } else {
              stateProps.fullColor = false;
            }
          } else {
            stateProps.fullColor = dataPackage.FullColor.value;
          }
        }

        //夜视模式
        if (dataPackage.DayNightMode) {
          let tempValue = dataPackage.DayNightMode.value;
          if (tempValue == 2) {
            // 黑白夜视
            stateProps.nightFunctionValue = stringsTo('black_white_vision_title');
          } else if (tempValue == 3) {
            // 全彩夜视
            stateProps.nightFunctionValue = stringsTo('full_color_vision_title');
          } else if (tempValue == 4) {
            // 智能夜视
            stateProps.nightFunctionValue = stringsTo('fullColor_smart_tit');
          }
        }

        //TODO 镜头畸变校正固件端未实现
        if (dataPackage.DistortionCorrection) {
          let pictureCorrectionValue = dataPackage.DistortionCorrection.value;
          if (pictureCorrectionValue) {
            stateProps.pictureCorrectionSwitch = true;
          }
        }

        if (dataPackage.CruiseSwitch) {
          stateProps.cruise_switch = dataPackage.CruiseSwitch.value;
          console.log('巡航开关值---', dataPackage.CruiseSwitch.value);
        } */

        console.log('物模型云端数据--------', stateProps);
        this.setState(stateProps);
      })
      .catch(error => {
        console.log('error111', JSON.stringify(error));
      });
    LetDevice.getSingleProperty('10030').then(data=>{
      console.log('10030',data);
      if(data?.value?.code==0){
        this.setState({
          cameraVolume:data?.value?.value,
        });
        this.initCameraVolume=data?.value?.value
      }
    })

    this.getSysProperty(); //是否休眠
    IMP2pClient.onFileOperateAddListener(data => {});
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      statusLightValue: false, //设置为ture 056报错statusLightValue: 0
      imageFlipState: false,
      trackSwitchValue: true,
      showCalibrationModal: false,
      fullColor: false,
      showRebootModal: false,
      nightFunctionValue: '', //夜视模式
      isSleep: false, //是否休眠
      cruise_switch: 0, //智能巡航类型
      browserPowerSaveHintSwitch: false, //流量保护
      cameraVolume: 1, //摄像机外放音量
      cameraVolumeModal: false,
    };
    this.cameraVolumeOptions=Array.from({length:101}, (_,k) => k);
    this.initCameraVolume=1;
  }

  componentWillUnmount() {
    this.chooseCruiseListener && this.chooseCruiseListener.remove();
    this.calibrationTimer && clearTimeout(this.calibrationTimer);
    showLoading(false);
  }

  render() {
    let {showSleep, showCorrectPos, showSynchronous056, hideStatusLight} = DeviceTemplatesUtils.getConfigProject(
      'camera',
      'a1FKrifIRwH',
    );


    return (
      <View style={styles.container}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          title={I18n.t('popo_setting_camera_text')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => this.props.navigation.pop(),
              accessibilityLabel: 'camera_go_back',
            },
          ]}
          right={[]}
        />
        <ScrollView showsVerticalScrollIndicator={false} style={{backgroundColor: '#fff'}}>
          <Separator />

          {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}

          <ListItmeWithSwitch
            title={stringsTo('settings_light_title')}
            value={this.state.statusLightValue}
            hide={hideStatusLight}
            onValueChange={value => {
              const params = {msg_id: 10009, value};
              const paramJson = JSON.stringify(params);
              showLoading(stringsTo('commLoadingText'), true);
              this.setState({
                statusLightValue: value,
              });
              LetDevice.setProperties(true, LetDevice.deviceID, '10009', paramJson)
                .then(res => {
                  console.log('设置指示灯开关成功', res);
                  showLoading(false);
                  if (value) {
                    IMIStorage.load({
                      key: LetDevice.deviceID + 'isNeverAlertPowerSaveHint',
                      autoSync: true,
                      syncInBackground: true,
                    }).then(res => {
                      if (res.isNeverRemind) {
                        IMIStorage.save({
                          key: LetDevice.deviceID + 'isNeverAlertPowerSaveHint',
                          data: {
                            isNeverRemind: false,
                          },
                          expires: null,
                        });
                      }
                    });
                  }
                })
                .catch(err => {
                  console.log('设置图指示灯开关', JSON.stringify(err));
                  showLoading(false);
                  showToast(I18n.t('operationFailed'));
                  this.setState({
                    statusLightValue: !value,
                  });
                });

              /*  if (value) {
                // LetDevice.propertyOn
                showLoading(stringsTo('commLoadingText'), true);
                IMILogUtil.propertyOnWithUploadLog('StatusLightSwitch')
                  .then(() => {
                    //解决开始是关闭状态，打开后，点击重启，开关又重置为关闭状态
                    showLoading(false);
                    this.setState({
                      statusLightValue: true,
                    });
                    //每次打开开关，则检查和重置"不再提醒"标志为false
                    IMIStorage.load({
                      key: LetDevice.deviceID + 'isNeverAlertPowerSaveHint',
                      autoSync: true,
                      syncInBackground: true,
                    })
                      .then(res => {
                        if (res.isNeverRemind) {
                          IMIStorage.save({
                            key: LetDevice.deviceID + 'isNeverAlertPowerSaveHint',
                            data: {
                              isNeverRemind: false,
                            },
                            expires: null,
                          });
                        }
                      })
                      .catch(_ => {});
                  })
                  .catch(() => {
                    showLoading(false);
                    this.setState({
                      statusLightValue: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                  });
              } else {
                showLoading(stringsTo('commLoadingText'), true);
                IMILogUtil.propertyOffWithUploadLog('StatusLightSwitch')
                  .then(() => {
                    showLoading(false);
                    this.setState(
                      {
                        statusLightValue: false,
                      },
                      callback => {},
                    );
                  })
                  .catch(() => {
                    showLoading(stringsTo('commLoadingText'), true);
                    this.setState({
                      statusLightValue: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                  });
              } */

              IMIStorage.load({
                key: LetDevice.deviceID + 'isDataUsageWarning',
                autoSync: true,
                syncInBackground: true,
              })
                .then(res => {
                  // console.log('关闭--当前流量保护',res);
                  this.setState({browserPowerSaveHintSwitch: res.isDataUsage});
                })
                .catch(_ => {
                  this.setState({browserPowerSaveHintSwitch: false});
                });
              // console.log('关闭--当前流量保护状态----xy',this.state.browserPowerSaveHintSwitch);
            }}
            accessibilityLabel={['camera_state_lamp_off', 'camera_state_lamp_on']}
          />
          {/* 流量保护 */}
          <ListItmeWithSwitch
            title={stringsTo('Data_usage_warning')}
            hide={deviceHotSpotUtil.isDirectConnectMode()}
            value={this.state.browserPowerSaveHintSwitch}
            subtitle={I18n.t('data_usage_warning_intro')}
            
            onValueChange={value => {
              showLoading(stringsTo('commWaitText'), true);
              IMIStorage.save({
                key: LetDevice.deviceID + 'isDataUsageWarning',
                data: {
                  isDataUsage: value,
                },
                expires: null,
              });
              setTimeout(() => {
                showToast(stringsTo('settings_set_success'))
                showLoading(false);
              }, 1000);
            }}
            accessibilityLabel={['camera_flow_protection_off', 'camera_flow_protection_on']}
          />

          {/*智能巡航*/}
          {/* <ListItem
            title={stringsTo('intelligent_cruise')}
             hide={!showCruise}
            value={
              this.state.cruise_switch == 0
                ? stringsTo('settings_switch_off')
                : this.state.cruise_switch == 1
                ? stringsTo('cruise_all_view')
                : stringsTo('cruise_favorite')
            }
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              this.props.navigation.push('IntelligentCruise');
            }}
            accessibilityLabel={'intelligent cruise'}
          /> */}

          {/* <View style={{height: 14, backgroundColor: '#F1F1F1'}} /> */}
          {/* 休眠设置 */}
          <ListItem
            title={stringsTo('sleep_set')}
            hide={!showSleep || deviceHotSpotUtil.isDirectConnectMode()}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              this.props.navigation.push('SleepPage');
            }}
            accessibilityLabel={'camera_dormancy'}
          />
          <ListItem
            title={stringsTo('setttings_infared')}
            /*      hide={!showDayNightMode} 记得打开 todo*/
            // value={this.state.nightFunctionValue}
            onPress={() => {
              // this.props.navigation.push("NightFunctionPage");
              this.props.navigation.navigate('NightFunctionPage', {
                nightValue: this.state.nightValue,
                callback: nightStr => {
                  console.log('返回修改侦测时间--', nightStr);

                  this.setState({
                    nightFunctionValue: nightStr,
                  });
                },
              });
            }}
          />

          <ListItem title={stringsTo('soundLightAlarm')} onPress={() => {
            this.props.navigation.navigate('AudioAlarmSetPage', {
              location: this.state.location,
              soundLightSwitchValue: this.state.soundLightSwitchValue,
              voiceSwitch: this.state.voiceSwitch,
              voiceUrl: this.state.voiceUrl,
              lightSwitch: this.state.lightSwitch,
              lightMode: this.state.lightMode,
              containTime: this.state.containTime,
              callback: ((backParams) => {})
            });
          }}/>

          {/*<ListItmeWithSwitch title={stringsTo('settings_flip_title')} value={this.state.imageFlipState}*/}
          {/*                    subtitle={stringsTo('settings_flip_subtitle')}*/}
          {/*                    onValueChange={(value) => {*/}
          {/*    if(value){*/}
          {/*        LetDevice.propertyOn("ImageFlipState").then(()=>{*/}
          {/*        }).catch(err=>{*/}
          {/*            this.setState({*/}
          {/*                imageFlipState:!value*/}
          {/*            });*/}
          {/*            showToast(I18n.t('operationFailed'));*/}
          {/*        });*/}
          {/*    }else{*/}
          {/*        LetDevice.propertyOff("ImageFlipState").then(()=>{*/}
          {/*        }).catch(err=>{*/}
          {/*            this.setState({*/}
          {/*                imageFlipState:!value*/}
          {/*            });*/}
          {/*            showToast(I18n.t('operationFailed'));*/}
          {/*        });*/}
          {/*    }*/}
          {/*}}/>*/}
          {/* <View>
            <View style={{height: 14, backgroundColor: '#F1F1F1'}} />
          </View> */}
   <ListItem
            title={stringsTo('cruise_control')}
            hide={!showCorrectPos}
            onPress={() => {
             this.props.navigation.push('CruiseControlSetting');
             
            }}
            accessibilityLabel={'cruise_control'}
          />
         

          <ListItem
            title={stringsTo('imi_camera_correct_pos')}
            hide={!showCorrectPos}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
                return;
              }

              // IMP2pClient.getChannelState(data => {
              //   if (parseInt(data, 10) === 1) {
                  this.setState({showCalibrationModal: true});
                // } else {
                //   showToast('未连接到云台，请稍后重试');
                //   return;
                // }
              // });
            }}
            accessibilityLabel={'camera_calibration'}
          />
          
          <ListItem
            title={stringsTo('camera_volume')}
            hide={!showCorrectPos}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
                return;
              }
              this.setState({cameraVolumeModal: true});
             
            }}
            value={this.cameraVolumeOptions[this.state.cameraVolume]+ '%'}
            accessibilityLabel={'camera_calibration'}
          />
           <ListItem
            title={
             stringsTo('setting_picture_setting')
            }
            onPress={() => {
              this.props.navigation.push('ImageSetting');
            }}
            accessibilityLabel={'camera_more_setting'}
          />
          <View style={{height: 80}} />
        </ScrollView>

        <View style={{width: '100%', height: 75, position: 'absolute', bottom: 0}}>
          <RoundedButtonViewNew
            buttonText={I18n.t('setting_reset')}
            disabled={false}
            buttonStyle={{backgroundColor: '#EEEEEE', margin: 15}}
            buttonTextStyle={{color: '#EB614B'}}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
              } else {
                this.setState({showRebootModal: true});
              }
            }}
            accessibilityLabel={'camera_restart'}
          />
        </View>
        {/* 云台校准暂时没这个物模型 */}
        <MessageDialog
          title={stringsTo('imi_camera_correct_pos')}
          visible={this.state.showCalibrationModal}
          canDismiss={true}
          // message={LetDevice.model == "a1FKrifIRwH"||LetDevice.model == "a1Ikkj5vsiK"|| LetDevice.model == "a1znn6t1et8" ? stringsTo("calibration_to_continue_30"):stringsTo(showSynchronous056?IMIHost.serverCode==0?'calibration_to_continue_35':'calibration_to_continue':'calibration_to_continue')} //只有056为35秒
          message={stringsTo('calibration_to_continue')} //只有056为35秒
          messageStyle={{marginBottom: 20, paddingLeft: 10, paddingRight: 10}}
          onDismiss={_ => {
            this.setState({showCalibrationModal: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelCameraPos',
              callback: _ => {
                this.setState({showCalibrationModal: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okCameraPos',
              callback: _ => {
                this.setState({showCalibrationModal: false});
                // showLoading(stringsTo('is_the_calibration'), true);

                showLoading(stringsTo('is_the_calibration'), true);

                const messageJson = JSON.stringify({
                  p2p_cmd: 1, //0x1:电机控制
                  msg_id: 1,
                  operation: 5, //电机转动方向 1:left 2:lright 3:up 4:down 5:reset
                });
                IMP2pClient.sendP2pData('2', messageJson);
                showLoading(false);
                this.setState({showCalibrationModal: false});

                // LetDevice.sendDeviceServerRequest('PTZCalibrate', {
                //   Direction: 0,
                // })
                //   .then(data => {
                //     this.setState({showCalibrationModal: false});
                //     if (
                //       showSynchronous056 ||
                //       LetDevice.model == 'a1FKrifIRwH' ||
                //       LetDevice.model == 'a1Ikkj5vsiK' ||
                //       LetDevice.model == 'a1znn6t1et8'
                //     ) {
                //       //只有056为跳转首页且35秒
                //       this.props.navigation.popToTop();
                //       /*发送广播,通知首页*/
                //       DeviceEventEmitter.emit('isCalibration', true); //添加广播
                //     } else {
                //       this.calibrationTimer && clearTimeout(this.calibrationTimer);
                //       this.calibrationTimer = setTimeout(() => {
                //         showLoading(false);
                //         showToast(I18n.t('calibration_completed'));
                //       }, 25000);
                //     }
                //     console.log('sendDeviceServerRequest:ok:' + data);
                //   })
                //   .catch(error => {
                //     showLoading(false);
                //     showToast(I18n.t('calibration_failure'));
                //     this.setState({showCalibrationModal: false});
                //     console.log('sendDeviceServerRequest:err:' + error);
                //   });
              },
            },
          ]}
        />
         {/* 摄像机外放音量*/}
          <MessageDialog
          title={stringsTo('camera_volume')}
          visible={this.state.cameraVolumeModal}
          canDismiss={true}
          subtile={this.cameraVolumeOptions[this.state.cameraVolume]+ '%'}
          // message={stringsTo('calibration_to_continue')} //只有056为35秒
          // messageStyle={{marginBottom: 20, paddingLeft: 10, paddingRight: 10}}
          messageContainerStyle={{paddingBottom:28,paddingLeft:28,paddingRight:28}}
          onDismiss={_ => {
            this.setState({cameraVolumeModal: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
          
              callback: _ => {
                this.setState({cameraVolumeModal: false});
              },
              btnStyle: {
    background: 'rgba(0,0,0,0.04)',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
    width:(newWidth  -72) / 2,
   
  },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okCameraPos',
              callback: _ => {
              
                this._onSetCameraVolume()
              },
              btnStyle: {
   backgroundColor: '#12AA9C',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
     width:(newWidth  -72) / 2,
    
  },
            },
          ]}
        >
<View style={{marginTop:27,marginBottom:25}}>
  <SlideGear onValueChange={this._onCameraVolumeChange} 
  value={this.state.cameraVolume}
  options={this.cameraVolumeOptions} 
  containerStyle={{   marginLeft: 0,
            marginRight: 0}}
            minimumTrackTintColor={'#12AA9C'}/>
  

 <View style={{display:'flex',flexDirection:'row',justifyContent:'space-between',
 fontSize:12}}><Text style={{color:'#B2B2B2'}}>0%</Text><Text style={{color:'#B2B2B2'}}>100%</Text></View>
</View>
        </MessageDialog>
        <MessageDialog
          title={stringsTo('setting_reset')}
          visible={this.state.showRebootModal}
          message={stringsTo('setting_reset_msg')}
          messageStyle={{marginBottom: 20, paddingLeft: 10, paddingRight: 10}}
          canDismiss={true}
          onDismiss={() => {
            this.setState({showRebootModal: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelCameraReset',
              callback: _ => {
                this.setState({showRebootModal: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okCameraReset',
              callback: _ => {
                showLoading(stringsTo('setting_reset'), true);
                this.setState({showRebootModal: false});
                LetDevice.sendAction(false, LetDevice.deviceID, '10010', JSON.stringify({}))
                  .then(() => {
                    console.log(' Reboot  then-> Reboot');
                    showLoading(stringsTo('setting_reset'), false);
                    showToast(I18n.t('settings_set_success'));
                  })
                  .catch(error => {
                    showLoading(stringsTo('setting_reset'), false);
                    showToast(stringsTo('operationFailed'));
                    console.log('Reboot error ' + JSON.stringify(error));
                  });
                //重启 这个物模型还在调试
                // LetDevice.sendDeviceServerRequest('Reboot', JSON.stringify({}))
                //   .then(data => {
                //     console.log(' Reboot  then-> Reboot' + data);
                //     showLoading(stringsTo('setting_reset'), false);
                //     showToast(I18n.t('settings_set_success'));
                //   })
                //   .catch(error => {
                //     showLoading(stringsTo('setting_reset'), false);
                //     showToast(stringsTo('operationFailed'));
                //     console.log('Reboot error ' + error);
                //   });
              },
            },
          ]}
        />
      </View>
    );
  }

  _assignRoot = component => {
    this.cameraGLView = component;
  };

  _isShareUser() {
    if (LetDevice.isShareUser) {
      showToast(stringsTo('shareUser_tip'));
      return true;
    }
    return false;
  }

  /*  _onPrepared(data) {
    console.log(`_onPrepared code : ${data}  `);
    this.cameraGLView.start();
  } */
  async getSysProperty() {
    // showLoading(stringsTo('commLoadingText'), true);
    // 三个一起就报错。注意注意

    // const data1006 = await LetDevice.getSingleProperty('10006').catch(error => {
    //   console.log(JSON.stringify(error), 'error10006');
    // });
    // const data1001 = await LetDevice.getSingleProperty('10001').catch(error => {
    //   console.log(JSON.stringify(error), 'error10001');
    // });
    // const data1012 = await LetDevice.getSingleProperty('10009').catch(error => {
    //   console.log(JSON.stringify(error), 'error10009');
    // });
    // console.log('data10061', data1006, data1001, data1012);
    // 使用接口获取哦
    const params1 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10009,
        method: 'sync'
      },
      Method: 'POST',
    };
    const params2 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10001,
        method: 'sync'
      },
      Method: 'POST',
    };
    const params3 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10005,
        method: 'sync'
      },
      Method: 'POST',
    };
    Promise.all([LetIMIIotRequest.sendUserServerRequest(params1, true), 
      LetIMIIotRequest.sendUserServerRequest(params2, true), 
      LetIMIIotRequest.sendUserServerRequest(params3, true)]).then(res => {
        this.setState({
          statusLightValue: res[0]?.value?.value,
          isSleep: !res[1] ? false : !res[1]?.value?.value,
          fullColor: res[2]?.value?.value
        });
        showLoading(false);
      }).catch(() => {
        this.setState({
          isSleep: false
        })
        showLoading(false);
      })

    // const data12 = await Promise.all([
    //   LetDevice.getSingleProperty('10009'),
    //   LetDevice.getSingleProperty('10001'),
    //   LetDevice.getSingleProperty('10005'),
    // ]).catch(error => {
    //   console.log(JSON.stringify(error), 'error+++++++');
    // });

    // if (data12) {
    //   console.log(JSON.stringify(data12), 'data121111');

    //   data12?.forEach(item => {
    //     //状态灯
    //     if (`${item?.thingId}` === '10009') {
    //       console.log('指示灯', item?.value?.value);

    //       this.setState({statusLightValue: item?.value?.value});
    //     }
    //     if (`${item?.thingId}` === '10001') {
    //       //是否休眠
    //       console.log('10001', item?.value?.value)
    //       this.setState({isSleep: !item?.value?.value});
    //     }
    //     //微光全彩
    //     if (`${item?.thingId}` === '10005') {
    //       console.log('10005', item?.value?.value)
    //       this.setState({fullColor: item?.value?.value});
    //     }
    //   });
    // }

    // showLoading(false);

    /*  LetDevice.getSingleProperty('10001')
      .then(data => {
        //0休眠 1关闭
        console.log('设备休眠--------SleepStatus' + JSON.stringify(data), typeof data);

        this.setState({isSleep: data?.value?.value});
      })
      .catch(error => {
        console.log('设备休眠error' + JSON.stringify(error));
        showToast(stringsTo('commLoadingFailText'));
      }); */
  }
  _onCameraVolumeChange = (value) => {
    console.log('摄像机音量变化', value);
    this.setState({cameraVolume: value});
  }
  _onSetCameraVolume=()=>{
   showLoading(stringsTo('setting_in_progress'), true);
    const paramJson = JSON.stringify({msg_id: '10030', value: this.state.cameraVolume});
     LetDevice.setProperties(true, LetDevice.deviceID, '10030', paramJson).catch(e => {
      showToast(stringsTo('operationFailed'));
      this.setState({cameraVolume: this.initCameraVolume});
    }).then(()=>{
        this.setState({cameraVolumeModal: false});
    }).finally(()=>{
      showLoading("",false);
    })
  }

}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  functionSettingStyle: {
    color: '#0000007F',
    fontSize: 12,
    marginTop: 23,
    marginLeft: 14,
  },
});
