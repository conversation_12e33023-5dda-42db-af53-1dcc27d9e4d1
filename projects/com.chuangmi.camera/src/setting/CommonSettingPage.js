import React from 'react';

import {StyleSheet, View, ScrollView, Dimensions, TextInput, Keyboard, Text} from 'react-native';

import {MessageDialog, showToast, RoundedButtonViewNew} from '../../../../imilab-design-ui';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import {XText, XView} from 'react-native-easy-app';
import {IMIGotoPage, LetDevice, LetIMIIotRequest, LetIDeviceManager} from '../../../../imilab-rn-sdk';
import {METHOD} from '../../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import DeviceHotSpotUtils from '../utils/DeviceHotSpotUtils';
import IMIPackage from '../../../../imilab-rn-sdk/native/local-kit/IMIPackage';
const {width, height} = Dimensions.get('window');
const deviceHotSpotUtil = DeviceHotSpotUtils.getInstance();
import Orientation from 'react-native-orientation';
import {customJsonParse} from '../utils/GenericUtils';
/**
 * 通用设置页面
 */
let CHECK_UPDATE_URL = 'link://app/pages/toDeviceUpgradePage'; //检查更新
let DEVICE_SHARE_URL = 'link://app/pages/toDeviceSharePage'; //共享
let COMMON_HELP_URL = 'link://feedback/pages/toHelpAndFeedbackPage'; //帮助与反馈
let COMMON_QUESTION_URL = 'link://feedback/pages/toCommonQuestionPage'; //常见问题

const tag = 'CommonSettingPage';
export default class CommonSettingPage extends BaseDeviceComponent {
  componentDidMount() {
    LetDevice.registerInfoChangeListener(data => {
      console.log(`_doDevInfoText eventData : ${data} + data ${JSON.stringify(data)}`);
    });
    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.getFirmwareInfo();
    });
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      keyBoardHeight: 0, //软键盘高度
      modalVisible: false, //弹出聊天输入
      modalDeleteVisible: false, //是否删除
      deviceName: LetDevice.devNickName,
      showCircle: false, //是否显示升级
    };
    this.inputData = LetDevice.devNickName; //输入框内容
    this._onChangeText = this._onChangeText.bind(this); //监听输入变化

    console.log(tag);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    this._onResumeListener && this._onResumeListener.remove();
  }

  UNSAFE_componentWillMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow.bind(this));
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide.bind(this));
    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      //app页面回到插件
      NavigationBar.setBarStyle('dark-content'); // 修改从云存购买界面返回状态栏显示白色字体问题

      //有新的升级版本，从升级页面回来需要重新检查是否升级到最新固件
      if (this.state.showCircle) {
        this.getFirmwareInfo();
      }
    });
  }

  renderBottomDeleteBtn() {
    return (
      <View style={{width: '100%', height: 116, position: 'absolute', bottom: 0,
      backgroundColor:'#FFF',justifyContent:'center'}}>


        <RoundedButtonViewNew
          buttonText={stringsTo('comm_setting_remove_device')}
          disabled={false}
          buttonStyle={{backgroundColor: 'rgba(0,0,0,0.04)', margin: 15,borderRadius:24}}

          buttonTextStyle={{color: '#EB614B'}}
          onPress={() => {
            /*if (LetDevice.isShareUser){
                                                    showToast(stringsTo('shareUser_tip'));
                                                    return;
                                                }*/

            this.setState({
              modalDeleteVisible: true,
            });
          }}
          accessibilityLabel={'comm_setting_remove_device'}
        />
      </View>
    );
  }
  render() {
    return (
      <XView style={styles.container}>
        <NavigationBar
          title={stringsTo('commTitleSettingText')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => this.props.navigation.pop(),
              accessibilityLabel: 'comm_setting_go_back',
            },
          ]}
          right={[]}
        />
        {this.renderBottomDeleteBtn()}
        <ScrollView showsVerticalScrollIndicator={false} style={{backgroundColor: '#FFF', marginBottom: 116}}>
          {/*        <Separator/>*/}
          <Text
            style={{
              color: '#00000080',
              fontSize: 12,
              paddingLeft: 15,
              paddingTop: 5,
              width: '100%',
              backgroundColor: '#ffffff',
            }}>
            {stringsTo('feature_set')}
          </Text>
          <ListItem
            title={stringsTo('popo_setting_camera_text')}
            onPress={() => {
              this.props.navigation.push('IMICameraSettingVC');
            }}
            accessibilityLabel={'setting_camera'}
          />
          <ListItem
            title={stringsTo('alarmSettingText')}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }

              if (LetDevice.model == 'a1MSKK9lmbs') {
                this.props.navigation.push('HouseKeepSettingV2');
              } else if (
                LetDevice.model == 'a1FKrifIRwH' ||
                LetDevice.model == 'a1Ikkj5vsiK' ||
                LetDevice.model == 'a1znn6t1et8'
              ) {
                // 026 021E01 036 进入新的看家界面 原生项目转RN
                this.props.navigation.push('HouseKeepOldNativeSetting');
              } else {
                this.props.navigation.push('HouseKeepSetting');
              }
            }}
            accessibilityLabel={'setting_housekeeping_assistant'}
          />
          <ListItem
            title={stringsTo('popo_setting_storage_text')}
            onPress={() => {
              // if (LetDevice.isShareUser) {
              //   showToast(stringsTo('shareUser_tip'));
              //   return;
              // }
              this.props.navigation.push('SdCardNewPage');
            }}
            accessibilityLabel={'setting_storage'}
          />
          {/* <ListItem
            title={stringsTo('local_device_connect_wifi')}
            // hide={!deviceHotSpotUtil.isDirectConnectMode()} todo 记得关闭
            onPress={() => {
              this.props.navigation.push('WIFISettingPage');
            }}
            accessibilityLabel={'setting_wifi'}
          /> */}
          <View style={{backgroundColor: 'rgba(0,0,0,0.15)', height: 1,marginLeft:14,
            marginRight:14,marginBottom:20,marginTop:20}} />
          <Text
            style={{
              color: '#00000080',
              fontSize: 12,
              paddingLeft: 15,
              paddingTop: 5,
              width: '100%',
              backgroundColor: '#ffffff',
            }}>
            {stringsTo('comm_setting_title')}
          </Text>

          <ListItem
            title={stringsTo('device_name')}
            value={this.state.deviceName}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              // bugJDDF-529
              Orientation.lockToPortrait();
              this.setState({
                modalVisible: true,
              });
            }}
            accessibilityLabel={'device_name'}
          />
          {!LetDevice.isShareUser && <ListItem
            title={stringsTo('shared_setting')}
            hide={deviceHotSpotUtil.isDirectConnectMode()}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              IMIGotoPage.startNativeCommShareDeviceListPage(LetDevice.deviceID);
            }}
            accessibilityLabel={'shared_setting'}
          />}
          <ListItem
            title={stringsTo('check_update')}
            hide={deviceHotSpotUtil.isDirectConnectMode()}
            showCircle={this.state.showCircle}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              this.props.navigation.push('UpgradePage');
              // IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, CHECK_UPDATE_URL);
            }}
            accessibilityLabel={'check_update'}
          />
          <ListItem
            title={stringsTo('help_callback')}
            hide={deviceHotSpotUtil.isDirectConnectMode()}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              // IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_HELP_URL);
              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_QUESTION_URL);
            }}
            accessibilityLabel={'help_callback'}
          />
          {/* <ListItem
            title={stringsTo('comm_setting_faq')}
            hide={deviceHotSpotUtil.isDirectConnectMode()}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_QUESTION_URL);
            }}
            accessibilityLabel={'comm_setting_faq'}
          /> */}
           <ListItem
            title={stringsTo('angelMoreSetting')}
            hide={deviceHotSpotUtil.isDirectConnectMode()}
            onPress={() => {
              this.props.navigation.push('MoreSetting');
            }}
            accessibilityLabel={'moreSetting'}
          />

          <MessageDialog
            title={stringsTo('confirm_deletion_device')}
            visible={this.state.modalDeleteVisible}
            canDismiss={true}
            onDismiss={() => {
              this.setState({modalDeleteVisible: false});
            }}
            buttons={[
              {
                text: I18n.t('cancel'),
                accessibilityLabel: 'cancelDelete',
                callback: _ => {
                  this.setState({modalDeleteVisible: false});
                },
              },
              {
                text: I18n.t('ok_button'),
                accessibilityLabel: 'okDelete',
                callback: _ => {
                  this.setState({modalDeleteVisible: false});
                  //  showLoading(stringsTo('preset_sleep_set'),false);
                  this.DeleteDevice();
                },
              },
            ]}
          />
        </ScrollView>
        {this._updateDeviceNameDialog()}
      </XView>
    );
  }
  /**
   * 修改设备名称的弹框
   * @private
   */
  _updateDeviceNameDialog() {
    return (
      <View>
        <MessageDialog
          title={stringsTo('update_device_name')}
          visible={this.state.modalVisible}
          bottomStyle={{bottom: isAndroid() ? 0 : this.state.keyBoardHeight}}
          canDismiss={true}
          onDismiss={() => {
            this.setState({modalVisible: false});
          }}
          buttons={[
            {
              text: stringsTo('cancel'),
              accessibilityLabel: 'cancelUpdateDeviceName',
              callback: _ => {
                this.setState({modalVisible: false});
              },
            },
            {
              text: stringsTo('ok_button'),
              accessibilityLabel: 'okUpdateDeviceName',
              callback: _ => {
                this.setState({modalVisible: false});
                this.updateDeviceName();
              },
            },
          ]}>
          <View
            style={{
              width: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              paddingVertical: 10,
            }}>
            <TextInput
              style={{
                fontSize: 15,
                borderRadius: 7,
                height: 50,
                width: width * 0.8,
                backgroundColor: '#F1F1F1',
                paddingLeft: 20,
                color: 'black',
              }}
              defaultValue={this.state.deviceName}
              placeholderTextColor={'#B2B2B2'}
              placeholder={stringsTo('voice_for_enter_name')}
              returnKeyType="done"
              clearButtonMode="while-editing"
              enablesReturnKeyAutomatically={true}
              editable={true}
              autoFocus={true}
              keyboardType="default"
              onChangeText={this._onChangeText}
            />
          </View>
        </MessageDialog>
      </View>
    );
  }
  _onChangeText(inputData) {
    //把获取到的内容，设置给showValue
    this.inputData = inputData;
  }
  _keyboardDidShow(e) {
    console.log('keyboardDidShow----', e);
    console.log('keyboardDidShow11----', e.endCoordinates.height);
    this.setState({
      keyBoardHeight: e.endCoordinates.height,
    });
  }
  _keyboardDidHide() {
    this.setState({
      keyBoardHeight: 0,
      // modalVisible:false
    });
  }
  onRequestClose() {
    this.setState({
      modalVisible: false,
    });
  }
  updateDeviceName() {
    if (this.inputData.length === 0) {
      showToast(stringsTo('input_name'));
      return;
    }
    //正常模式也兼容，app端会调用'api/app_device/nickname'接口
    let newName = this.inputData ? this.inputData : LetDevice.devNickName;
    if (newName.length > 15) {
      showToast(stringsTo('max_device_name'));
      return;
    }
    LetIDeviceManager.renameDeviceName(LetDevice.deviceID, newName)
      .then(res => {
        showToast(stringsTo('settings_set_success'))
        this.setState({deviceName: newName});
        LetDevice.setDevNickName(newName);
      })
      .catch(error => {
         
        this.inputData = LetDevice.devNickName;
        showToast(I18n.t('operationFailed'));
      });
  }

  //   获取设备是否需要升级
  getFirmwareInfo() {
    const params = {
      Path: 'v1.0/imilab-01/ota/getUpgradeFirmware',
      ParamMap: {
        iotId: LetDevice.deviceID,
      },
      Method: 'GET',
    };
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        let needUpgrade = false;
        if (item.length > 0) {
          const firmwareInfo = customJsonParse(item[0]);
          if (
            firmwareInfo?.status == 0 ||
            firmwareInfo?.status == 6 ||
            firmwareInfo?.status == 7 ||
            firmwareInfo?.status == 8
          ) {
            needUpgrade = true;
          }
        }
        this.setState({
          showCircle: needUpgrade,
        });
      })
      .catch(e => {
        console.log('========', JSON.stringify(e));
      });
  }
  compareVersion(curVersion, newVersion) {
    if (this.isEmpty(newVersion) || this.isEmpty(curVersion)) {
      return false;
    } else if (newVersion.equals(curVersion)) {
      return false;
    }

    let newSpilUnderLine = newVersion.split('_');
    let curSpilUnderLine = curVersion.split('_');
    if (newSpilUnderLine.length < 3 || newSpilUnderLine.length < 3) {
      return false;
    }
    let newSpilComma = newSpilUnderLine[1].split('.');
    let curSpilComma = curSpilUnderLine[1].split('.');
    if (newSpilComma.length < 3 || curSpilComma.length < 3) {
      return false;
    }

    let newNum =
      parseInt(newSpilComma[0]) * 1000 +
      parseInt(newSpilComma[1]) * 300 +
      parseInt(newSpilComma[2]) * 10 +
      parseInt(newSpilUnderLine[2]);
    let curNum =
      parseInt(curSpilComma[0]) * 1000 +
      parseInt(curSpilComma[1]) * 300 +
      parseInt(curSpilComma[2]) * 10 +
      parseInt(curSpilUnderLine[2]);
    if (newNum > curNum) {
      return true;
    } else {
      return false;
    }
  }

  isEmpty(obj) {
    if (typeof obj === 'undefined' || obj == null || obj === '') {
      return true;
    } else {
      return false;
    }
  }

  DeleteDevice() {
    LetIDeviceManager.removeDevice(LetDevice.deviceID)
      .then(res => {
        IMIGotoPage.exit();
      })
      .catch(e => {
        console.log(JSON.stringify(e), 'del message');
      });
    // if (IMIPackage.minApiLevel >= 10011) {
    //   //正常模式也兼容，app端会调用'api/app_device/unbind'接口
    //  ;
    //   return;
    // }

    // const params = {
    //   Path: 'api/app_device/unbind',
    //   Method: METHOD.POST,
    //   ParamMap: [
    //     {
    //       iotId: LetDevice.deviceID,
    //       isAli: true,
    //     },
    //   ],
    // };
    // console.log('传值params--', params);
    // LetIMIIotRequest.sendUserServerRequest(params)
    //   .then(data => {
    //     IMIGotoPage.exit();
    //   })
    //   .catch(error => {});
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  textInputContainer: {
    position: 'absolute',
    height: 45,
    backgroundColor: '#ffffff',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  dialogButtons: {
    // 按钮容器
    height: 40, // 底部按钮的高度
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 55,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    width: width * 0.9,
  },
});
