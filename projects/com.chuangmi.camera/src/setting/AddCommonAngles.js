import {View, StyleSheet} from 'react-native';
import React, {useState, useRef, useLayoutEffect} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {stringsTo} from '../../../../globalization/Localize';
import AddAnglesPlayer from '../live/AddAnglesPlayer';

const AddCommonAngles = props => {
  const {anglesName = ''} = props.route.params;
  const navBarAddCommonAngles = useRef();
  const [navBarAddCommonAnglesBarHeight, setNavBarAddCommonAnglesBarHeight] = useState(0);
  useLayoutEffect(() => {
    console.log(
      'this.props.navBarAddCommonAngles?.getNavigationBarHeight',
      navBarAddCommonAngles.current?.getNavigationBarHeight(),
    );

    setNavBarAddCommonAnglesBarHeight(navBarAddCommonAngles.current?.getNavigationBarHeight());
  }, []);
  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo('common_angles')}
        ref={component => (navBarAddCommonAngles.current = component)}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'cruise_control',
          },
        ]}
        right={[]}
      />

      <View style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 不要管为啥重新实例化一个播放器，因为用同一个播放器引用太严重不好改，到时候CameraPlayerPage页面改什么全都复制过来就没问题了 */}
        <AddAnglesPlayer
          showNavBar={false}
          showPlayItem={false}
          showPtzCenterControl={true}
          showBottomLayout={false}
          anglesName={anglesName}
          isAddCommonAngles={true}
          navBarAddCommonAnglesBarHeight={navBarAddCommonAnglesBarHeight}
          {...props}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
});

export default AddCommonAngles;
