import React, {useRef, useState, useEffect} from 'react';
import {View, Text, StyleSheet, FlatList} from 'react-native';
import PropTypes from 'prop-types';
import {isIos} from '../../../../../imilab-rn-sdk/utils/Utils';

/**
 * @description 滚动列表选择器 (使用FlatList实现)
 * @property {array<string>} data - 选择列表的数据源
 * @property {number} selectedIndex - 当前选中的下标
 * @property {string} unit - 单位，如年，月，日等
 * @property {function} onValueChange - 选择项变化的回调
 * @property {number} optionsWrapperWidth - 控件的宽度
 * @property {number} optionHeight - 每一个选项的高度
 * @property {string} highlightBorderColor - 被选中项上下两根边框线的颜色
 * @property {number} highlightBorderWidth - 被选中项上下两根边框线的粗细
 * @property {string} activeItemColor - 被选中项的字体颜色
 * @property {string} itemColor - 常态项的字体颜色
 * @property {number} activeItemFontSize - 被选中项的字体大小
 * @property {number} itemFontSize - 常态项的字体大小
 */
/*
 optionsCount: 3,
  optionsWrapperWidth: 100,
  optionHeight: 60,
  data: [],
  selectedIndex: 0,
  unit: '',
  highlightBorderWidth: 1,
  highlightBorderColor: '#333',
  activeItemColor: '#222121',
  itemColor: '#B4B4B4',
  wrapperBackground: '#fff',

*/
const CustomSpinner = props => {
  const {
    data = [],
    selectedIndex = 0,
    unit = '',
    onValueChange,
    optionsWrapperWidth = 100,
    optionHeight = 60,
    highlightBorderWidth = 1,
    highlightBorderColor = '#333',
    itemColor = '#B4B4B4',
    activeItemColor = '#222121',
    wrapperBackground = '#fff',
    activeItemFontSize,
    itemFontSize,
    optionsCount = 3,
  } = props;

  // 创建包含空白项的数据源
  const createEmptyArray = arrayLength => {
    return new Array(Math.floor(arrayLength)).fill(' ');
  };

  const getExtendedData = () => {
    return createEmptyArray(optionsCount / 2).concat(data.concat(createEmptyArray(optionsCount / 2)));
  };

  const [extendedData, setExtendedData] = useState(getExtendedData());
  const [currentIndex, setCurrentIndex] = useState(selectedIndex + Math.floor(optionsCount / 2));
  const flatListRef = useRef(null);
  const scrolling = useRef(false);
  const momentumScrolling = useRef(false);
  const timer = useRef(null);

  // 滚动到指定索引位置
  const scrollToIndex = index => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: index * optionHeight,
        animated: true,
      });
    }
  };

  // 组件挂载后滚动到初始选中位置
  useEffect(() => {
    setTimeout(() => {
      scrollToIndex(selectedIndex);
    }, 0);

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, []);

  // 当selectedIndex或data变化时更新
  useEffect(() => {
    if (props.selectedIndex !== selectedIndex && data[props.selectedIndex] !== undefined) {
      setCurrentIndex(props.selectedIndex + Math.floor(optionsCount / 2));
      setTimeout(() => {
        scrollToIndex(props.selectedIndex);
      }, 0);
    }

    if (props.data.length !== data.length) {
      const newExtendedData = getExtendedData();
      setExtendedData(newExtendedData);

      if (!newExtendedData[currentIndex]) {
        setCurrentIndex(props.data.length + Math.floor(optionsCount / 2));
      }
    }
  }, [props.selectedIndex, props.data]);

  // 选择选项
  const selectOption = y => {
    const selectedIndex = Math.round(y / optionHeight);
    const selectedIndexWithEmptyBlock = selectedIndex + Math.floor(optionsCount / 2);

    setCurrentIndex(selectedIndexWithEmptyBlock);
    scrollToIndex(selectedIndex);

    if (onValueChange) {
      onValueChange(extendedData[selectedIndexWithEmptyBlock], selectedIndex);
    }
  };

  // 滚动结束事件处理
  const handleMomentumScrollEnd = event => {
    const y = event.nativeEvent.contentOffset.y;

    if (timer.current) {
      clearTimeout(timer.current);
    }

    // iOS需要特殊处理，避免重复触发
    if (isIos()) {
      if (!scrolling.current && momentumScrolling.current) {
        selectOption(y);
      }
    } else {
      selectOption(y);
    }

    momentumScrolling.current = false;
  };

  // 滚动开始事件处理
  const handleMomentumScrollBegin = () => {
    momentumScrolling.current = true;

    if (timer.current) {
      clearTimeout(timer.current);
    }
  };

  // 拖拽开始事件处理
  const handleScrollBeginDrag = () => {
    scrolling.current = true;

    if (timer.current) {
      clearTimeout(timer.current);
    }
  };

  // 拖拽结束事件处理
  const handleScrollEndDrag = event => {
    const y = event.nativeEvent.contentOffset.y;
    scrolling.current = false;

    if (timer.current) {
      clearTimeout(timer.current);
    }

    timer.current = setTimeout(() => {
      if (!scrolling.current && !momentumScrolling.current) {
        selectOption(y);
      }
    }, 10);
  };

  // 渲染列表项
  const renderItem = ({item, index}) => {
    const isSelected = index === currentIndex;
    return (
      <View style={[{height: optionHeight, flexDirection: 'row'}, styles.itemWrapper]}>
        <Text
          style={[
            {color: itemColor, fontSize: itemFontSize !== undefined ? itemFontSize : 16},
            isSelected && {
              color: activeItemColor,
              fontSize: activeItemFontSize !== undefined ? activeItemFontSize : 23,
            },
          ]}>
          {item}
        </Text>
        {isSelected ? (
          <Text style={{marginLeft: 10, color: props.activeItemColor || '#4A70A5', fontSize: 10, fontWeight: 'bold'}}>
            {unit}
          </Text>
        ) : null}
      </View>
    );
  };

  // 高亮选中项的样式
  const highlightVarStyle = {
    top: Math.floor(optionsCount * 0.5) * optionHeight,
    height: optionHeight,
    borderTopWidth: highlightBorderWidth,
    borderBottomWidth: highlightBorderWidth,
    borderTopColor: highlightBorderColor,
    borderBottomColor: highlightBorderColor,
  };

  return (
    <View
      style={[
        {
          height: optionHeight * optionsCount,
          width: optionsWrapperWidth,
          backgroundColor: wrapperBackground,
        },
        styles.container,
      ]}>
      <View style={[highlightVarStyle, styles.highlight]} />
      <FlatList
        data={extendedData}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        showsVerticalScrollIndicator={false}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        onMomentumScrollBegin={handleMomentumScrollBegin}
        onScrollEndDrag={handleScrollEndDrag}
        onScrollBeginDrag={handleScrollBeginDrag}
        bounces={false}
        initialNumToRender={extendedData.length}
        getItemLayout={(_, index) => ({
          length: optionHeight,
          offset: optionHeight * index,
          index,
        })}
        ref={flatListRef}
      />
    </View>
  );
};

CustomSpinner.propTypes = {
  optionsCount: PropTypes.number,
  optionsWrapperWidth: PropTypes.number,
  optionHeight: PropTypes.number,
  data: PropTypes.array,
  selectedIndex: PropTypes.number,
  unit: PropTypes.string,
  highlightBorderWidth: PropTypes.number,
  highlightBorderColor: PropTypes.string,
  activeItemColor: PropTypes.string,
  itemColor: PropTypes.string,
  wrapperBackground: PropTypes.string,
  activeItemFontSize: PropTypes.number,
  itemFontSize: PropTypes.number,
};

// CustomSpinner.defaultProps = {
//   optionsCount: 3,
//   optionsWrapperWidth: 100,
//   optionHeight: 60,
//   data: [],
//   selectedIndex: 0,
//   unit: '',
//   highlightBorderWidth: 1,
//   highlightBorderColor: '#333',
//   activeItemColor: '#222121',
//   itemColor: '#B4B4B4',
//   wrapperBackground: '#fff',
// };

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
  },
  itemWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  highlight: {
    width: '100%',
    position: 'absolute',
  },
});

export default CustomSpinner;
