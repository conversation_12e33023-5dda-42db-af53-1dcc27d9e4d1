import React from 'react';

import {StyleSheet, View, Image, Text, TouchableWithoutFeedback, Dimensions, BackHandler} from 'react-native';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';

import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';

import {LetDevice, LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import {showLoading, showToast} from '../../../../imilab-design-ui';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import IMIToast from '../../../../imilab-design-ui/src/widgets/IMIToast';
const screen_width = Dimensions.get('window').width;

/**
 * 夜视功能设置页面
 */

export default class NightFunctionPage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props, context) {
    super(props, context);
    this.state = {
      nightMode: 2,
      fullColor: false
    };
    this.loading1 = false;
    this.loading2 = false;
  }
  componentDidMount() {
    console.log('nightFunctionPage --', this.props.route.params.nightValue);
    if (this.props.route.params?.nightValue !== undefined) {
      this.setState({
        nightMode: this.props.route.params.nightValue,
      });
    }
    this.getNightState();
  }
  // 更新夜视状态
  getNightState = () => {
    // 使用接口获取哦
    setTimeout(() => {
      showLoading(stringsTo('commLoadingText'), true);
    })
    const params = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10003,
        method: 'sync'
      },
      Method: 'POST',
    };
    const params3 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10005,
        method: 'sync'
      },
      Method: 'POST',
    };
    this.loading1 = true;
    this.loading2 = true;
    LetIMIIotRequest.sendUserServerRequest(params3, true).then(data3 => {
      this.setState({
        fullColor: data3?.value?.value,
      });
    }).catch(error => {
    }).finally(() => {
      this.loading1 = false;
      if (!this.loading2) {
        showLoading(false);
      }
    })
    LetIMIIotRequest.sendUserServerRequest(params, true).then(value => {
        console.log('夜视模式getPropertyCloud$$$$$$$$$$$$' + value);
        console.log('getPropertyCloud$$$$$$$$$$$$' + value);

        this.setState({
          nightMode: value?.value?.value,
        });
      })
      .catch(error => {
        showToast(I18n.t('commLoadingFailText'));
        console.log('夜视模式', JSON.stringify(error));
      }).finally(() => {
        this.loading2 = false;
        if (!this.loading1) {
          showLoading(false);
        }
      });
  };

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }
  componentWillUnmount() {
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
    showLoading(false);
  }

  onBackHandler = () => {
    // console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
    // if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    // } else {
    // }
    // return false;
  };

  // 返回上一页
  _onPressBack = () => {
    if (this.props.route.params.callback) {
      console.log('返回传值', this.state.nightMode);
      let tempStr;
      if (this.state.nightMode == 1) {
        //黑白夜视 改成打开
        tempStr = stringsTo('full_color_vision_title');
      } else if (this.state.nightMode == 0) {
        // 全彩夜视 改成关闭
        tempStr = stringsTo('black_white_vision_title'); // 已开启
      } else if (this.state.nightMode == 2) {
        // 智能夜视 改成自动
        tempStr = stringsTo('fullColor_smart_tit');
      }

      console.log('返回值---', tempStr);
      this.props.route.params.callback(tempStr);
    }
    this.props.navigation.pop();
  };

  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('setttings_infared')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        <Separator />
        <View style={{width: '100%', height: 210}}>
          {this._renderItemOne()}
          {this._renderItemTwo()}
          {this._renderItemThe()}
        </View>

        {/* <View style={{top: 20, height: 130, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-around'}}>
          {[
            {text: I18n.t('full_color_vision_title'), uri: require('../../resources/images/full_color_img.png')},
            {
              text: I18n.t('black_white_vision_title'),
              uri: require('../../resources/images/full_color_black_white_img.png'),
            },
          ].map((item, i) => {
            return (
              <View key={i} style={{width: 145, height: 65}}>
                <Image key={i} style={{width: 145, height: 65, borderRadius: 10}} source={item.uri} />
                <Text style={{fontSize: 10, color: '#828282', top: 5, lineHeight: 15}}>{item.text}</Text>
              </View>
            );
          })}
        </View> */}
  <View style={{backgroundColor: 'rgba(0,0,0,0.15)', height: 1,marginLeft:14,
            marginRight:14,marginBottom:20,marginTop:20}} />
        <ListItmeWithSwitch
              disabled={this.state.nightMode == 0}
              title={stringsTo('settings_light_full_color_title')}
              /*  hide={!showLightFullColor} 记得打开 todo*/
              subtitle={stringsTo('settings_light_full_color_subtitle')}
              value={this.state.fullColor}
              onValueChange={value => {
                const params = {value};
                IMIToast.hideToast()
                showLoading(stringsTo('commWaitText'), true);
                LetDevice.setProperties(true, LetDevice.deviceID, '10005', JSON.stringify(params))
                  .then(() => {
                    showToast(stringsTo('settings_set_success'))
                    this.setState({
                      fullColor: value,
                    });
                  })
                  .catch(error => {
                    console.log(error, '错误了');
                    this.setState({
                      fullColor: this.state.fullColor,
                    });
                    showToast(I18n.t('operationFailed'));
                  })
                  .finally(() => {
                    showLoading(false);
                  });
              }}
              accessibilityLabel={['camera_full_color_off', 'camera_full_color_on']}
            />
      </View>
    );
  }
  _renderItemOne() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            console.log('全彩夜视3');
            // 因为物模型是按照060a02，所以只有关闭自动，开启，微光全彩改成关闭
            this._onSelectedItem(2);
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.nightMode == 2 ? '#12AA9C' : '#000000',
                }}>
                {/*  {stringsTo('full_color_vision_title')}  因为物模型是按照060a02，所以只有关闭自动，开启，微光全彩改成关闭*/}
                {stringsTo('fullColor_title3')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {stringsTo('fullColor_subTit')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.nightMode == 2 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemTwo() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            console.log('2');
            //因为物模型是按照060a02，所以只有关闭自动，开启，黑白改成改成打开
            this._onSelectedItem(0);
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.nightMode == 0 ? '#12AA9C' : '#000000',
                }}>
                {/*   {stringsTo('black_white_vision_title')}因为物模型是按照060a02，所以只有关闭自动，开启，黑白改成改成打开 */}
                {stringsTo('fullColor_title2')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {stringsTo('fullColor_black_subTit')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.nightMode == 0 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemThe() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            console.log('智能夜视4');
            //因为物模型是按照060a02，所以只有关闭自动，开启，智能夜视4改成改成自动
            this._onSelectedItem(1);
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.nightMode == 1 ? '#12AA9C' : '#000000',
                }}>
                {/*  {stringsTo('fullColor_smart_tit')} //因为物模型是按照060a02，所以只有关闭自动，开启，智能夜视4改成改成自动*/}
                {stringsTo('fullColor_title1')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {stringsTo('fullColor_smart_subTit')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.nightMode == 1 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _onSelectedItem(value) {
    console.log('夜视选中value--', value);

    // this.setState({nightMode: value},callback=>{
    //     this._onPressBack()
    // });
    IMIToast.hideToast()
    showLoading(stringsTo('commWaitText'), true);
    //let params = {DayNightMode: value};
    const params = {msg_id: 10003, value};
    const paramJson = JSON.stringify(params);
    console.log('夜视选中value测试值=-hhhh--', JSON.stringify(params));
    //LetDevice.setProperties(true, LetDevice.deviceID, 10003, paramJson)
    LetDevice.setProperties(true, LetDevice.deviceID, '10003', paramJson)
      .then(() => {
        showLoading(false);
        this.setState({nightMode: value}, callback => {
          // this._onPressBack();
          showToast(stringsTo('settings_set_success'))
        });
      })
      .catch(error => {
        console.log('失败----', error);
        console.log('失败----', error, this.state.nightMode, value);
        this.setState({nightMode: this.state.nightMode});
        showToast(I18n.t('operationFailed'));
        showLoading(false);
      });

    IMILogUtil.uploadClickEventValue({DayNightMode: value.toString()});
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
