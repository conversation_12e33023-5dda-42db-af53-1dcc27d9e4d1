import React from 'react';

import {BackHandler, Dimensions, FlatList, Image, StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';

import {colors, imiThemeManager} from '../../../../imilab-design-ui';
import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import IMIHost from '../../../../imilab-rn-sdk/native/local-kit/IMIHost';
import {SOUND_AND_LIGHT_WARNING} from "../../constants/Spec";
import { LetDevice } from '../../../../imilab-rn-sdk';


const screen_width = Dimensions.get('window').width;

//档期支持的提示音数组
const voiceArr = [
  {
    //"无",
    replyName: stringsTo('voice_for_wu'),
    replyCNUrl: 'null', //大陆
    replyUSUrl: 'null', //美国
    replySGUrl: 'null', //新加坡
    replyDEUrl: 'null', //欧洲
    replRUUrl: 'null', //俄罗斯
    replIDUrl: 'null', //印度
    index: 1,
    time: '5',
  },
  {
    //"警报",
    replyName: stringsTo('voice_for_warning'),
    replyCNUrl: '1-alarm', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //欧洲 德国
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //印度
    index: 2,
    time: '5',
  },
  {
    //"叮咚声",
    replyName: stringsTo('voice_for_dingdong'),
    replyCNUrl: '2-ding_dong', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //印度
    index: 3,
    time: '5',
  },
  {
    //"您好，欢迎光临"
    replyName: stringsTo('voice_for_welcome'),
    replyCNUrl: '3-welcom_female', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //印度
    index: 4,
    time: '5',
  },
  {
    //"您已进入监控区域"
    replyName: stringsTo('voice_for_area'),
    replyCNUrl: '4-entered_monitoring_male', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//大陆
    replyUSUrl:
      'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //美国
    replySGUrl:
      'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //新加坡
    replyDEUrl:
      'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //欧洲
    replRUUrl:
      'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //俄罗斯
    replIDUrl:
      'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //印度
    index: 5,
    time: '5',
  },
  {
    //"请随手关门，谢谢"
    replyName: stringsTo('voice_for_closedoor'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//大陆
    replyCNUrl: '5-close_door_female', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //印度
    index: 6,
    time: '5',
  },
  {
    //"请注意安全"
    replyName: stringsTo('voice_for_safe'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//大陆
    replyCNUrl: '6-safety_female', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //印度
    index: 7,
    time: '5',
  },
  {
    //"上下楼梯,请注意安全"
    replyName: stringsTo('voice_for_stairs'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//大陆
    replyCNUrl: '9-stairs_safe_female', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //印度
    index: 8,
    time: '5',
  },
  {
    //"危险区域，请离开"
    replyName: stringsTo('voice_for_dangerArea'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//大陆
    replyCNUrl: '10-danger_area_male', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //印度
    index: 9,
    time: '5',
  },
];

const TAG = 'PromptVoiceSetPage';

//TODO 尝试：如果无自定义音频，默默删除customize_audio_alarm文件夹以清除上次解绑未删除的文件
/**
 * 提示音选择
 */
export default class PromptVoiceSetPage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      allVoiceArr: voiceArr,
      audioPath: this.props.route.params.audioPath,
      isAudioOn: this.props.route.params.isAudioOn,
    };
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentWillUnmount() {
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  onBackHandler = () => {
    console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
    if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    } else {
    }
    return false;
  };

  _onPressBack = () => {
    if (this.props.route.params.callback) {
      this.props.route.params.callback(this.state.audioPath, this.state.isAudioOn);
    }
    this.props.navigation.pop();
  };

  componentDidMount() {
    console.log('当前服务器地址--', IMIHost.serverCode);
  }

  render() {
    global.navigation = this.props.navigation;
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('tip_voice_selected')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        <FlatList
          data={this.state.allVoiceArr}
          renderItem={this._renderItem}
          keyExtractor={(item, index) => `key_${index}`}
          extraData={this.state}
          onEndReachedThreshold={0.1}
        />
      </View>
    );
  }

  _renderItem = ({item, index}) => {
    const {isAudioOn, audioPath} = this.state;
    let isSelected = false;
    if (index === 0 && !isAudioOn) {
      isSelected = true;
    } else if (index !== 0 && isAudioOn && audioPath === this.showCurrentImg(item)) {
      isSelected = true;
    }
    return (
      <TouchableWithoutFeedback onPress={() => {
        const sound = this.showCurrentImg(item)
        if (isSelected) {
          const piid = SOUND_AND_LIGHT_WARNING.SOUND_TEST.PIID;
          LetDevice.setProperties(true, LetDevice.deviceID, piid, JSON.stringify({msg_id: piid, value: {uselect: sound, sound_file: sound, sound_type: 0}}));
        } else {
          if (index === 0) {
            this.setState({ isAudioOn: false })
            const piid = SOUND_AND_LIGHT_WARNING.WARNING_AUDIO_SWITCH.PIID;
            LetDevice.setProperties(true, LetDevice.deviceID, piid, JSON.stringify({msg_id: piid, value: false,}));
          } else {
            this.setState({ isAudioOn: true, audioPath: this.showCurrentImg(item) })
            const piid = SOUND_AND_LIGHT_WARNING.WARNING_AUDIO_SWITCH.PIID;
            LetDevice.setProperties(true, LetDevice.deviceID, piid, JSON.stringify({msg_id: piid, value: false,}));


            const alarmSoundPiid = SOUND_AND_LIGHT_WARNING.WARNING_ALARM_CONFIG.PIID;
            LetDevice.setProperties(true, LetDevice.deviceID, alarmSoundPiid, JSON.stringify({msg_id: alarmSoundPiid, value: {sound_file: sound}}), true);
          }
        }
      }}>
        <View
          style={{
            flexDirection: 'column',
            height: 60,
            width: screen_width,
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'white',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: screen_width,
              height: 60,
              backgroundColor: 'white',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
            <View style={{ flexDirection: 'row' }}>
              <Image
                style={{height: 25, width: 25, marginLeft: 14, resizeMode: 'contain'}}
                source={isSelected ? require('../../resources/images/icon_select_s.png') : null}
              />
              <Text
                numberOfLines={2}
                style={{
                  fontSize: 15,
                  marginLeft: 14,
                  color: isSelected ? colors.defaultThemeColor : '#333333',
                  textAlign: 'left',
                }}>
                {item.replyName}
              </Text>
            </View>
            <Image
              style={{height: 30, width: 30, marginRight: 20, resizeMode: 'contain'}}
              source={isSelected ? require('../../resources/images/icon_play_primary.png') : null}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  showCurrentImg(item) {
    let newAudioUrl = item.replyCNUrl;
    console.log(TAG, 'IMIHost.serverCode', IMIHost.serverCode);
    if (IMIHost.serverCode === IMIHost.SERVER_CODE.CN) {
      newAudioUrl = item.replyCNUrl;
    } else if (IMIHost.serverCode === IMIHost.SERVER_CODE.SG) {
      newAudioUrl = item.replySGUrl;
    } else if (IMIHost.serverCode === IMIHost.SERVER_CODE.US) {
      newAudioUrl = item.replyUSUrl;
    } else if (IMIHost.serverCode === IMIHost.SERVER_CODE.EU) {
      newAudioUrl = item.replyDEUrl;
    }
    console.log('newAudioUrl--', newAudioUrl);
    return newAudioUrl;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
});
