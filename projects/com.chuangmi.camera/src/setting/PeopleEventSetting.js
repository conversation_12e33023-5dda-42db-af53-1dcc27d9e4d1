import {Text, View, StyleSheet, ScrollView, Image} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage, LetDevice} from '../../../../imilab-rn-sdk';
import {imiThemeManager, showLoading, showToast} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import I18n, {stringsTo} from '../../../../globalization/Localize';

const PeopleEventSetting = props => {
  const [peopleDetection, setPeopleDetection] = useState(true);
  const [peopleTracking, setPeopleTracking] = useState(true);
  const [peopleFrame, setPeopleFrame] = useState(true);

  useEffect(() => {
    // 初始化数据
  }, []);

  const handlePeopleDetectionChange = value => {
    showLoading(stringsTo('commWaitText'), true);
    const paramJson = JSON.stringify({msg_id: '10022', value});

    LetDevice.setProperties(true, LetDevice.deviceID, '10022', paramJson)
      .then(() => {
        showToast(stringsTo('settings_set_success'));
        setPeopleDetection(value);
      })
      .catch(e => {
        showToast(I18n.t('operationFailed'));
        setPeopleDetection(peopleDetection);
      })
      .finally(() => {
        showLoading(false);
      });
  };

  const handlePeopleTrackingChange = value => {
    const paramJson = JSON.stringify({msg_id: '10027', value});
    // 这里可以添加设备控制逻辑
    LetDevice.setProperties(true, LetDevice.deviceID, '10027', paramJson)
      .then(() => {
        showToast(stringsTo('settings_set_success'));
        setPeopleTracking(value);
      })
      .catch(e => {
        showToast(I18n.t('operationFailed'));
        setPeopleTracking(peopleTracking);
      })
      .finally(() => {
        showLoading(false);
      });
  };

  // const handlePeopleFrameChange = value => {
  //   setPeopleFrame(value);
  //   // 这里可以添加设备控制逻辑
  //   console.log('人形画框开关:', value);
  // };
  useEffect(() => {
    LetDevice.getSingleProperty('10022').then(res => {
      if (res?.value?.code == 0) {
        setPeopleDetection(res.value.value);
      }
    });
    LetDevice.getSingleProperty('10027').then(res => {
      if (res?.value?.code == 0) {
        setPeopleTracking(res.value.value);
      }
    });
    // Promise.all(LetDevice.getSingleProperty('10022'), LetDevice.getSingleProperty('10027')).then(res => {
    //   console.log('res1', res);
    // });
  }, []);
  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo('humanoid_detection')}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
           
              
               props.navigation.goBack? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'housekeeping_assistant_back',
          },
        ]}
        right={[]}
      />

      <ScrollView style={styles.scrollView}>
        {/* 算法介绍区域 */}
        {/* <View style={styles.algorithmSection}>
          <View style={styles.imageContainer}>
            <Image style={styles.demoImage} resizeMode="cover" />
          </View>

          <View style={styles.algorithmInfo}>
            <Text style={styles.algorithmTitle}>算法介绍</Text>
            <Text style={styles.algorithmDescription}>
              AI检测画面中是否有人移动，检测到时，将在看家助手中记录事件，还可立即配置消息至手机、联动智能配件等。
            </Text>
          </View>
        </View> */}

        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 人形检测开关 */}
          <ListItmeWithSwitch
           title={stringsTo("peopleEvent")}
            value={peopleDetection}
            onValueChange={handlePeopleDetectionChange}
            accessibilityLabel={['people_detection_off', 'people_detection_on']}
          />
          {peopleDetection ? (
            <View>
              {/* 人形追踪开关 */}
              <ListItmeWithSwitch
                title={stringsTo("settings_alarm_human_track_title")}
                value={peopleTracking}
                disabled={!peopleDetection} // 当人形检测关闭时禁用
                onValueChange={handlePeopleTrackingChange}
                accessibilityLabel={['people_tracking_off', 'people_tracking_on']}
              />

              {/* 人形画框开关 */}
              {/* <ListItmeWithSwitch
                title="人形画框"
                value={peopleFrame}
                disabled={!peopleDetection} // 当人形检测关闭时禁用
                onValueChange={handlePeopleFrameChange}
                accessibilityLabel={['people_frame_off', 'people_frame_on']}
              /> */}
            </View>
          ) : null}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  algorithmSection: {
    backgroundColor: '#F8F9FA',
    marginTop: 0,
  },
  imageContainer: {
    height: 200,
    backgroundColor: '#E8E8E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 15,
    marginTop: 15,
    borderRadius: 8,
    overflow: 'hidden',
  },
  demoImage: {
    width: '100%',
    height: '100%',
  },
  algorithmInfo: {
    padding: 15,
  },
  algorithmTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  algorithmDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  settingsSection: {
    marginTop: 10,
  },
});

export default PeopleEventSetting;
