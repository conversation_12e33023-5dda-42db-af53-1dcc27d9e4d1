import React, {PureComponent} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  ActivityIndicator,
  ScrollView,
  ImageBackground,
  StatusBar,
} from 'react-native';
import {colors, ListItem} from '../../../../imilab-design-ui';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIStorage, LetDevice, LetIMIIotRequest, IMILog, LetIProperties} from '../../../../imilab-rn-sdk';
import {showToast, MessageDialog, showLoading, ChoiceItem} from '../../../../imilab-design-ui';
import AlertDialog from '../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog';

import I18n, {stringsTo} from '../../../../globalization/Localize';
import {EmptySDCarView} from '../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/sdcard/EmptySDCarView';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import RoundedButtonViewNew from '../../../../imilab-design-ui/src/widgets/settingUI/RoundedButtonViewNew';
import {isIos, isIphoneXSeries, isIphone14ProMax} from '../../../../imilab-rn-sdk/utils/Utils';
import VersionUtils from '../utils/VersionUtils';

const ScreenHeight = Dimensions.get('window').height;
const tag = 'SdCardNewPage';

export default class SdCardNewPage extends PureComponent {
  constructor(props, context) {
    super(props, context);
    this.state = {
      storageRemainCapacity: 0, //SD卡剩余容量
      storageAllCapacity: 0, //SD卡所有容量
      storageRemainLeft: 0, //SD卡剩余容量
      sdCardStatus: -1, //SD卡状态，-1表示数据加载中
      dialogVisible: false, //格式化SD卡对话框的可见性
      injectDialogVisible: false, //弹出SD卡确认对话框的可见性
      loopQueryType: 0, //0:初始化 1:退出SD卡 2:格式化SD卡
      showCalibrationModal: false, //录制模式弹窗

      storageRecordMode: 2, //录制模式
      currentStorageRecordMode: 2, // 录制模式
      showRecordQualityModal: false, //录制清晰度弹窗
      currentRecordMode: 1, // 录制清晰度
      tempCurrentRecordMode: 1, // 录制清晰度
      storageSwitch: true, //默认打开存储
      showRecordEventMode: false, //是否支持事件录制

      // TODO 是否支持SD卡新方案,目前此工程下仅有062支持，但062仅支持中英文，如果其他项目遇到小语种适配，我来调整适配 fugui 20230425
      supportNewSolution: false,
    };
    this.isFirst = true;
    this.count = 0;
    this.formatFlag = false;
    console.log(tag);
  }

  componentDidMount() {
    this.isFirst = true;
    if (LetDevice.model == 'a1Godgpvr3D') {
      this.getFirmwareInfo();
    }

    this.getRecordMode();
    this.getSdStatusAndStorageInfo(this.state.loopQueryType);
    this.intervalQuery = setInterval(() => this.getSdStatusAndStorageInfo(this.state.loopQueryType), 2500);
    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      showLoading(stringsTo('storageCardFormating'), false);
      this.intervalQuery && clearInterval(this.intervalQuery);
    });

      //监听Action获取SD卡总容量、已用容量、可循环录制的时长（由属性改为Action非常不合理）
      this.deviceActionListener = LetIProperties.addActionResultListener(result => {
          try {
          
            
              let item = JSON.parse(result);
              
              let stateProps = {};
              if (`${item?.thingid}` === `${10016}`) {
                
                  if (isNaN(item?.value?.value)) {
                      return;
                  }
                  stateProps.storageRemainCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
              }
              if (`${item?.thingid}` === `${10014}`) {
                  if (isNaN(item?.value?.value)) {
                      return;
                  }
                  stateProps.storageAllCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
              }
              if (`${item?.thingid}` === `${10017}`) {
                  if (isNaN(item?.value?.value)) {
                      return;
                  }
                  stateProps.storageRemainLeft = (item?.value?.value / 60 / 24).toFixed(1);
              }
              this.setState(stateProps);
          } catch (error) {
              // 处理错误情况
              console.error("JSON解析失败:", error,result);
          }

      });
  }

  componentWillUnmount() {
    if (this.state.sdCardStatus == 3) {
      showLoading(false);
    }
    this.intervalQuery && clearInterval(this.intervalQuery);
    this.deviceActionListener && this.deviceActionListener.remove();


      IMILogUtil.uploadClickEventValue({SdCardStatus: this.state.sdCardStatus});
  }
  //获取摄像机录制模式
  getRecordMode() {
    // 使用接口获取哦
    const params = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10012,
        method: 'sync'
      },
      Method: 'POST',
    };
    LetIMIIotRequest.sendUserServerRequest(params, true).then(value => {
      //取得的值是字符串'0'，'-1'，'5'...
      console.log(10012, value);

      this.setState({
        storageRecordMode: value?.value?.value,
        currentStorageRecordMode: value?.value?.value,
        storageSwitch: value?.value?.value !== 2,
      });
    });

    //录像清晰度，0高清  1标清
    LetDevice.getPropertyCloud('RecordQuality').then(value => {
      console.log('------', value);
      this.setState({tempCurrentRecordMode: value, currentRecordMode: value});
    });
  }

  /*每隔2.5秒查询一次SD卡状态和容量信息*/
  getSdStatusAndStorageInfo(type = 0) {


      Promise.all([
        LetDevice.sendAction(false, LetDevice.deviceID, '10016', JSON.stringify({msg_id: 10016})),
        LetDevice.getSingleProperty('10013'),
        LetDevice.sendAction(false, LetDevice.deviceID, '10014', JSON.stringify({msg_id: 10014})),
        LetDevice.sendAction(false, LetDevice.deviceID, '10017', JSON.stringify({msg_id: 10017}))])
      .then(data => {
        this.count = 0;
        data?.forEach(item => {
          const stateProps = {};
          if (`${item?.thingId}` === `${10013}`) {
            if (this.formatFlag) {
              IMILog.logD('格式化后收到的参数', '10013' + JSON.stringify({...item, type}));
            }
            // 0代表有卡，1没卡
            stateProps.sdCardStatus = item?.value?.value;
            if (item?.value?.value !== 4 && type === 2) {
              if (item?.value?.value == 0) {
                showToast(stringsTo('sdcard_format_success'))
              } else {
                showToast(stringsTo('sdcard_format_fail'))
              }
              this.setState({loopQueryType: 0})
              showLoading(false);
             
              this.formatFlag = false;
            }
            if (type === 1 && item?.value?.value !== 7) {
              this.setState({loopQueryType: 0});
              showLoading(false);
            }
          }
          //下面的三个属性改为动作了，从监听中获取数据，很不合理
          /*if (`${item?.thingId}` === `${10016}`) {
            if (isNaN(item?.value?.value)) {
              return;
            }
            stateProps.storageRemainCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
          }
          if (`${item?.thingId}` === `${10014}`) {
            if (isNaN(item?.value?.value)) {
              return;
            }
            stateProps.storageAllCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
          }
          if (`${item?.thingId}` === `${10017}`) {
            if (isNaN(item?.value?.value)) {
              return;
            }
            stateProps.storageRemainLeft = (item?.value?.value / 60 / 24).toFixed(1);
          }*/
          this.setState(stateProps);
        });
        if (this.isFirst && this.props.route.params.type === 2) {
          this._formatSdCard();
        }
        this.isFirst = false;
      })
      .catch(() => {
        this.count++;
        if (this.count > 4 && this.isFirst) {
          this.setState({
            sdCardStatus: -2
          })
          showLoading(false);
          // showToast(stringsTo('commLoadingFailText'))
          this.intervalQuery && clearInterval(this.intervalQuery);
        }
      });
    // LetDevice.updateAllPropertyCloud()
    //   .then(result => {
    //     //取得的值是字符串'0'，'-1'，'5'...
    //     console.log('getPropertyCloud---------', result);
    //     let stateProps = {};
    //     let status = -1;
    //     let resultJSON = JSON.parse(result);
    //     console.log('getPropertyCloud1---------', resultJSON);
    //     resultJSON?.forEach(element => {
    //       //SD卡状态
    //       if (`${element?.thingId}` === `${10013}`) {
    //         console.log('sdCardStatus', element);
    //         status = parseInt(element?.value?.value);
    //         stateProps.sdCardStatus = element?.value?.value;
    //       }
    //     });
    //     if (resultJSON.hasOwnProperty('NeedFormatStorageMedia')) {
    //       stateProps.supportNewSolution = true;
    //     }
    //     if (resultJSON.hasOwnProperty('StorageStatus')) {
    //       status = parseInt(resultJSON.StorageStatus.value);
    //       stateProps.sdCardStatus = status;
    //     }
    //     if (resultJSON.hasOwnProperty('StorageRemainCapacity')) {
    //       let remainCapacity = parseInt(resultJSON.StorageRemainCapacity.value);
    //       stateProps.storageRemainCapacity = (remainCapacity / 1000).toFixed(2);
    //     }
    //     if (status == 0 || status == 4) {
    //       //未插卡||SD卡已弹出
    //       showLoading(stringsTo('storageCardFormating'), false);
    //       if (type == 1) {
    //         showLoading(stringsTo('commLoadingText'), false);
    //         showToast(I18n.t('injectSuccess'));
    //         stateProps.loopQueryType = 0;
    //       }
    //     } else if (status == 1 || status == 2) {
    //       //正常使用中 和 未格式化(格式化失败为此状态)
    //       //不加上次的sdCardStatus为3的限制，会出现还没出现loading时，就弹格式化成功，然后再出现loading，过后是格式化成功
    //       console.log('轮询----正常或未格式化', status, type, this.lastSdCardStatus);
    //       if (type == 2 && this.lastSdCardStatus == 3) {
    //         //处于格式化后的轮询状态，并且正在显示格式化中，根据status判断格式化是否成功
    //         showLoading(stringsTo('storageCardFormating'), false);
    //         status == 1 ? showToast(I18n.t('sdcard_format_success')) : showToast(I18n.t('sdcard_format_fail'));
    //         stateProps.loopQueryType = 0;
    //       }
    //     } else if (status == 3) {
    //       //正在格式化
    //       //showLoading(stringsTo('storageCardFormating'), true,true);
    //     } else if (status == 5) {
    //       //SD卡异常，暂不用
    //     } else if (status == -99) {
    //       status = 1; //弹出SD时的中间态,当做正常态处理
    //     }
    //     showLoading(false);
    //     this.lastSdCardStatus = status;
    //     this.setState(stateProps);
    //   })
    //   .catch(error => {
    //     console.log('轮询error:', error);
    //     if (this.state.loopQueryType != 0) {
    //       return;
    //     }
    //     //进入本页面，物模型获取失败
    //     showLoading(false);
    //     this.setState({sdCardStatus: -2}); //设置一个未处理的status值，使其显示空白页面防止一直loading
    //     showToast(stringsTo('commLoadingFailText'));
    //   });
  }

  getRecordModeStatus() {
    let comment = '';
    if (this.state.storageRecordMode == 1) {
      comment = stringsTo(this.state.showRecordEventMode ? 'setting_record_model_event' : 'setting_record_model_move');
    } else if (this.state.storageRecordMode == 0) {
      comment = stringsTo('setting_record_model_always');
    } else {
      comment = stringsTo('setting_record_model_close');
    }
    return comment;
  }

  getRecordQualityStatus() {
    let comment = '';
    if (this.state.currentRecordMode == 0) {
      comment = stringsTo('resolution_qhd');
    } else {
      comment = stringsTo('resolution_sd');
    }
    return comment;
  }

  /*弹出SD卡*/
  _injectSdCard() {
    let paramJson = JSON.stringify({msg_id: 10019});
    showLoading(stringsTo('storageCardHinting'), true, false);
    LetDevice.sendAction(true, LetDevice.deviceID, '10019', paramJson)
      .then(res => {
        // showToast(I18n.t('action_success'));
        // this.getSdStatusAndStorageInfo(1);
        // showLoading(stringsTo('commLoadingText'), false);
        this.setState({loopQueryType: 1});
      })
      .catch(err => {
        showLoading(false);
        showToast(I18n.t('injectFailed'));
      });
  }

  /*格式化SD卡*/
  _formatSdCard() {
    let paramJson = JSON.stringify({msg_id: 10018});
    showLoading(stringsTo('storageCardFormating'), true, false);
    this.formatFlag = true;
    LetDevice.sendAction(true, LetDevice.deviceID, '10018', paramJson)
      .then(res => {
        // showToast(I18n.t('action_success'));
        this.setState({loopQueryType: 2});
      })
      .catch(err => {
        this.formatFlag = false;
        showLoading(false);
        showToast(I18n.t('sdcard_format_fail'));
      });
  }

  _saveRecordMode() {
    console.log('_saveRecordMode :' + this.state.storageRecordMode + ' --> ' + this.state.currentStorageRecordMode);
    IMIStorage.save({
      key: LetDevice.deviceID + 'storageRecordMode',
      data: {
        storageRecordMode: this.state.storageRecordMode,
      },
      expires: null,
    });
    this._uploadRecordMode(0, false);
  }

  _loadRecordMode() {
    IMIStorage.load({
      key: LetDevice.deviceID + 'storageRecordMode',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        console.log('_loadRecordMode :' + res.storageRecordMode + ' --> ' + typeof res.storageRecordMode);
        this._uploadRecordMode(parseInt(res.storageRecordMode), true);
      })
      .catch(_ => {
        console.log('_loadRecordMode error', _);
        this._uploadRecordMode(2, true);
      });
  }

  _uploadRecordMode(value, isSwitch) {
    let params = {StorageRecordMode: value};
    IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params))
      .then(data => {
        // console.log(' 存储开关成功',params);
        this.setState({
          storageRecordMode: value,
          currentStorageRecordMode: value,
          storageSwitch: isSwitch,
        });
      })
      .catch(error => {});
  }
  onSetStorageSwitch(value) {
    showLoading(stringsTo('commWaitText'), true);
    const paramJson = JSON.stringify({msg_id: '10012', value: value ? 0 : 2});
    LetDevice.setProperties(true, LetDevice.deviceID, '10012', paramJson)
      .then(() => {
        showLoading(false);
        this.setState({
          storageRecordMode: value ? 0 : 2,
          currentStorageRecordMode: value ? 0 : 2,
          storageSwitch: value,
        });
      })
      .catch(() => {
        showLoading(false);
      });
  }
  /*页面加载中*/
  _renderLoading() {
    if (this.state.sdCardStatus != -1 && this.state.sdCardStatus != 7 && this.state.sdCardStatus != 6) {
      return null;
    }
    return (
      <View style={styles.loadingContainer}>
        <View style={{ marginTop: -(isIos() ? (isIphone14ProMax() ? 59 : isIphoneXSeries() ? 47 : 20) + 50 : StatusBar.currentHeight + 50)}}></View>
        <ActivityIndicator style={{width: 54, height: 54}} color={'#4A70A5'} size={'large'} />
        <Text
          style={{
            textAlign: 'center',
            color: 'black',
            fontSize: 15,
          }}>
          {stringsTo(this.state.sdCardStatus === -1 ? 'commLoadingText' : this.state.sdCardStatus === 6 ? 'commRepairText' : 'commWaitText')}
        </Text>
      </View>
    );
  }

  // 异常
  _renderError() {
    if (this.state.sdCardStatus !== -2) {
      return null;
    }
    return (
      <View style={styles.loadingContainer}>
        <Text
          style={{
            textAlign: 'center',
            color: 'black',
            fontSize: 15,
            marginTop: -(isIos() ? (isIphone14ProMax() ? 59 : isIphoneXSeries() ? 47 : 20) + 50 : StatusBar.currentHeight + 50),
          }}>
          {stringsTo('commLoadingFailText')}
        </Text>
      </View>
    );
  }

  /*未插卡时SD卡页面*/
  _renderEmptySdCard() {
    if (this.state.sdCardStatus != 1) {
      return null;
    }
    return (
      <EmptySDCarView
        defaultIcon={
          LetDevice.model == 'a1zcQKoHQ83'
            ? require('../../resources/images/sd_empty_icon.png')
            : require('../../resources/images/lookback_pic_empty.png')
        }
        defaultText={stringsTo('noSdCardTitle')}
        rootStyle={{backgroundColor: '#F7F7F7'}}
        /*  subText={stringsTo('no_sd_card_tips_text')}*/
        limitShareUser={true}
        iconSize={LetDevice.model == 'a1zcQKoHQ83' ? true : false}
      />
    );
  }

  // 存储卡页面
  _renderNormalSdCardNew() {
    // 0正常 4格式化中
    if (this.state.sdCardStatus != 0 && this.state.sdCardStatus != 4) {
      //正常和正在格式化中
      return null;
    }
    let statusMessage =
      this.state.sdCardStatus == 3
        ? stringsTo('sdcard_status4')
        : this.state.storageSwitch
        ? I18n.t('sdcard_status_normal_new1')
        : I18n.t('sd_suspended');
    return (
      <View style={{width: '100%', backgroundColor: '#F7F7F7'}}>
        <View style={{...styles.sdcardNewStyle, height: 330}}>
          {/*  {LetDevice.model == 'a1MSKK9lmbs' ? (
            <ImageBackground
              style={{
                display: 'flex',
                width: 208,
                height: 208,
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              source={
                this.state.storageSwitch
                  ? require('../../resources/images/sd_cord_top_bg.png')
                  : require('../../resources/images/sd_card_top_bg_no.png')
              }>
              <Text style={{fontSize: 17, fontWeight: '500', color: 'white'}} numberOfLines={1}>
                {statusMessage}
              </Text>
            </ImageBackground>
          ) : (
            <ImageBackground
              style={{display: 'flex', width: 208, height: 208, flexDirection: 'column', alignItems: 'center'}}
              source={
                this.state.storageSwitch
                  ? require('../../resources/images/sd_cord_top_bg.png')
                  : require('../../resources/images/sd_card_top_bg_no.png')
              }>
              <Text style={styles.stateCoverTitle} numberOfLines={1}>
                {this.state.storageSwitch ? I18n.t('sdcard_status_normal_new') : I18n.t('sd_suspended')}
              </Text>
              <View style={styles.stateCoverSeparate} />
              <Text style={styles.stateCoverDetail}>{I18n.t('sdcard_status_more_new')}</Text>
              <Text style={styles.stateCoverDetail1}>{this.state.storageRemainCapacity + 'GB'}</Text>
            </ImageBackground>
          )} */}
          <ImageBackground
            style={{display: 'flex', width: 208, height: 208, flexDirection: 'column', alignItems: 'center'}}
            source={
              this.state.storageSwitch
                ? require('../../resources/images/sd_cord_top_bg.png')
                : require('../../resources/images/sd_card_top_bg_no.png')
            }>
            <Text style={styles.stateCoverTitle} numberOfLines={1}>
              {this.state.storageSwitch ? I18n.t('sdcard_status_normal_new1') : I18n.t('sd_suspended')}
            </Text>
            <View style={styles.stateCoverSeparate} />
            <Text style={{...styles.stateCoverDetail, marginTop: 15}}>{I18n.t('sdcard_status0')}</Text>
            {/* <Text style={styles.stateCoverDetail1}>{this.state.storageRemainCapacity + 'GB'}</Text> */}
          </ImageBackground>
          <View style={{marginTop: 10}}>
              <Text>{I18n.t('sdCardRemain', {code: this.state.storageRemainCapacity})}, {I18n.t('sdCardTotal', {code: this.state.storageAllCapacity})}</Text>
              <Text style={{textAlign: 'center'}}>{I18n.t('sdCardLeft', {code: this.state.storageRemainLeft})}</Text>
          </View>
        </View>

        <View style={{height: 14, backgroundColor: '#F1F1F1'}} />
        <ListItmeWithSwitch
          title={stringsTo('sd_storage_switch')}
          value={this.state.storageSwitch}
          onValueChange={value => {
            this.onSetStorageSwitch(value);
            /*  console.log('onValueChange ==> ' + value);
            value && this._loadRecordMode();
            !value && this._saveRecordMode(); */
          }}
          disabled={this.state.sdCardStatus != 0 && this.state.sdCardStatus != 4}
        />
        {/* 录制模式 */}
        <ListItem
          title={stringsTo('setting_record_model')}
          hide={!this.state.storageSwitch}
          value={this.getRecordModeStatus()}
          onPress={() => {
            if (this._isShareUser()) {
              return;
            }
            this.setState({showCalibrationModal: true}, callback => {});
          }}
          disabled={this.state.sdCardStatus != 0 && this.state.sdCardStatus != 4}
        />

        {/* 060 没有
        <ListItem
          title={stringsTo('record_quality')}
          hide={!(LetDevice.model == 'a1MSKK9lmbs' && this.state.storageSwitch)}
          value={this.getRecordQualityStatus()}
          onPress={() => {
            // if (this._isShareUser())return;
            this.setState({showRecordQualityModal: true}, callback => {});
          }}
          disabled={this.state.sdCardStatus != 1}
        /> */}

        <View style={{height: 14, backgroundColor: '#F1F1F1'}} />

        <ListItem
          title={stringsTo('storageCardFormat')}
          accessibilityLabel={'storageCardFormat'}
          onPress={() => {
            if (this._isShareUser()) {
              return;
            }
            this.setState({dialogVisible: true});
          }}
          disabled={this.state.sdCardStatus != 0 && this.state.sdCardStatus != 4}
        />

        <ListItem
          title={stringsTo('injectSdCardTitle')}
          accessibilityLabel={'injectSdCardTitle'}
          onPress={() => {
            if (this._isShareUser()) {
              return;
            }
            this.setState({injectDialogVisible: true});
          }}
          disabled={this.state.sdCardStatus != 0 && this.state.sdCardStatus != 4}
        />

        {LetDevice.model == 'a1MSKK9lmbs' ? (
          <View style={{width: '100%', backGroundColor: '#F7F7F7', marginVertical: 14}}>
            <Text style={{marginHorizontal: 14, color: '#7F7F7F', fontSize: 12, lineHeight: 17}}>
              {I18n.t('sd_card_use_hint_062')}
            </Text>
          </View>
        ) : (
          <View style={{width: '100%', backGroundColor: '#F7F7F7'}}>
            <Text
              style={{marginHorizontal: 14, marginTop: 14, color: '#7F7F7F', fontSize: 12, lineHeight: 16}}
              allowFontScaling={false}>
              {I18n.t('sdcard_tip1_new')}
            </Text>
            <Text
              style={{
                marginHorizontal: 14,
                marginTop: 6,
                marginBottom: 14,
                color: '#7F7F7F',
                fontSize: 12,
                lineHeight: 16,
              }}
              allowFontScaling={false}>
              {I18n.t('sdcard_tip2_new')}
            </Text>
          </View>
        )}
      </View>
    );
  }

  /*SD推出UI*/
  _renderLaunchSdCard() {
    // 5已推出
    if (this.state.sdCardStatus != 5) {
      return null;
    }
    let length = I18n.t('sdcard_out_already').length;
    return (
      <View style={{width: '100%', backgroundColor: '#F7F7F7'}}>
        <View style={styles.sdcardNewStyle}>
          <ImageBackground
            style={{display: 'flex', width: 208, height: 208, flexDirection: 'column', alignItems: 'center'}}
            source={require('../../resources/images/sd_error.png')}>
            <Text
              style={[styles.stateCoverTitle, {textAlign: 'center', fontSize: length > 7 ? 12 : 17}]}
              numberOfLines={2}>
              {I18n.t('sdcard_out_already')}
            </Text>
            <View style={styles.stateCoverSeparate} />
          </ImageBackground>
        </View>
      </View>
    );
  }
  /*SD异常的UI- SD卡异常、未格式化*/
  _renderAbnormalSdCard() {
    // 3异常
    if (this.state.sdCardStatus != 3 && this.state.sdCardStatus != 2 && this.state.sdCardStatus !== 8 && this.state.sdCardStatus !== 9) {
      return null;
    }
    //因为目前仅062使用的sd_status_need_format和sd_status_need_format_description无小语种翻译，因此其他项目走sdCardDamaged的逻辑 20230425
    let navigationBarHeight = isIos() ? (isIphone14ProMax() ? 59 : isIphoneXSeries() ? 47 : 20) + 50 : StatusBar.currentHeight + 30;
    return (
      <View style={{width: '100%', height: ScreenHeight - navigationBarHeight}}>
        <View style={[styles.sdcardNewStyle, {backgroundColor: 'transparent'}]}>
          <ImageBackground
            style={{
              display: 'flex',
              width: 208,
              height: 208,
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            source={require('../../resources/images/sd_error.png')}>
            {this.state.supportNewSolution ? (
              <View style={{alignItems: 'center'}}>
                <Text style={[styles.stateCoverTitle, {marginTop: 0}]} numberOfLines={1}>
                  {this.state.sdCardStatus == 2
                    ? stringsTo('sdcard_status6')
                    : stringsTo('sdcard_status_abnormal')}
                </Text>
                <View style={[styles.stateCoverSeparate, {marginVertical: 20}]} />
                <Text
                  style={[
                    styles.stateCoverTitle,
                    {fontWeight: '400', marginTop: 0, fontSize: 12, textAlign: 'center', maxWidth: 130},
                  ]}
                  numberOfLines={3}>
                  {this.state.sdCardStatus == 2 ? stringsTo('sdcard_status6') : ''}
                </Text>
              </View>
            ) : (
              <Text
                style={[styles.stateCoverTitle, {maxWidth: 180, marginTop: 0, textAlign: 'center'}]}
                numberOfLines={3}>
                {this.state.sdCardStatus == 2
                    ? stringsTo('sdcard_status6')
                    : this.state.sdCardStatus === 8
                      ? stringsTo('sdcard_status7')
                      : this.state.sdCardStatus === 9
                        ? stringsTo('sdcard_status8')
                        : stringsTo('sdCardDamaged')}
              </Text>
            )}
          </ImageBackground>
        </View>

        {this.state.sdCardStatus !== 9 && <View style={{width: '100%', height: 75, position: 'absolute', bottom: 20}}>
          <RoundedButtonViewNew
            buttonText={stringsTo('storageCardFormat')}
            disabled={false}
            buttonStyle={{backgroundColor: '#EEEEEE', margin: 15}}
            buttonTextStyle={{color: '#EB614B'}}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              this.setState({dialogVisible: true});
            }}
          />
        </View>}
      </View>
    );
  }
  /*录制模式弹窗*/
  _renderRecordAlert() {
    if (!this.state.showCalibrationModal) {
      return null;
    }
    return (
      <MessageDialog
        title={I18n.t('setting_record_model')}
        message={''}
        visible={this.state.showCalibrationModal}
        buttons={[
          {
            text: I18n.t('cancel'),
            accessibilityLabel: 'cancelSetRecord',
            callback: _ => this.setState({showCalibrationModal: false, currentStorageRecordMode: this.state.storageRecordMode}),
          },
          {
            text: I18n.t('ok_button'),
            accessibilityLabel: 'okSetRecord',
            callback: _ => {
              this.setState({showCalibrationModal: false});
              showLoading(stringsTo('commLoadingText'), true);

              //let params = {StorageRecordMode: this.state.currentStorageRecordMode};
              const paramJson = JSON.stringify({msg_id: 10012, value: this.state.currentStorageRecordMode});
              LetDevice.setProperties(true, LetDevice.deviceID, '10012', paramJson)
                .then(() => {
                  showToast(stringsTo('settings_set_success'))
                  this.setState({
                    storageRecordMode: this.state.currentStorageRecordMode,
                    storageSwitch: this.state.currentStorageRecordMode !== 2,
                  });
                })
                .catch(err => {
                  showToast(stringsTo('operationFailed'))
                  console.log('设置录制模式', JSON.stringify(err));
                  this.setState({currentStorageRecordMode: this.state.storageRecordMode});
                })
                .finally(() => {
                  showLoading(false);
                });
            },
          },
        ]}
        onDismiss={() => this.setState({showCalibrationModal: false, currentStorageRecordMode: this.state.storageRecordMode})}>
        <ChoiceItem
          title={stringsTo('setting_record_model_always')}
          subtitle={stringsTo('setting_record_model_always_title')}
          containerStyle={{margin: 14, marginTop: 0, height: 90}}
          checked={this.state.currentStorageRecordMode === 0 ? true : false}
          onValueChange={value => {
            this.setState({
              currentStorageRecordMode: value ? 0 : this.state.storageRecordMode,
            });
          }}
        />
        <ChoiceItem
          title={stringsTo(this.state.showRecordEventMode ? 'setting_record_model_event' : 'setting_record_model_move')}
          subtitle={stringsTo(
            this.state.showRecordEventMode ? 'setting_record_model_event_title' : 'setting_record_model_move_title',
          )}
          containerStyle={{margin: 14, marginTop: 0, height: 90}}
          checked={this.state.currentStorageRecordMode === 1 ? true : false}
          onValueChange={value => {
            this.setState({
              currentStorageRecordMode: value ? 1 : this.state.storageRecordMode,
            });
          }}
        />
        <ChoiceItem
          title={stringsTo('setting_record_model_close')}
          subtitle={stringsTo('setting_record_model_close_title')}
          containerStyle={{margin: 14, marginTop: 0, height: 90}}
          checked={this.state.currentStorageRecordMode == 2 ? true : false}
          onValueChange={value => {
            this.setState({
              currentStorageRecordMode: value ? 2 : this.state.storageRecordMode,
            });
          }}
        />
      </MessageDialog>
    );
  }

  _renderRecordQualityAlert() {
    if (!this.state.showRecordQualityModal) {
      return null;
    }
    return (
      <MessageDialog
        title={I18n.t('record_quality')}
        message={''}
        visible={this.state.showRecordQualityModal}
        buttons={[
          {
            text: I18n.t('cancel'),
            accessibilityLabel: 'cancelSetRecordQuality',
            callback: _ =>
              this.setState({showRecordQualityModal: false, tempCurrentRecordMode: this.state.currentRecordMode}),
          },
          {
            text: I18n.t('ok_button'),
            accessibilityLabel: 'okSetRecordQuality',
            callback: _ => {
              this.setState({showRecordQualityModal: false});
              let params = {RecordQuality: this.state.tempCurrentRecordMode};
              IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params))
                .then(data => {
                  // console.log(' 切换录制模式成功',params);
                  this.setState({currentRecordMode: this.state.tempCurrentRecordMode});
                })
                .catch(error => {
                  this.setState({tempCurrentRecordMode: this.state.currentRecordMode});
                });
            },
          },
        ]}
        onDismiss={() => this.setState({showRecordQualityModal: false})}>
        <ChoiceItem
          title={stringsTo('resolution_sd')}
          containerStyle={{margin: 14, marginTop: 0, height: 70}}
          checked={this.state.tempCurrentRecordMode == 1 ? true : false}
          onValueChange={value => {
            this.setState({
              tempCurrentRecordMode: value ? 1 : this.state.currentRecordMode,
            });
          }}
        />

        <ChoiceItem
          title={stringsTo('resolution_qhd')}
          containerStyle={{margin: 14, marginTop: 0, height: 70}}
          checked={this.state.tempCurrentRecordMode == 0 ? true : false}
          onValueChange={value => {
            this.setState({
              tempCurrentRecordMode: value ? 0 : this.state.currentRecordMode,
            });
          }}
        />
      </MessageDialog>
    );
  }

  /*格式化SD卡二次确认对话框*/
  _rendDialog() {
    if (!this.state.dialogVisible) {
      return null;
    }
    return (
      <AlertDialog
        title={I18n.t('formatTitle')}
        message={I18n.t('formatMessage')}
        visible={this.state.dialogVisible}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: _ => this.setState({dialogVisible: false}),
          },
          {
            text: I18n.t('ok_button'),
            callback: _ => {
              this.setState({dialogVisible: false});
              this._formatSdCard();
            },
          },
        ]}
        onDismiss={() => this.setState({dialogVisible: false})}
      />
    );
  }

  _renderInjectDialog() {
    return (
      <AlertDialog
        showTitle={false}
        visible={this.state.injectDialogVisible}
        message={I18n.t('storageCardHint')}
        messageStyle={{
          marginTop: 20,
          marginBottom: 26,
          fontSize: 17,
          fontWeight: '700',
          color: '#333333',
        }}
        ignoreHint={this.state.isNeverRemind}
        onCheckValueChange={checked => {
          this.setState({isNeverRemind: checked});
        }}
        canDismiss={true}
        onDismiss={() => {
          this.setState({injectDialogVisible: false});
        }}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: _ => {
              this.setState({injectDialogVisible: false});
            },
          },
          {
            text: I18n.t('ok_button'),
            callback: _ => {
              this.setState({injectDialogVisible: false});
              this._injectSdCard();
            },
          },
        ]}
      />
    );
  }

  _isShareUser() {
    if (LetDevice.isShareUser) {
      showToast(stringsTo('shareUser_tip'));
      return true;
    }
    return false;
  }

  render() {
    global.navigation = this.props.navigation;

    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('record_files_sdcard')}
          backgroundColor={this.state.sdCardStatus != 5 ? 'white' : 'transparent'}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this.props.navigation.pop()}]}
          right={[]}
        />

        {this._renderLoading()}

        {this._renderError()}

        {this._renderEmptySdCard()}

        <ScrollView showsVerticalScrollIndicator={false}>
          {this._renderNormalSdCardNew()}
          {this._renderLaunchSdCard()}
          {this._renderRecordAlert()}
          {this._renderAbnormalSdCard()}
          {this._renderRecordQualityAlert()}
        </ScrollView>

        {/*  {this._renderAbnormalSdCard()}*/}

        {this._rendDialog()}
        {this._renderInjectDialog()}

        {/*{this._renderRecordAlert()}*/}
      </View>
    );
  }

  getFirmwareInfo() {
    VersionUtils.getInstance()
      .get056FirmwareVersionPlayBackGridStatus()
      .then(res => {
        console.log('get056FirmwareVersionPlayBackGridStatus', res > 0);
        this.setState({showRecordEventMode: res > 0});
      })
      .catch(error => {
        console.log('get056FirmwareVersionPlayBackGridStatus error', error);
      });
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
  sdVolumeContainer: {
    height: 170,
    marginTop: 14,
    marginBottom: 20,
    marginHorizontal: 14,
    paddingHorizontal: 16,
    backgroundColor: '#496EE04C',
    borderRadius: 10,
    alignItems: 'center',
  },
  sdcardNewStyle: {
    paddingTop: 42,
    width: '100%',
    height: 300,
    alignItems: 'center',
    paddingBottom: 42,
    backgroundColor: 'white',
  },
  // stateCover: {
  //     position: 'absolute',
  //     width: 208,
  //     height: 208,
  //     alignItems: 'center',
  //     backgroundColor:'orange',
  //     borderRadius:104,
  // },
  stateCoverTitle: {
    marginTop: 65,
    fontSize: 17,
    fontWeight: '500',
    color: 'white',
  },
  stateCoverSeparate: {
    marginTop: 20,
    backgroundColor: 'white',
    height: 1,
    width: 208 - 28 * 2,
    marginLeft: 28,
    marginRight: 28,
  },
  stateCoverDetail: {
    marginTop: 10,
    fontSize: 15,
    color: 'white',
  },
  stateCoverDetail1: {
    marginTop: 7,
    fontSize: 15,
    color: 'white',
  },
  imageStyle: {
    width: 60,
    height: 60,
    marginTop: 20,
    marginBottom: 24,
  },
  volumeStickContainer: {
    width: '100%',
    height: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
  },
  volumeStickStyle: {
    position: 'absolute',
    height: 20,
    backgroundColor: '#9E8BEF',
    borderRadius: 10,
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  volumeTextStyle: {
    flex: 1,
    fontSize: 14,
    color: '#4A70A5',
    marginTop: 10,
  },
  destroyHintTextStyle: {
    width: '100%',
    fontSize: 14,
    color: '#FFFFFF',
    marginTop: 10,
    textAlign: 'left',
  },
  loadingContainer: {
    width: '100%',
    height: '100%',
    top: isIos() ? (isIphone14ProMax() ? 59 : isIphoneXSeries() ? 47 : 20) + 50 : StatusBar.currentHeight + 50,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transport',
  },
});
