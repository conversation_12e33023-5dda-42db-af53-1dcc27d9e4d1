import React from 'react';

import {StyleSheet, View, Image, Text, Platform, ScrollView, Dimensions, SafeAreaView} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';

import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ChoiceItem from '../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem';

import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {XText, XView} from 'react-native-easy-app';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {CameraMethod, LetIMIIotRequest, aliAlarmEventCloudApi, IMIGotoPage,imiAlarmEventCloudApi} from '../../../../imilab-rn-sdk';
import {showToast} from '../../../../imilab-design-ui';
import {showLoading} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';

import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import {customJsonParse} from '../utils/GenericUtils';

const BACKGROUNDCOLOR = '#4A6EE019';
const UNCHECKED_BACKGROUNDCOLOR = '#F2F3F5';

const {width} = Dimensions.get('window');

/**
 * 看家助手功能设置页面
 */

const tag = 'HouseKeepSetting';

export default class HouseKeepSetting extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      showRecordTimeModal: false, //推送时间间隔弹窗显示
      AlarmFrequencyLevel: 3, //推送时间间隔
      selectAlarmTime: 3, //逗留时长Value
      TrackSwitchValue: false, //人形追踪
      alarmPushValue: false, //报警消息推送
      LoudSwitchValue: false, //异响开关 大声
      LoudSwitchPushValue: false, //异响推送开关
      PeopleSwitchValue: false, //人形功能开关
      PeopleSwitchPushValue: false, //人形推送开关
      MoveSwitchValue: false, //移动侦测开关
      MoveSwitchPushValue: false, //移动推送开关
      soundLightSwitchValue: false, //声光报警开关
      oneKeySwitchValue: false, //一键警告开关
      MotionDetectSensitivityTit: stringsTo('sensitivity_for_high_tit'), //报警灵敏度
      detectionSensitivityValue: 0, //侦测灵敏度
      detectionSensitivityVisible: false, //侦测灵敏度弹窗
      pushValue: false, //推送开关
      alarmTimeString: '',
      vipState: -1,
      VirtualFenceValue:false, //虚拟围栏
      PrivacyAreaValue:false, //隐私区域保护
    };
    this.noticeData = {};
    this.detectionSensitivityDefaultValue = 0;
    this.watchList = [];
    this.loading1 = false;
    this.loading2 = false;
    this.loading3 = false;
    this.err1 = false;
    this.err2 = false;
    this.err3 = false;
    this.is060=LetDevice.model==='imilab.ipc.060'
  }

  componentWillUnmount() {
    // this.didFocusListener && this.didFocusListener.remove();
    // this.setState = () => false;// 组件销毁的时候将异步方法撤销
    // console.log('componentWillUnmount');
    showLoading("",false);
  }

  componentDidMount() {
    this.getAllValue();
    // if (LetDevice.model != 'a1Godgpvr3D') {
    //   this.getPushValue();
    // }
    this._subscribeFocus = this.props.navigation.addListener('focus', () => {
      // 从后面页回来
      this.getSwitchValueByCloud();
      this.getPushSwitchValueByCloud();
      // Orientation.addOrientationListener(this._orientationDidChange);
    });
  }
  // getPushValue() {
  //   const params = {
  //     Path: 'api/app/notice/event/list',
  //     ParamMap: {
  //       iotId: LetDevice.deviceID,
  //       productKey: LetDevice.model,
  //     },
  //   };

  //   console.log('开关推送传值params--', params);
  //   LetIMIIotRequest.sendUserServerRequest(params)
  //     .then(data => {
  //       console.log(' 推送获取数据--sendUserServerRequest  then->' + data);
  //       console.log(JSON.parse(data));
  //       console.log('hhhh');
  //       var pushArr = JSON.parse(data);
  //       for (let value in pushArr) {
  //         let {event, noticeEnabled} = pushArr[value];
  //         if (event === 'MOVE') {
  //           this.setState({PeopleSwitchPushValue: noticeEnabled});
  //         }
  //         if (event === 'SOUND') {
  //           this.setState({LoudSwitchPushValue: noticeEnabled});
  //         }
  //       }
  //     })
  //     .catch(error => {
  //       console.log('推送数据获取错误--', JSON.stringify(error));
  //     });
  // }

  async getAllValue() {
    this.loading3=true;
    this.err3=false;
    showLoading(stringsTo('commWaitText'), true);
    //
    const params1 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10021,
        method: 'sync',
      },
      Method: 'POST',
    };
    const params2 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: '10022',
        method: 'sync',
      },
      Method: 'POST',
    };
    const params3 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: '10023',
        method: 'sync',
      },
      Method: 'POST',
    };
    const params4 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: '10024',
        method: 'sync',
      },
      Method: 'POST',
    };
    const params5 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: '100000',
        method: 'sync',
      },
      Method: 'POST',
    };
    const params6 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: '100006',
        method: 'sync',
      },
      Method: 'POST',
    };
    let stateProps = {};
   
    // 暂时先这样
    const queryList=this.is060?[LetIMIIotRequest.sendUserServerRequest(params1, true),
      LetIMIIotRequest.sendUserServerRequest(params2, true),
      LetIMIIotRequest.sendUserServerRequest(params3, true),
      LetIMIIotRequest.sendUserServerRequest(params4, true),
    ]:[LetIMIIotRequest.sendUserServerRequest(params1, true),
      LetIMIIotRequest.sendUserServerRequest(params2, true),
      LetIMIIotRequest.sendUserServerRequest(params3, true),
      LetIMIIotRequest.sendUserServerRequest(params4, true),
      LetIMIIotRequest.sendUserServerRequest(params5, true),
      LetIMIIotRequest.sendUserServerRequest(params6, true),
      // imiAlarmEventCloudApi.getVipState(LetDevice.deviceID)
    ]
    Promise.all(queryList).then(res => {
        stateProps.MoveSwitchValue = Boolean(res[0]?.value?.value);
        stateProps.PeopleSwitchValue = Boolean(res[1]?.value?.value);
        stateProps.LoudSwitchValue = Boolean(res[2]?.value?.value);
        stateProps.detectionSensitivityValue = res[3]?.value?.value;
        this.detectionSensitivityDefaultValue = res[3]?.value?.value;
        stateProps.VirtualFenceValue = Boolean(res[4]?.value?.value);
        stateProps.PrivacyAreaValue = Boolean(res[5]?.value?.value);
        // try {
        //   stateProps.vipState = JSON.parse(res[6])?.status;
        // } catch (error) {
        //   console.log(error,"这是错误")
        // }
        this.setState(stateProps);
        this.loading3=false
     
        
        if (!this.loading1 && !this.loading2 && !this.loading3) {
          showLoading("",false);
          if (this.err1 || this.err2 || this.err3) {
            showToast(stringsTo('commLoadingFailText'));
          }
        }
      }).catch((error) => {
        console.log(error,"这是错误333")
        this.loading3=false;
        this.err3=true;
        if (!this.loading1 && !this.loading2 && !this.loading3) {
          showLoading("",false);
          if (this.err1 || this.err2 || this.err3) {
            showToast(stringsTo('commLoadingFailText'));
          }
        }
      })
    
    
    // let dataObject = data;
    //     let stateProps = {};
    //     data?.forEach(item => {
    //       //移动侦测
    //       if (`${item?.thingId}` === `${10021}`) {
    //         console.log('10021 ', item);
    //         stateProps.MoveSwitchValue = item?.value?.value;
    //       }
    //       //人形侦测
    //       if (`${item?.thingId}` === `${10022}`) {
    //         console.log('10022 ', item);
    //         stateProps.PeopleSwitchValue = item?.value?.value;
    //       }
    //       //大声侦测
    //       if (`${item?.thingId}` === `${10023}`) {
    //         console.log('10023', item);
    //         stateProps.LoudSwitchValue = item?.value?.value;
    //       }
    //       //侦测灵敏度
    //       if (`${item?.thingId}` === `${10024}`) {
    //         console.log('10024', item);
    //         stateProps.detectionSensitivityValue = item?.value?.value;
    //         this.detectionSensitivityDefaultValue = item?.value?.value;
    //       }
    //     });
    //     //侦测时间
    //     /* if (dataObject.AlarmFrequencyLevel) {
    //       stateProps.AlarmFrequencyLevel = dataObject.AlarmFrequencyLevel.value;
    //       stateProps.selectAlarmTime = dataObject.AlarmFrequencyLevel.value;
    //     }
    //     if (dataObject.AlarmSwitch) {
    //       if (typeof dataObject.AlarmSwitch.value === 'number') {
    //         if (dataObject.AlarmSwitch.value == 1) {
    //           stateProps.alarmPushValue = true;
    //         } else {
    //           stateProps.alarmPushValue = false;
    //         }
    //       } else {
    //         stateProps.alarmPushValue = dataObject.AlarmSwitch.value;
    //       }
    //       if (dataObject.AlarmSwitch.value) {
    //         this.getAlarmTime();
    //       }
    //     }
    //     if (dataObject.TrackSwitch) {
    //       if (typeof dataObject.TrackSwitch.value === 'number') {
    //         if (dataObject.TrackSwitch.value == 1) {
    //           stateProps.trackSwitchValue = true;
    //         } else {
    //           stateProps.trackSwitchValue = false;
    //         }
    //       } else {
    //         stateProps.trackSwitchValue = dataObject.TrackSwitch.value;
    //       }

    //       //   stateProps.trackSwitchValue = dataObject.TrackSwitch.value;
    //     }
    //     if (dataObject.LoudSwitch) {
    //       stateProps.LoudSwitchValue = dataObject.LoudSwitch.value;
    //     }
    //     if (dataObject.PeopleSwitch) {
    //       stateProps.PeopleSwitchValue = dataObject.PeopleSwitch.value;
    //     }
    //     if (dataObject.MoveSwitch) {
    //       stateProps.MoveSwitchValue = dataObject.MoveSwitch.value;
    //     }

    //     if (dataObject.FenceAttr) {
    //       if (dataObject.FenceAttr.value) {
    //         let fence_switch = JSON.parse(dataObject.FenceAttr.value);
    //         if (fence_switch.fence_switch == 1) {
    //           //1是打开 0是关闭
    //           stateProps.FenceSwitchValue = true;
    //         } else {
    //           stateProps.FenceSwitchValue = false;
    //         }
    //       } else {
    //         stateProps.FenceSwitchValue = false;
    //       }
    //     }
    //     if (dataObject.MotionDetectSensitivity) {
    //       if (dataObject.MotionDetectSensitivity.value === 1) {
    //         //低灵敏度
    //         stateProps.MotionDetectSensitivityTit = stringsTo('sensitivity_for_low_tit');
    //       } else if (dataObject.MotionDetectSensitivity.value === 5) {
    //         // 高灵敏度
    //         stateProps.MotionDetectSensitivityTit = stringsTo('sensitivity_for_high_tit');
    //       }
    //     }

    //     if (dataObject.SoundLightAlarm) {
    //       //声光报警、智能播报用的相同名称的物模型
    //       if (dataObject.SoundLightAlarm.value.length == 0) {
    //         stateProps.soundLightSwitchValue = false;
    //       } else {
    //         console.log('声光报警有值');
    //         let tempStr = JSON.parse(dataObject.SoundLightAlarm.value);
    //         stateProps.soundLightSwitchValue = tempStr.sound_light_alarm_switch; // 声光报警开关
    //       }
    //     } else {
    //       stateProps.soundLightSwitchValue = false;
    //       console.log('声光报警开关无值');
    //     }

    //     if (dataObject.OneKeyAlarm) {
    //       //一键警告
    //       if (dataObject.OneKeyAlarm.value.length == 0) {
    //         // 第一次绑定数据为空
    //         stateProps.oneKeySwitchValue = false;
    //       } else {
    //         console.log('一键警告有值');
    //         let tempStr = JSON.parse(dataObject.OneKeyAlarm.value);
    //         stateProps.oneKeySwitchValue = tempStr.one_key_alarm_switch; // 一键警告开关
    //       }
    //     } else {
    //       stateProps.oneKeySwitchValue = false;
    //       console.log('一键警告开关无值');
    //     } */

    //     // 统一设置从设备端获取的值
    //     this.setState(stateProps);
    //   })
    //   .catch(error => {
    //     // alert(JSON.stringify(error));
    //     showLoading(false);
    //     console.log(JSON.stringify(error));
    //   });
  }
  getAlarmTime() {
    aliAlarmEventCloudApi
      .getAlarmEventInterval(LetDevice.deviceID)
      .then(data => {
        console.log('IMIAlarmEventCloudApi---', data);
        let object = JSON.parse(data);
        if (object.eventInterval) {
          let time = 60;
          switch (this.state.selectAlarmTime) {
            case 0:
              time = 180;
              break;
            case 1:
              time = 300;
              break;
            case 2:
              time = 600;
              break;
            case 3:
              time = 1800;
              break;
            default:
              time = 300;
              break;
          }
          if (time != object.eventInterval) {
            this.setCloundTime(time);
          }
        }
      })
      .catch(error => {});
  }
  // 获取看家助手总开关，从云服务那边
  getSwitchValueByCloud() {
    const params = {
      Path: 'v1.0/imilab-01/app/cloudstorage/get-video-plan',
      ParamMap: {
        iotId: LetDevice.deviceID,
      },
      Method: 'GET',
    };
    this.loading2 = true;
    this.err2=false;
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        const stateProps = {};
        stateProps.alarmPushValue = item?.switchOn;
        console.log('customJsonParse(item)', item);
        this.noticeData = item;
        if (item?.switchOn) {
          // this.getPushSwitchValueByCloud();
        }
        if (item?.interval) {
          stateProps.selectAlarmTime = item.interval;
          stateProps.AlarmFrequencyLevel = item.interval;
        }
        
        if (item?.timeBegin === '00:00:00' && item?.timeEnd === '23:59:59') {
          stateProps.alarmTimeString = stringsTo('alarm_time_all');
        } else if (item?.timeBegin === '08:00:00' && item?.timeEnd === '20:00:00') {
          stateProps.alarmTimeString = stringsTo('alarm_time_day');
        } else if (item?.timeBegin === '20:00:00' && item?.timeEnd === '08:00:00') {
          stateProps.alarmTimeString = stringsTo('alarm_time_night');
        } else {
          stateProps.alarmTimeString =
            stringsTo('alarm_direction_custom') + '(' + item?.timeBegin?.slice(0, -3) + '-' + item?.timeEnd?.slice(0, -3) + ')';
        }
        this.setState(stateProps);
      })
      .catch(e => {
        console.log(JSON.stringify(e));
        this.err2=true;
      })
      .finally(() => {
        this.loading2=false
        if (!this.loading1 && !this.loading2 && !this.loading3) {
          showLoading("",false);
          if (this.err1 || this.err2 || this.err3) {
            showToast(stringsTo('commLoadingFailText'));
          }
        }
      });
  }
  //根据iotId获取事件消息推送的开关配置
  getPushSwitchValueByCloud() {
    const params = {
      Path: 'v1.0/imilab-01/device/config/notice/event/get',
      ParamMap: {
        iotId: LetDevice.deviceID,
      },
      Method: 'GET',
    };
    this.loading1 = true;
    this.err1=false;
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(item => {
        const stateProps = {};
        const switchList = customJsonParse(item);
        const list = [];
        switchList?.forEach(element => {
          // push事件总开关
          console.log('element', element);
          if (element.eventCode === 'ALL') {
            stateProps.pushValue = element?.openSwitch;
          }
          if (element.eventCode !== 'ALL') {
            list.push(element);
          }
        });
        this.watchList = list;
        console.log('customJsonParse(item)', customJsonParse(item));
        this.setState(stateProps);
      })
      .catch(e => {
        console.log(JSON.stringify(e));
        this.err1=true;
      })
      .finally(() => {
        this.loading1=false
        if (!this.loading1 && !this.loading2 && !this.loading3) {
          showLoading("",false);
          if (this.err1 || this.err2 || this.err3) {
            showToast(stringsTo('commLoadingFailText'));
          }
        }
      });
  }
  // 设置看家开关属性
  setSwitchValueByCloud() {
    const params = {
      Path: '/v1.0/imilab-01/app/cloudstorage/set-video-plan',
      ParamMap: {
        iotId: LetDevice.deviceID,
        ...this.noticeData,
      },
      Method: 'POST',
    };
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, false)
      .then(item => {
        this.getSwitchValueByCloud();
        showToast(stringsTo('settings_set_success'));
      })
      .catch(e => {
        showToast(I18n.t('operationFailed'));
        this.setState({
          alarmPushValue: this.state.alarmPushValue,
          selectAlarmTime: this.state.AlarmFrequencyLevel,
        });
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        showLoading("",false);
      });
  }

  // 设置推送开关属性
  setSwitchByCloud(value, index) {
    const params = {
      Path: 'v1.0/imilab-01/device/config/notice/event/set',
      ParamMap: {
        iotId: LetDevice.deviceID,
        noticeEventLs: [{openSwitch: value, eventCode: 'ALL', eventName: '总开关'}, ...this.watchList],
      },
      Method: 'POST',
    };
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(() => {
        this.setState({pushValue: value});
      })
      .catch(e => {
        this.setState({pushValue: !value});
        showToast(stringsTo('action_fail'));
        console.log('失败了', JSON.stringify(e));
      })
      .finally(() => {
        showLoading("",false);
      });
  }

  // 检测区域绘制点击事件处理
  handleDetectionAreaDrawPress = () => {
    console.log('点击检测区域绘制');
    this.props.navigation.push('DetectionAreaSetting', {
      callback: (switchValue) => {
        console.log('返回修改检测区域开关--', switchValue);
        if (switchValue !== 'failed') {
          // 这里可以根据需要更新相关状态
          console.log('检测区域开关状态:', switchValue);
        }
      }
    });
  }
  render() {
    /* let {
      showMoveSwitch,
      showSoundLightAlarm,
      showOneKeyAlarm,
      showTrackSwitch,
      showMotionDetectSensitivity,
      showPeopleSwitch,
      showLoudSwitch,
      showNoHuman,
      showSoundBroadcast,
      showFencesSwitch,
      showSynchronous056,
      hideDetect,
    } = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH'); */

    global.navigation = this.props.navigation;
    console.log('pushValue--' + this.state.alarmPushValue);
    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('alarmSettingText')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'housekeeping_assistant_back',
            },
          ]}
          right={[]}
        />
        <ListItmeWithSwitch
          title={stringsTo('alarmText')}
          value={this.state.alarmPushValue}
          onValueChange={value => {
            this.noticeData.switchOn = value;
            this.setSwitchValueByCloud();
          }}
          accessibilityLabel={['housekeeping_assistant_open', 'housekeeping_assistant_close']}
        />
        {/*看家开关*/}
        <ScrollView showsVerticalScrollIndicator={false}>
          {this.state.alarmPushValue ? (
            <View>
              {/* <ListItem
                title={stringsTo('recordType')}
                value={this.state.vipState !== 1 ? stringsTo('recordType1') : stringsTo('recordType2')}
                hideArrow={true}
                subtitle={this.state.vipState !== 1 ? stringsTo('recordTypeSub') : null}
                subtitleStyle={{width: (width * 2) / 3}}
                accessibilityLabel={'housekeeping_assistant_record_interval'}
              /> */}
              <ListItem
                title={stringsTo('alarmTimeSetting')}
                value={this.state.selectAlarmTime + stringsTo('time_minutes')}
                hideArrow={false}
                subtitle={stringsTo('settings_alarm_push_time_sub')}
                subtitleStyle={{width: (width * 2) / 3}}
                onPress={() => {
                  this.setState({showRecordTimeModal: true});
                }}
                accessibilityLabel={'housekeeping_assistant_time_interval'}
              />
              <ListItem
                title={stringsTo('alarm_time_set')}
                value={this.state.alarmTimeString}
                onPress={() => {
                  console.log('进入报警消息管理');
                  this.props.navigation.push('AlarmTmieSetting', {
                    alarmData: this.noticeData,
                    callback: alarmstr => {
                      console.log('返回修改侦测时间--', alarmstr);

                      this.setState({
                        alarmTimeString: alarmstr,
                      });
                    },
                  });
                }}
                accessibilityLabel={'housekeeping_assistant_alarm_massage_time'}
              />
              {this.is060?null:<ListItem
                title="检测区域绘制"
                subtitle="在直播画面中框出侦测区域，对所有检测类型生效"
                onPress={this.handleDetectionAreaDrawPress}
                accessibilityLabel={'detection_area_draw'}
              />}
              <Text
                style={{
                  color: '#00000080',
                  fontSize: 12,
                  paddingLeft: 15,
                  paddingTop: 5,
                  width: '100%',
                  backgroundColor: '#ffffff',
                }}>
                {stringsTo('alarm_info_set')}
              </Text>
              <ListItmeWithSwitch
                title={stringsTo('move_event')}
                value={this.state?.MoveSwitchValue}
                onValueChange={value => {
                  this.setProperties('10021', value, this.state?.MoveSwitchValue, 'MoveSwitchValue');
                }}
              />
              
              <ListItmeWithSwitch
                title={stringsTo('alarm_sound_detection')}
                value={this.state?.LoudSwitchValue}
                onValueChange={value => {
                  this.setProperties('10023', value, this.state?.LoudSwitchValue, 'LoudSwitchValue');
                }}
              />
              <ListItem
                title={stringsTo('people_event')}
                value={Boolean(this.state?.PeopleSwitchValue)?stringsTo('preset_opened'):stringsTo('preset_closed')}
                // onValueChange={value => {
                //   this.setProperties('10022', value, this.state?.PeopleSwitchValue, 'PeopleSwitchValue');
                // }}
                onPress={() => {
                    console.log('进入人形');
                  
                    this.props.navigation.push('PeopleEventSetting');
                  }}
              />
              {this.is060?null:<>
                <ListItem
                title={stringsTo('virtual_Fence')}
                value={Boolean(this.state?.VirtualFenceValue)?stringsTo('preset_opened'):stringsTo('preset_closed')}

                onPress={() => {
                    console.log('进入虚拟围栏');
                    this.props.navigation.push('VirtualFenceSetting', {
                      callback: (switchValue) => {
                        console.log('返回修改虚拟围栏开关--', switchValue);
                        if (switchValue !== 'failed') {
                          this.setState({
                            VirtualFenceValue: Boolean(switchValue)
                          });
                        }
                      }
                    });
                  }}
              />
               <ListItem
                title={stringsTo('family_protection')}

                onPress={() => {
                    console.log('进入家人守护');
                    this.props.navigation.push('FamilyProtectionSetting');
                  }}
              />
               <ListItem
                title={stringsTo('privacy_area_protection')}
                value={Boolean(this.state?.PrivacyAreaValue)?stringsTo('preset_opened'):stringsTo('preset_closed')}

                onPress={() => {
                    console.log('进入隐私区域保护');
                    this.props.navigation.push('PrivacyAreaSetting', {
                      callback: (switchValue) => {
                        console.log('返回修改隐私区域开关--', switchValue);
                        if (switchValue !== 'failed') {
                          this.setState({
                            PrivacyAreaValue: Boolean(switchValue)
                          });
                        }
                      }
                    });
                  }}
              />
              </>}
              <ListItem
                title={stringsTo('detection_sensitivity')}
                value={
                  this.state?.detectionSensitivityValue
                    ? stringsTo('sensitivity_for_high_tit')
                    : stringsTo('sensitivity_for_low_tit')
                }
                onPress={() => {
                  this.setState({detectionSensitivityVisible: true});
                }}
                accessibilityLabel={'侦测灵敏度'}
              />
              <Text
                style={{
                  color: '#00000080',
                  fontSize: 12,
                  paddingLeft: 15,
                  paddingTop: 5,
                  width: '100%',
                  backgroundColor: '#ffffff',
                }}>
                {stringsTo('alarm_info_push')}
              </Text>
              {/* <ListItmeWithSwitch
                title={stringsTo('message_push_switch')}
                value={this.state?.pushValue}
                onValueChange={value => {
                  this.setSwitchByCloud(value, 0);
                  console.log('报警消息推送开关', value);
                }}
              /> */}
                <ListItem
                  title={stringsTo('message_push_manager')}
                  subtitle={stringsTo('message_push_phone')}
                  onPress={() => {
                    console.log('进入报警消息管理');
                    this.props.navigation.push('WarnPushSetting');
                  }}
                  accessibilityLabel={'housekeeping_assistant_alarm_massage_detection'}
                />
            </View>
          ) : null}
          {/* {this.state.alarmPushValue ? (
            <View style={{width: '100%', backgroundColor: 'white'}}>
              {showOneKeyAlarm == showSoundLightAlarm &&
              showOneKeyAlarm == false &&
              showOneKeyAlarm == showMoveSwitch &&
              showOneKeyAlarm == showPeopleSwitch &&
              showOneKeyAlarm == showLoudSwitch ? null : (
                <View>
                  <Text
                    style={{
                      textAlign: 'left',
                      color: '#828282',
                      fontSize: 12,
                      paddingLeft: 14,

                      height: 30,
                      lineHeight: 30,
                      backgroundColor: '#F1F1F1',
                    }}>
                    {stringsTo('alarm_event_tit')}
                  </Text>
                </View>
              )}
              {showPeopleSwitch && !hideDetect ? (
                <ListItem
                  title={stringsTo('people_event')}
                  value={this.state.PeopleSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("PeopleDetectionSettingPage");
                    this.props.navigation.navigate(
                      showSynchronous056 ? 'PeopleDetectionSettingNewPage' : 'PeopleDetectionSettingPage',
                      {
                        callback: peopleStr => {
                          console.log('返回修改人形侦测--', peopleStr);
                          console.log('返回修改人形侦测--', peopleStr);
                          if (peopleStr != 'failed') {
                            if (peopleStr == '1') {
                              this.setState({PeopleSwitchValue: true});
                            } else {
                              this.setState({PeopleSwitchValue: false});
                            }
                          }
                        },
                      },
                    );
                  }}
                />
              ) : null}

              {showMoveSwitch && !hideDetect ? (
                <ListItem
                  title={stringsTo('move_event')}
                  value={this.state.MoveSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("PushMessageSettingPage");
                    this.props.navigation.navigate('MoveDetectionSettingPage', {
                      callback: moveStr => {
                        console.log('返回修改移动侦测--', moveStr);
                        if (moveStr != 'failed') {
                          if (moveStr == '1') {
                            this.setState({MoveSwitchValue: true});
                          } else {
                            this.setState({MoveSwitchValue: false});
                          }
                        }
                      },
                    });
                  }}
                />
              ) : null}

              {showLoudSwitch && !hideDetect ? (
                <ListItem
                  title={stringsTo('alarm_loud_switch')}
                  value={this.state.LoudSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("PushMessageSettingPage");
                    this.props.navigation.navigate('LoudDetectionSettingPage', {
                      callback: loudStr => {
                        console.log('返回修改异响侦测--', loudStr);
                        if (loudStr != 'failed') {
                          if (loudStr == '1') {
                            this.setState({LoudSwitchValue: true});
                          } else {
                            this.setState({LoudSwitchValue: false});
                          }
                        }
                      },
                    });
                  }}
                />
              ) : null}

              {showFencesSwitch && !hideDetect ? (
                <ListItem
                  title={stringsTo('fence_detect_switch')}
                  value={this.state.FenceSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("PushMessageSettingPage");
                    this.props.navigation.navigate('FenceDetectionSetting', {
                      callback: loudStr => {
                        console.log('返回修改异响侦测--', loudStr);
                        if (loudStr != 'failed') {
                          if (loudStr == '1') {
                            this.setState({FenceSwitchValue: true});
                          } else {
                            this.setState({FenceSwitchValue: false});
                          }
                        }
                      },
                    });
                  }}
                />
              ) : null}

              {showNoHuman ? (
                <ListItem
                  title={stringsTo('no_human')}
                  onPress={() => {
                    // if (this._isShareUser()) return;
                    this.props.navigation.push('NoHumanSetting');
                  }}
                  accessibilityLabel={'no_human'}
                />
              ) : null}

              {showSoundLightAlarm ? (
                <ListItem
                  title={stringsTo('soundLightAlarm')}
                  value={this.state.soundLightSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("AudioAlarmSetPage");
                    this.props.navigation.navigate('AudioAlarmSetPage', {
                      callback: soundlightStr => {
                        console.log('返回声光报警--', soundlightStr);
                        console.log('返回声光报警--', soundlightStr);
                        if (soundlightStr != 'failed') {
                          // if (soundlightStr  == "1"){
                          //     this.setState({PeopleSwitchValue:true})
                          // }else {
                          //     this.setState({PeopleSwitchValue:false})
                          // }
                          this.getAllValue();
                        }
                      },
                    });
                  }}
                />
              ) : null}

              {showSoundBroadcast ? (
                <ListItem
                  title={stringsTo('audio_broadcast')}
                  value={this.state.soundLightSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("AudioAlarmSetPage");
                    this.props.navigation.navigate('IntelligentAudioAlarmSetPage', {
                      callback: soundlightStr => {
                        if (soundlightStr != 'failed') {
                          this.getAllValue();
                        }
                      },
                    });
                  }}
                />
              ) : null}

              {showOneKeyAlarm ? (
                <ListItem
                  title={stringsTo('click_warning_tit')}
                  value={this.state.oneKeySwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                  onPress={() => {
                    // this.props.navigation.push("ClickWarningSetPage");
                    this.props.navigation.navigate('ClickWarningSetPage', {
                      enterType: 'houseKeep',
                      callback: warnStr => {
                        console.log('返回一键警告--', warnStr);
                        console.log('返回一键警告--', warnStr);
                        if (warnStr != 'failed') {
                          this.getAllValue();
                          // if (warnStr  == "1"){
                          //     this.setState({oneKeySwitchValue:true})
                          // }else {
                          //     this.setState({oneKeySwitchValue:false})
                          // }
                        }
                      },
                    });
                  }}
                />
              ) : null}

              {showTrackSwitch ? (
                <View>
                  {!hideDetect ? <View style={{height: 14, backgroundColor: '#F1F1F1'}} /> : null}
                  <ListItmeWithSwitch
                    title={stringsTo('settings_alarm_human_track_title')}
                    value={this.state.trackSwitchValue}
                    onValueChange={value => {
                      if (value) {
                        LetDevice.propertyOn('TrackSwitch')
                          .then(() => {
                            this.setState({
                              trackSwitchValue: value,
                            });
                          })
                          .catch(err => {
                            this.setState({
                              trackSwitchValue: !value,
                            });
                            showToast(I18n.t('operationFailed'));
                          });
                      } else {
                        LetDevice.propertyOff('TrackSwitch')
                          .then(() => {
                            this.setState({
                              trackSwitchValue: value,
                            });
                          })
                          .catch(err => {
                            this.setState({
                              trackSwitchValue: !value,
                            });
                            showToast(I18n.t('operationFailed'));
                          });
                      }
                    }}
                    accessibilityLabel={['housekeeping_assistant_human_on', 'housekeeping_assistant_human_off']}
                  />
                </View>
              ) : null}
              {
                <View>
                  <Text
                    style={{
                      textAlign: 'left',
                      color: '#828282',
                      fontSize: 12,
                      paddingLeft: 14,
                      lineHeight: 30,
                      height: 30,
                      backgroundColor: '#F1F1F1',
                    }}>
                    {stringsTo('alarm_event_manager')}
                  </Text>
                </View>
              }

              <ListItem
                title={stringsTo('alarmTimeSetting')}
                value={pushTime + stringsTo('time_minutes')}
                hideArrow={false}
                subtitle={stringsTo('settings_alarm_push_time_sub')}
                subtitleStyle={{width: (width * 2) / 3}}
                onPress={() => {
                  this.setState({showRecordTimeModal: true});
                }}
                accessibilityLabel={'housekeeping_assistant_time_interval'}
              />
              {showMotionDetectSensitivity ? (
                <ListItem
                  title={stringsTo('alarm_sensitivity')}
                  value={this.state.MotionDetectSensitivityTit}
                  onPress={() => {
                    // this.props.navigation.push("AlarmSensitivityPage");
                    this.props.navigation.navigate('AlarmSensitivityPage', {
                      callback: sensitvityStr => {
                        console.log('返回修改灵敏度--', sensitvityStr);
                        if (sensitvityStr != 'failed') {
                          this.setState({
                            MotionDetectSensitivityTit: sensitvityStr,
                          });
                        }
                      },
                    });
                  }}
                />
              ) : null}

              <ListItem
                title={stringsTo('message_push_manager')}
                subtitle={stringsTo('message_push_phone')}
                onPress={() => {
                  console.log('进入报警消息管理');
                  this.props.navigation.push('PushMessageSettingPage');
                }}
                accessibilityLabel={'housekeeping_assistant_alarm_massage_detection'}
              />
            </View>
          ) : null} */}

          {/*录像时长弹窗*/}
          {this._recordTimeModal()}
          {/* 侦测灵敏度弹窗 */}
          {this._recordDetectionSensitivityModal()}
        </ScrollView>
      </View>
    );
  }
  //推送时间间隔时长
  _recordTimeModal() {
    return (
      <View>
        <MessageDialog
          title={stringsTo('alarmTimeSetting')}
          visible={this.state.showRecordTimeModal}
          canDismiss={true}
          onDismiss={() => {
            this.setState({selectAlarmTime: this.state.AlarmFrequencyLevel, showRecordTimeModal: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelRecordTimeGap',
              callback: _ => {
                this.setState({selectAlarmTime: this.state.AlarmFrequencyLevel, showRecordTimeModal: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okRecordTimeGap',
              callback: _ => {
                this.setState({showRecordTimeModal: false});
                this.updateRecordTime();
              },
            },
          ]}>
          <ChoiceItem
            title={'3' + stringsTo('tip_time_minute')}
            containerStyle={{margin: 14, marginTop: 0, height: 60}}
            checked={this.state.selectAlarmTime == 3 ? true : false}
            onValueChange={value => {
              if (value == true) {
                this.setState({
                  selectAlarmTime: 3,
                });
              } else {
                this.setState({
                  selectAlarmTime: this.state.AlarmFrequencyLevel,
                });
              }
            }}
            accessibilityLabel={'housekeeping_assistant_time_interval_3_minutes'}
          />
          <ChoiceItem
            title={'5' + stringsTo('tip_time_minute')}
            containerStyle={{margin: 14, marginTop: 0, height: 60}}
            checked={this.state.selectAlarmTime == 5 ? true : false}
            onValueChange={value => {
              if (value == true) {
                this.setState({
                  selectAlarmTime: 5,
                });
              } else {
                this.setState({
                  selectAlarmTime: this.state.AlarmFrequencyLevel,
                });
              }
            }}
            accessibilityLabel={'housekeeping_assistant_time_interval_5_minutes'}
          />
          <ChoiceItem
            title={'10' + stringsTo('tip_time_minute')}
            containerStyle={{margin: 14, marginTop: 0, height: 60}}
            checked={this.state.selectAlarmTime == 10 ? true : false}
            onValueChange={value => {
              if (value == true) {
                this.setState({
                  selectAlarmTime: 10,
                });
              } else {
                this.setState({
                  selectAlarmTime: this.state.AlarmFrequencyLevel,
                });
              }
            }}
            accessibilityLabel={'housekeeping_assistant_time_interval_10_minutes'}
          />
          <ChoiceItem
            title={'30' + stringsTo('tip_time_minute')}
            containerStyle={{margin: 14, marginTop: 0, height: 60}}
            checked={this.state.selectAlarmTime == 30 ? true : false}
            onValueChange={value => {
              if (value == true) {
                this.setState({
                  selectAlarmTime: 30,
                });
              } else {
                this.setState({
                  selectAlarmTime: this.state.AlarmFrequencyLevel,
                });
              }
            }}
            accessibilityLabel={'housekeeping_assistant_time_interval_30_minutes'}
          />
        </MessageDialog>
      </View>
    );
  }
  //侦测灵敏度
  _recordDetectionSensitivityModal() {
    return (
      <View>
        <MessageDialog
          title={stringsTo('detection_sensitivity')}
          visible={this.state.detectionSensitivityVisible}
          canDismiss={true}
          onDismiss={() => {
            this.setState({detectionSensitivityVisible: false,detectionSensitivityValue:this.detectionSensitivityDefaultValue});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDetectionSensitivity',
              callback: _ => {
                this.setState({
                  detectionSensitivityValue: this.detectionSensitivityDefaultValue,
                  detectionSensitivityVisible: false,
                });
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okDetectionSensitivity',
              callback: _ => {
                this.setState({detectionSensitivityVisible: false});
                this.updateDetectionSensitivity();
              },
            },
          ]}>
          <ChoiceItem
            title={stringsTo('sensitivity_for_low_tit')}
            subtitle={stringsTo('sensitivity_for_low_subtit')}
            containerStyle={{margin: 14, marginTop: 0, paddingTop: 10, paddingBottom: 10}}
            checked={this.state.detectionSensitivityValue === 0 ? true : false}
            onValueChange={value => {
              this.setState({
                detectionSensitivityValue: value === true ? 0 : 1,
              });
            }}
            accessibilityLabel={'housekeeping_assistant_time_interval_3_minutes'}
          />
          <ChoiceItem
            title={stringsTo('sensitivity_for_high_tit')}
            subtitle={stringsTo('sensitivity_for_high_subtit')}
            containerStyle={{margin: 14, marginTop: 0, paddingTop: 10, paddingBottom: 10}}
            checked={this.state.detectionSensitivityValue === 1 ? true : false}
            onValueChange={value => {
              this.setState({
                detectionSensitivityValue: value === true ? 1 : 0,
              });
            }}
            accessibilityLabel={'housekeeping_assistant_time_interval_5_minutes'}
          />
        </MessageDialog>
      </View>
    );
  }
  //设置告警属性
  setProperties(key, value, defaultValue, stateKey) {
    showLoading(stringsTo('commWaitText'), true);
    const paramJson = JSON.stringify({msg_id: key, value});
    const stateProps = {};
    LetDevice.setProperties(true, LetDevice.deviceID, key, paramJson)
      .then(() => {
        stateProps[stateKey] = value;
        this.setState(stateProps);
        showToast(stringsTo('settings_set_success'))
      })
      .catch(e => {
        stateProps[stateKey] = defaultValue;
        this.setState(stateProps);
        showToast(I18n.t('operationFailed'));
      })
      .finally(() => {
        showLoading("",false);
      });
  }
  //设置灵敏度()
  updateDetectionSensitivity() {
    if (this.detectionSensitivityDefaultValue === this.state.detectionSensitivityValue) {
      return;
    }
    showLoading(stringsTo('commWaitText'), true);
    const paramJson = JSON.stringify({value: this.state.detectionSensitivityValue});
    LetDevice.setProperties(true, LetDevice.deviceID, '10024', paramJson)
      .then(re => {
        this.detectionSensitivityDefaultValue = this.state.detectionSensitivityValue;
        showToast(stringsTo('settings_set_success'))
      })
      .catch(e => {
        console.log('eeeeeeee', JSON.stringify(e), paramJson, LetDevice.deviceID);
        this.setState({
          detectionSensitivityValue: this.detectionSensitivityDefaultValue,
        });
        showToast(I18n.t('operationFailed'));
      })
      .finally(() => {
        showLoading("",false);
      });
  }
  //设置录像时长
  updateRecordTime() {
    this.noticeData.interval = this.state.selectAlarmTime;
    console.log('推送时间间隔--', this.state.selectAlarmTime);
    this.setSwitchValueByCloud();
    // showLoading(stringsTo('commWaitText'), true);
    // let time = 60;
    // switch (this.state.selectAlarmTime) {
    //   case 0:
    //     time = 180;
    //     break;
    //   case 1:
    //     time = 300;
    //     break;
    //   case 2:
    //     time = 600;
    //     break;
    //   case 3:
    //     time = 1800;
    //     break;
    //   default:
    //     time = 300;
    //     break;
    // }
    // let params = {AlarmFrequencyLevel: this.state.selectAlarmTime};
    // LetDevice.setPropertyCloud(params)
    //   .then(() => {
    //     this.setState({
    //       AlarmFrequencyLevel: this.state.selectAlarmTime,
    //     });
    //     showToast(I18n.t('settings_set_success'));
    //     showLoading(false);
    //     this.setCloundTime(time);
    //     console.log('设置报警时间间隔---' + this.state.selectAlarmTime);

    //     IMILogUtil.uploadClickEventValue({AlarmInterval: time});
    //   })
    //   .catch(() => {
    //     this.setState({
    //       selectAlarmTime: this.state.AlarmFrequencyLevel,
    //     });
    //     showToast(I18n.t('operationFailed'));
    //     showLoading(false);
    //   });
  }
  //设置云端推送时间
  setCloundTime(time) {
    aliAlarmEventCloudApi
      .setAlarmEventInterval(LetDevice.deviceID, time)
      .then(res => {
        console.log('IMIAlarmEventCloudApi--res----', res);
      })
      .catch(error => {});
  }

  //重写安卓物理返回键
  onBackAndroid() {
    if (this.state.showCalibrationModal) {
      this.setState({showCalibrationModal: false});
      return true;
    }
    return super.onBackAndroid();
  }

  //设置推送消息
  updatePushMsg(value, event) {
    console.log('消息--', value);
    // alert(event);
    const params = {
      Path: 'api/app/notice/event/set',
      Method: 'PUT',
      ParamMap: {
        iotId: LetDevice.deviceID,
        productKey: LetDevice.model,
        event: event,
        noticeEnabled: value,
      },
    };
    console.log('开关推送传值params--', params);
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(data => {
        showToast(I18n.t('settings_set_success'));
        showLoading("",false);
      })
      .catch(error => {
        console.log('设置推送数据错误--', error);
        // alert('111111111');
        if (event === 'MOVE') {
          this.setState({PeopleSwitchPushValue: !value});
        }
        if (event === 'SOUND') {
          this.setState({LoudSwitchPushValue: !value});
        }
        showToast(I18n.t('operationFailed'));
        showLoading("",false);
        console.log('sendUserServerRequest error ' + error);
      });
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },

  functionSettingStyle: {
    color: '#0000007F',
    fontSize: 12,
    marginTop: 23,
    marginLeft: 14,
  },
  modalTit: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 60,
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    textAlign: 'center',
    // backgroundColor: 'red',
    fontWeight: 'bold',
    justifyContent: 'center',
    backgroundColor: 'red',
  },
  modalSubTit: {
    fontSize: 12,
    color: '#7F7F7F',
    lineHeight: 40,
    paddingLeft: 14,
    // backgroundColor: 'green',
    alignItems: 'center',
    justifyContent: 'center',
    // backgroundColor:'blue',
  },
  distanceModalTit: {
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    // backgroundColor:'green',
    marginTop: 16,
  },
  distanceModalSubTit: {
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    fontSize: 12,
    color: '#7F7F7F',
    // backgroundColor:'red',
    marginTop: -10,
    marginBottom: 14,
  },
});
