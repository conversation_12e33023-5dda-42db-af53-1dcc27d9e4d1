import React from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  Dimensions,
  ART,
  PanResponder,
  Platform,
  BackHandler,
  PixelRatio, TouchableOpacity, ScrollView
} from 'react-native';

let {
  Surface,
  Shape,
  Path
} = ART;

import { Device, Service, Host } from 'miot';
import { MessageDialog, NavigationBar } from 'mhui-rn';
import { LoadingDialog } from 'miot/ui/Dialog';
import Toast from '../components/Toast';
import Util from "../util2/Util";
import VersionUtil from "../util/VersionUtil";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtilV2, {
  PIID_FENCE_AREA,
  PIID_FENCE_DIRECTION,
  PIID_FENCE_SWITCH,
  PIID_PRIVATE_AREA_SWITCH,
  SIID_AI_CUSTOM,
  SIID_FENCE_DETECTION
} from "../util/AlarmUtilV2";
import { styles } from "../setting/SettingStyles";
import Radio from "miot/ui/Radio";
import AlbumHelper from "../util/AlbumHelper";

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框的到画面边缘的外边距
const CIRCLE_RADIUS = 3; // 矩形线框这个角上实心圆的半径
const DEVIATION_VALUE = 20; // 线框宽度、圆半径、除法取整引起的误差，试用
const VALID_TOUCH_RANGE = 15; // 四个边角的有效触摸范围
const TAG = "MonitorAreaModifyPageV2";
export default class MonitorAreaModifyPageV2 extends React.Component {
  static navigationOptions = (navigation) => {
    return { headerTransparent: true, header: null };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      progressing: false,
      showCover: true,
      canSave: false,
      showSaveDialog: false,
      items: [
        { title: LocalizedStrings['come_in_area'], subtitle: LocalizedStrings['come_in_area_desc'], isChecked: false, value: 1 },
        { title: LocalizedStrings['leave_area'], subtitle: LocalizedStrings['leave_area_desc'], isChecked: true, value: 2 }
      ],
      dir: 2
    };
    this.isMoving = false
    this.timeStamp = Date.now();
    // 用户划定线框区域的左上和右下角坐标
    this.rectDatas = this.props.navigation.getParam('areaData');
    this.privateSwitch = this.props.navigation.getParam('privateSwitch');
    this.privateDatas = this.props.navigation.getParam('privateAreaData');
    this.styleType = this.props.navigation.getParam('styleType') ? this.props.navigation.getParam('styleType') : 1;
    this.styleSource = [
      require("../../Resources/Images/area_style_bg_one.webp"),
      require("../../Resources/Images/area_style_bg_two.webp"),
      require("../../Resources/Images/area_style_bg_three.webp"),
      require("../../Resources/Images/area_style_bg_four.webp")][this.styleType-1];
    // 有效看护区域矩形背景框的左上和右下角坐标
    this.rectBackGround = this.rectBackGround = [Math.floor(this.rectDatas[0] / itemWidth) * itemWidth, Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
    Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth, Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight];
    this.distanceData = [0, 0, 0, 0];
    this.touchPosition = 0; // 开始拖拽的点的坐标位或得出的值

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        console.log("onPanResponderGrant")
        let x = evt.nativeEvent.locationX;// 开始触摸点相对于父View的横坐标
        let y = evt.nativeEvent.locationY; // 开始触摸点相对于父View的纵坐标
        this.touchBeginCoordX = x;
        this.touchBeginCoordY = y;

        this.distanceData = [0, 0, 0, 0];
        let smallest = [VALID_TOUCH_RANGE, VALID_TOUCH_RANGE]; // 矩形框四个角的有效触摸范围，x轴和y轴方向均在VALID_TOUCH_RANGE以内
        let positionCoord = [0, 0]; // 用户开始触摸点的x和y轴坐标

        // 触摸点在线框左上角，则设定触摸坐标为[8,4]
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        if (this.distanceData[0] < smallest[0]) { // 触摸点在线框左上角坐标的x轴方向的有效范围内
          positionCoord[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        if (this.distanceData[1] < smallest[1]) { // 触摸点在线框左上角坐标y轴方向的有效范围内
          positionCoord[1] = 4;
        }

        // 触摸点在线框右下角，则设定触摸坐标为[2,1]
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        if (this.distanceData[2] < smallest[0]) { // 触摸点在线框右下角坐标的x轴方向的有效范围内
          positionCoord[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        if (this.distanceData[3] < smallest[1]) { // 触摸点在线框右下角坐标y轴方向的有效范围内
          positionCoord[1] = 1;
        }
        this.touchPosition = positionCoord[0] | positionCoord[1]; // 通过位或运算得出共有12，3，6，9四个值
      },

      onPanResponderMove: (evt, gestureState) => {
        /* let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY; */
        if (Math.abs(gestureState.dx) <= 5 && Math.abs(gestureState.dy) <= 5) {
          console.log("---------------------------没滑动距离")
          return;
        }
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy;// dy 从触摸操作开始时的累计纵向位移
        let pointChange = false;
        switch (this.touchPosition) {
          case 12: { // 拖动左上角 触摸点[8,4]
            if (x >= REACT_MARGIN && x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: { // 拖动右上角 触摸点[2,4]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 3: { // 拖动右下角 触摸点[2,1]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: { // 拖动左下角 触摸点[8,1]
            if (x >= 3 && x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        if (pointChange) {
          this.setState({ canSave: true });
        }

      },

      onPanResponderRelease: () => {
        console.log("onPanResponderRelease");
      },

      onPanResponderTerminate: () => {
        console.log("onPanResponderTerminate")
      }
    });

  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this._getDirectionSpecData();

    this.timeoutCrop && clearTimeout(this.timeoutCrop);
    this.timeoutCrop = setTimeout(() => {
      Host.file.isFileExists(VersionUtil.settingsImgPathV2).then((success) => {
        console.log("isFileExists===", success);
        this.existsSettingsImg = success;
        this.cropImage();
        this.forceUpdate();
      }).catch((err) => {
        console.log("err=", JSON.stringify(err));
      });

    }, 100);
  }

  async cropImage() {
    let imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }`;
    if (!this.existsSettingsImg) {
      imageSource = require("../../Resources/Images/ai2_monitor_img.webp");
    } else {
      if (Platform.OS !== "ios") {
        imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }?timestamp=${ this.timeStamp }`;
      }
    }
    if (!this.existsSettingsImg) {
      // 未获取到截图，就不要去截图了
      this.setState({ showBg: true, showCover: false });
      return;
    }
    try {
      let source = `file://${ imageSource }`;
      if (Platform.OS === "ios") {
        source = imageSource;
      }
      Image.getSize(source, (width, height) => {
        console.log("pic info", width, height);
        let targetName = `crop/crop_area_${ Date.now() }.jpg`;
        let cropWidth = Math.abs(this.privateDatas[2] - this.privateDatas[0]);
        let cropHeight = Math.abs(this.privateDatas[3] - this.privateDatas[1]);

        let pxWidth = parseInt(width * cropWidth / viewWidth);
        let pxHeight = parseInt(width * cropHeight / viewWidth);
        let leftX = parseInt(width * this.privateDatas[0] / viewWidth);
        let leftY = parseInt(height * this.privateDatas[1] / viewHeight);

        let params = {
          offset: { x: leftX, y: leftY },
          size: { width: pxWidth, height: pxHeight },
          displaySize: { width: pxWidth, height: pxHeight }
        };

        console.log("crop params", params, viewWidth, viewHeight, pxWidth, pxHeight, leftX, leftY);
        console.log("crop settingsImgPathV2", VersionUtil.settingsImgPathV2);
        Host.file.cropImage(targetName, VersionUtil.settingsImgPathV2, params).then((res) => {
          console.log("is success", res);
          let filepath = `${ Host.file.storageBasePath }/${ targetName }`;
          let uri = { uri: filepath };
          this.setState({ showCover: true, coverImageUri: uri }, () => {
            setTimeout(() => {
              this.setState({ showBg: true });
            }, 200);
          });
        }).catch((error) => {
          this.setState({ showBg: true });
        });
      }, (failure) => {
        this.setState({ showBg: true });
      });
    } catch (e) {
      this.setState({ showBg: true });
    }
  }

  _getDirectionSpecData() {
    this.setState({ progressing: true });
    let params = [{ "sname": SIID_AI_CUSTOM, "pname": PIID_FENCE_DIRECTION }];
    AlarmUtilV2.getSpecPValue(params, 2, TAG).then((vo) => {
      let newStates = { progressing: false };
      let isFailed = false;

      if (vo[0]?.code == 0) {
        // 0: None
        // 1: Enter
        // 2: Leave
        // 3: Enter Leave
        let dir = vo[0].value;
        let directionItems = JSON.parse(JSON.stringify(this.state.items));
        for (let i = 0; i < directionItems.length; i++) {
          directionItems[i].isChecked = (dir & directionItems[i].value) > 0;
        }
        newStates.items = directionItems;
        newStates.dir = dir;
      } else {
        isFailed = true;
      }
      this.setState(newStates, () => {
        isFailed ? Toast.fail('c_get_fail') : null;
      });
    }).catch(() => {
      Toast.fail('c_get_fail');
      this.setState({ progressing: false });
    });

  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings["fence_area_edit"],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({ showSaveDialog: true });
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.COMPLETE : null,
          onPress: () => {
            this.onSubmit();
            this.props.navigation.getParam('callback')(this.rectDatas); // 回调当前用户选定的线框坐标值
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  // 点击保存看护区域坐标数据
  onSubmit() {
    this.setState({ progressing: true });
    let positions = [Math.ceil(this.rectDatas[0] / viewWidth * 100), Math.ceil(this.rectDatas[1] / viewHeight * 100),
    Math.ceil(this.rectDatas[2] / viewWidth * 100), Math.ceil(this.rectDatas[3] / viewHeight * 100)];
    // let valueString = JSON.stringify([{ area: `[${ positions[0] }, ${ positions[1] }],[${ positions[2] }, ${ positions[3] }]` }]);
    let valueString = JSON.stringify([{ pos: `[${positions[0]}, ${positions[1]}],[${positions[2]}, ${positions[3]}]` }]);
    console.log("onSubmit params=", valueString);

    let params = [{ "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_AREA, value: valueString },
    { "sname": SIID_AI_CUSTOM, "pname": PIID_FENCE_DIRECTION, value: this.state.dir }];

    AlarmUtilV2.setSpecPValue(params, TAG).then((vo) => {
      this.setState({ progressing: false });
      if (vo[0].code == 0) {
        // Toast.success("c_set_success");
        this.props.navigation.goBack();
      } else {
        Toast.fail("action_failed");
      }
    }).catch((err) => {
      this.setState({ progressing: false });
      Toast.fail("action_failed", err);
    });
  }

  render() {
    // let imageSource = `${Host.file.storageBasePath}/${VersionUtil.settingsImgPath}`;
    // if (Platform.OS !== "ios") {
    //   imageSource = `${Host.file.storageBasePath}/${VersionUtil.settingsImgPath}?timestamp=${this.timeStamp}`;
    // }
    // imageSource = { uri: imageSource };


    let imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }`;

    if (!this.existsSettingsImg) {
      imageSource = require("../../Resources/Images/ai2_monitor_img.webp");
    } else {
      if (Platform.OS !== "ios") {
        imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }?timestamp=${ this.timeStamp }`;
      }
      imageSource = { uri: imageSource };
    }

    let paths = [];
    // 全部处于隐私区域内
    let allInPrivate = 0;
    if (!this.privateSwitch) {
      // 隐私区域未开启
      allInPrivate = 2;
    } else {
      if (this.rectDatas[0] >= this.privateDatas[2]
        || this.rectDatas[2] <= this.privateDatas[0]
        || this.rectDatas[1] >= this.privateDatas[3]
        || this.rectDatas[3] <= this.privateDatas[1]
        || (this.rectDatas[0] <= this.privateDatas[0] && this.rectDatas[1] <= this.privateDatas[1] && this.rectDatas[2] >= this.privateDatas[2] && this.rectDatas[3] >= this.privateDatas[3])) {
        // 全部位于隐私区域内
        allInPrivate = 1;
      } else if (this.rectDatas[0] >= this.privateDatas[0]
        && this.rectDatas[1] >= this.privateDatas[1]
        && this.rectDatas[2] <= this.privateDatas[2]
        && this.rectDatas[3] <= this.privateDatas[3]) {
        //全部位于在区域内
        allInPrivate = 2;
      } else {
        //隐私区域与围栏有交集
        allInPrivate = 0;
        paths = this.getPaths()
      }
    }
    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    let private_rectangle_path = Path()
      .moveTo(this.privateDatas[0], this.privateDatas[1])
      .lineTo(this.privateDatas[2], this.privateDatas[1])
      .lineTo(this.privateDatas[2], this.privateDatas[3])
      .lineTo(this.privateDatas[0], this.privateDatas[3])
      .close();

    // 表示有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();

    let background_path_all = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_one = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_two = Path()
      .moveTo(this.rectDatas[0], 0)
      .lineTo(viewWidth, 0)
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(this.rectDatas[0], this.rectDatas[1])
      .close();

    let background_path_three = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(viewWidth, viewHeight)
      .lineTo(this.rectDatas[0], viewHeight)
      .close();

    let background_path_four = Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .close();

    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1] - CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1] - CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2], this.rectDatas[3] + CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3] + CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let cropWidth = Math.abs(this.privateDatas[2] - this.privateDatas[0]);
    let cropHeight = Math.abs(this.privateDatas[3] - this.privateDatas[1]);

    return (<View style={{
      display: "flex",
      height: "100%",
      width: "100%",
      flex: 1,
      flexDirection: "column",
      backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF"
    }}>
      {this.renderTitleBar()}

      <ScrollView showVerticalScrollIndicator={false} alwaysBounceVertical={false}>
        <Text style={{
          fontSize: 12,
          color: "#666666",
          marginLeft: 27,
          marginTop: 28
        }}>{LocalizedStrings.setting_monitor_area_paint}</Text>

        <View {...this.panResponder.panHandlers}>
          <ImageBackground style={{
            width: viewWidth,
            height: viewHeight,
            marginHorizontal: 24,
            marginTop: 13,
            marginBottom: 20
          }} source={require("../../Resources/Images/ai2_monitor_img.webp")} imageStyle={{ borderRadius: 0 }}>
            {/* 防止直播流截图不存在，不能让其做ImageBackground。需要让一个固定图片来兜底 */}
            {/*<Image style={{*/}
            {/*  width: viewWidth,*/}
            {/*  height: viewHeight*/}
            {/*}} source={imageSource} key={this.timeStamp} />*/}

            {
              !this.privateSwitch ? <Image style={{
                width: viewWidth,
                height: viewHeight
              }} source={imageSource} key={this.timeStamp} /> : null
            }
            {/* 展示背景图 */}
            {
              this.privateSwitch ? <Image style={{
                width: viewWidth,
                height: viewHeight
              }} resizeMode={'cover'} source={this.styleSource} key={this.timeStamp}
              /> : null
            }

            {this.state.showCover && this.privateSwitch ?
              <View style={{
                position: 'absolute',
                width: cropWidth,
                height: cropHeight,
                left: this.privateDatas[0],
                top: this.privateDatas[1]
              }}>
                <Image
                  source={this.state.coverImageUri}
                  style={{
                    width: cropWidth,
                    height: cropHeight,
                    resizeMode: 'contain'
                  }}
                  onError={(error) => {
                    console.log("image load error", error);
                  }}
                />
              </View>
              : null
            }

            <View style={{ position: 'absolute' }}>
              <Surface width={viewWidth} height={viewHeight}>
                {/* <Shape d={background_path} fill="#32BAC0" opacity="0.3" /> */}
                <Shape d={background_path_one} fill="#000000" opacity="0.5" />
                <Shape d={background_path_two} fill="#000000" opacity="0.5" />
                <Shape d={background_path_three} fill="#000000" opacity="0.5" />
                <Shape d={background_path_four} fill="#000000" opacity="0.5" />
                {
                  allInPrivate === 1 ? <Shape d={draggable_rectangle_path} stroke="#ff0000" strokeWidth={1} /> :
                    allInPrivate === 2 ?
                      <Shape d={draggable_rectangle_path} stroke="#32BAC0" strokeWidth={1} /> : null
                }
                {allInPrivate === 0 ?
                  paths.map((item, index) => {
                    return (
                      <Shape key={index} d={item.path} stroke={item.color} strokeWidth={1} />
                    )
                  }) : null
                }

                {/*<Shape d={private_rectangle_path} stroke="#00ff00" strokeWidth={1} />*/}
                <Shape d={top_left_circle} fill="#32BAC0" />
                <Shape d={top_right_circle} fill="#32BAC0" />
                <Shape d={bottom_right_circle} fill="#32BAC0" />
                <Shape d={bottom_left_circle} fill="#32BAC0" />

              </Surface>

            </View>
          </ImageBackground>
        </View>

        {this._renderFunctionSettings()}
      </ScrollView>
      <LoadingDialog
        visible={this.state.progressing}
        message={LocalizedStrings['c_setting']}
        onModalHide={() => this.setState({ progressing: false })} />
      {this._renderBackDialog()}
    </View>);
  }

  /* 围栏方向 */
  _renderFunctionSettings() {
    let darkMode = Util.isDark();
    return (
      <View>
        <Text style={{
          color: "#666666",
          fontSize: 14,
          fontWeight: "400",
          paddingHorizontal: 24,
          lineHeight: 21,
          fontFamily: "MI Lan Pro"
        }}>{LocalizedStrings['setting_fence_direction']}</Text>
        <View style={styles.whiteblank} />
        {/*<Text style={styles.desc_title}>{LocalizedStrings['setting_monitor_scene']}</Text>*/}

        {
          this.state.items.map((item, index) => {
            return (
              <TouchableOpacity
                key={`direction_${index}`}
                onPress={() => {
                  this.changeCheck(item, index);
                }}
              >
                <View
                  style={[{ marginTop: 15 }]}>
                  <View style={{ marginVertical: 15, marginHorizontal: 28, flexDirection: 'row', alignItems: "center" }}>

                    <View style={{ flex: 1 }}>
                      <Text numberOfssLines={3}
                        style={{ lineHeight: 22, fontSize: 16, fontWeight: "bold", color: "#000000" }}>{item.title}</Text>
                      <Text numberOfssLines={6}
                        style={{ lineHeight: 18, fontSize: 13, color: "#00000099" }}>{item.subtitle}</Text>
                    </View>
                    <Image
                      source={item.isChecked?require('../../Resources/Images/icon_no_border_selected.png'): Util.isDark() ? require('../../Resources/Images/icon_no_border_unselected_d.png') : require('../../Resources/Images/icon_no_border_unselected.png')}
                      style={{ width: 22, height: 22 }}/>
                    {/*<Radio isChecked={item.isChecked} changeCheck={() => this.changeCheck(item, index)} />*/}
                  </View>
                </View>
              </TouchableOpacity>
            )
          })
        }
        <View style={{ height: 40 }} />
      </View>
    );

  }
  changeCheck(item, index) {
    if (item.isChecked) {
      // 不能全部取消，必须保留一个
      const filteredData = this.state.items.filter(item => item.isChecked);
      const count = filteredData.length;
      if (count == 1) {
        Toast.show(LocalizedStrings['setting_fence_direction_warning']);
        return;
      }
    }
    let data = JSON.parse(JSON.stringify(this.state.items));
    data[index].isChecked = !item.isChecked;
    // 遍历
    let upValue = 0;
    for (let i = 0; i < data.length; i++) {
      if (data[i].isChecked) {
        upValue = upValue | data[i].value;
      }
    }

    this.setState({ items: data, dir: upValue, canSave: true });

  }
  getPaths() {
    let fStartX = this.rectDatas[0];// 起始x
    let fStartY = this.rectDatas[1];// 起始y
    let fEndX = this.rectDatas[2];// 结束点x
    let fEndY = this.rectDatas[3];// 结束点y

    let pStartX = this.privateDatas[0];// 起始x
    let pStartY = this.privateDatas[1];// 起始y
    let pEndX = this.privateDatas[2];// 结束点x
    let pEndY = this.privateDatas[3];// 结束点y
    let redPath = Path();
    let greenPath = Path();
    let paths = [];
    if (fStartX < pStartX && fStartY < pStartY && fEndX > pEndX && fEndY < pEndY) {
      // 包裹隐私区域上半部分
      redPath.moveTo(pStartX, fEndY)
        .lineTo(fStartX, fEndY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(pEndX, fEndY);

      greenPath.moveTo(pStartX, fEndY)
        .lineTo(pEndX, fEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX < pStartX && fStartY < pStartY && fEndX < pEndX && fEndY > pEndY) {
      // 包裹隐私区域左半部分
      redPath.moveTo(fEndX, pStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, pEndY);
      greenPath.moveTo(fEndX, pStartY)
        .lineTo(fEndX, pEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX >= pStartX && fStartY < pStartY && fEndX >= pEndX && fEndY > pEndY) {
      //包裹隐私区域右半部分
      redPath.moveTo(fStartX, pStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(fStartX, fEndY)
        .lineTo(fStartX, pEndY);

      greenPath.moveTo(fStartX, pStartY)
        .lineTo(fStartX, pEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX < pStartX && fStartY < pStartY && fEndX <= pEndX && fEndY < pEndY) {
      // 包裹隐私区域左上部分
      redPath.moveTo(pStartX, fEndY)
        .lineTo(fStartX, fEndY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, pStartY);

      greenPath.moveTo(fEndX, pStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(pStartX, fEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX >= pStartX && fStartY < pStartY && fEndX > pEndX && fEndY < pEndY) {
      // 包裹隐私区域右上部分
      redPath.moveTo(fStartX, pStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(pEndX, fEndY);

      greenPath.moveTo(fStartX, pStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(pEndX, fEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX >= pStartX && fStartY < pStartY && fEndX <= pEndX && fEndY < pEndY) {
      // 包裹隐私区域中上部分 可视区域中上
      redPath.moveTo(fStartX, pStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, pStartY);

      greenPath.moveTo(fStartX, pStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, pStartY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX >= pStartX && fStartY > pStartY && fEndX <= pEndX && fEndY >= pEndY) {
      // 包裹隐私区域中下部分 可视区域中下
      redPath.moveTo(fStartX, pEndY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, pEndY);

      greenPath.moveTo(fStartX, pEndY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, pEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX > pStartX && fStartY < pStartY && fEndX <= pEndX && fEndY > pEndY) {
      // 上下超出
      redPath
        .moveTo(fStartX, pStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, pStartY);

      let redPathTwo = Path()
        .moveTo(fStartX, pEndY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, pEndY);

      greenPath
        .moveTo(fStartX, pStartY)
        .lineTo(fStartX, pEndY);

      let greenPathTwo = Path()
        .moveTo(fEndX, pStartY)
        .lineTo(fEndX, pEndY);

      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: redPathTwo, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
      paths.push({ path: greenPathTwo, color: "#32BAC0" });
    } else if (fStartX < pStartX && fStartY > pStartY && fEndX <= pEndX && fEndY < pEndY) {
      // 处于课时区域高度内，包裹左侧部分隐私区域
      redPath.moveTo(pStartX, fStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(pStartX, fEndY);

      greenPath.moveTo(pStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(pStartX, fEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX < pStartX && fStartY > pStartY && fEndX <= pEndX && fEndY >= pEndY) {
      // 左下角交集
      redPath.moveTo(pStartX, fStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, pEndY);

      greenPath.moveTo(pStartX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, pEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX >= pStartX && fStartY > pStartY && fEndX > pEndX && fEndY >= pEndY) {
      // 右下角交集
      redPath.moveTo(fStartX, pEndY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, fStartY)
        .lineTo(pEndX, fStartY);

      greenPath.moveTo(fStartX, pEndY)
        .lineTo(fStartX, fStartY)
        .lineTo(pEndX, fStartY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX < pStartX && fStartY > pStartY && fEndX > pEndX && fEndY >= pEndY) {
      // 包裹底部
      redPath.moveTo(pStartX, fStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(fEndX, fEndY)
        .lineTo(fEndX, fStartY)
        .lineTo(pEndX, fStartY);

      greenPath.moveTo(pStartX, fStartY)
        .lineTo(pEndX, fStartY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    } else if (fStartX < pStartX && fStartY > pStartY && fEndX >= pEndX && fEndY < pEndY) {
      // 左右超出
      redPath.moveTo(pStartX, fStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(pStartX, fEndY);

      let redPathTwo = Path()
        .moveTo(pEndX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(pEndX, fEndY);

      greenPath
        .moveTo(pStartX, fStartY)
        .lineTo(pEndX, fStartY);

      let greenPathTwo = Path()
        .moveTo(pStartX, fEndY)
        .lineTo(pEndX, fEndY);

      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: redPathTwo, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
      paths.push({ path: greenPathTwo, color: "#32BAC0" });
    } else if (fStartX >= pStartX && fStartY > pStartY && fEndX > pEndX && fEndY < pEndY) {
      // 处于课时区域高度内，包裹右侧部分隐私区域
      redPath.moveTo(pEndX, fStartY)
        .lineTo(fEndX, fStartY)
        .lineTo(fEndX, fEndY)
        .lineTo(pEndX, fEndY);

      greenPath.moveTo(pEndX, fStartY)
        .lineTo(fStartX, fStartY)
        .lineTo(fStartX, fEndY)
        .lineTo(pEndX, fEndY);
      paths.push({ path: redPath, color: "#ff0000" });
      paths.push({ path: greenPath, color: "#32BAC0" });
    }
    return paths;
  }

  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={LocalizedStrings['exit_change_disappear']}
        messageStyle={{ textAlign: "center" }}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
            }
          },
          {
            text: LocalizedStrings["exit"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
              this.props.navigation.goBack();
            }
          }
        ]}
      />
    )
  }
  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.timeoutCrop && clearTimeout(this.timeoutCrop);
    AlbumHelper.deleteFolderFile("crop");
  }

  onBackHandler = () => {
    // if (this.state.canSave) {
    //   this.onSubmit();
    //   return true;
    // }
    if (this.state.canSave) {
      this.setState({ showSaveDialog: true });
      return true;
    }
    return false;
  };

}