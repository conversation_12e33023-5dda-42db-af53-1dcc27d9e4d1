'use strict';
import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  ART, Platform, BackHandler, TouchableWithoutFeedback
} from 'react-native';
let { Surface, Shape, Path } = ART;

import { Device, Service } from 'miot';
import Host from "miot/Host";
import NavigationBar from "miot/ui/NavigationBar";
import { LoadingDialog } from 'miot/ui/Dialog';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import Toast from '../components/Toast';
import VersionUtil from '../util/VersionUtil';
import Util from "../util2/Util";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from "../setting/SettingStyles";
import { AccessibilityRoles, getAccessibilityConfig } from "mhui-rn/dist/utils/accessibility-helper";
import { ChoiceDialog, MessageDialog } from "mhui-rn";
import { strings as I18n } from "miot/resources";
import AlarmUtilV2, {
  PIID_FENCE_AREA, PIID_FENCE_DIRECTION,
  PIID_FENCE_SWITCH,
  PIID_MOVE_SWITCH, PIID_PRIVATE_AREA_PARAMS, PIID_PRIVATE_AREA_SWITCH, SIID_AI_CUSTOM,
  SIID_AI_DETECTION,
  SIID_FENCE_DETECTION
} from "../util/AlarmUtilV2";
import { Event } from '../config/base/CfgConst';
import TrackUtil from '../util/TrackUtil';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距
const directionOptions = [{
  title: LocalizedStrings['come_in_area']
}, {
  title: LocalizedStrings['leave_area']
}, {
  title: LocalizedStrings['pass_desc']
}]
const TAG = "SmartFenceSetting";
//虚拟围栏
export default class SmartFenceSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      smartMonitorSwitch: this.props.navigation.getParam("switch") !== undefined ? this.props.navigation.getParam("switch") : false, // 虚拟围栏开关
      privateSwitch: false, // 隐私区域开关
      monitorAreaData: null, // 看护区域坐标数据 如 "[{\"area\":\"[0,0],[94.100]\"}]"
      privateAreaData: null, // 看护区域坐标数据 如 "[{\"area\":\"[0,0],[94.100]\"}]"
      sceneSelectedIndex: 0, // 当前选中看护模式的索引
      progressing: false,
      directionIndex: 0,
      items: [
        { title: LocalizedStrings['come_in_area'], subtitle: LocalizedStrings['come_in_area_desc'], isChecked: false, value: 1 },
        { title: LocalizedStrings['leave_area'], subtitle: LocalizedStrings['leave_area_desc'], isChecked: true, value: 2 }
      ],
      showAIDialog: false
    };

    // 矩形线框的左上角和右下键的x、y坐标轴
    this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    this.privateRectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];

    this.timeStamp = Date.now();

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        if (Platform.OS === "android") {
          BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
        }
      }
    );
    this.willBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        if (Platform.OS === "android") {
          BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
        }
      }
    );
  }
// android返回键处理
  onBackHandler = () => {
    this.goBackPage();
    return true;
  }

  goBackPage() {
    let callback = this.props.navigation.getParam('callback');
    if (callback) {
      callback(this.state.smartMonitorSwitch);
    }
    this.props.navigation.goBack();
  }
  componentDidMount() {
    this.setNavigationBar();
    this._getAllSpecData();
  }

  // 设置导航栏
  setNavigationBar() {
    this.props.navigation.setParams({
      title: LocalizedStrings['detect_fence'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            // if (this._isCanSave()) {
            //   this._updateSceneDurationData();
            // } else {
            //   this.props.navigation.goBack();
            // }
            // this.props.navigation.goBack();
            this.goBackPage();


          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  // 获取智能看护看关，划定区域的数据、设置的看护场景的时间段的Spec数据
  _getAllSpecData() {
    // this.setState({ progressing: true });
    let params = [
      { "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_SWITCH },
      { "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_AREA },
      { "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_SWITCH },
      { "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_PARAMS }
    ];
    AlarmUtilV2.getSpecPValue(params, 2, TAG).then((vo) => {
      let newStates = { progressing: false };
      let isFailed = false;
      if (vo[0]?.code == 0) {
        newStates.smartMonitorSwitch = vo[0].value;
      } else {
        isFailed = true;
      }

      if (vo[1]?.code == 0) {
        newStates.monitorAreaData = vo[1].value;
      } else {
        isFailed = true;
      }

      newStates.privateSwitch = vo[2]?.value;

      try {
        let areaData = JSON.parse(vo[3].value);
        newStates.styleType = areaData.style;
        newStates.privateAreaData = areaData.area;
      } catch (e) {
        // 隐私区域未正确写入默认值的情况
        newStates.styleType = 1;
        newStates.privateAreaData = { "pos": "[17, 17],[83, 83]" };
      }


      this.setState(newStates, () => {
        isFailed ? Toast.fail('c_get_fail') : null;
        if (this.state.monitorAreaData) {
          this._parseAreaDataSpecValue(JSON.parse(this.state.monitorAreaData)[0].pos);
        }
        if (this.state.privateAreaData) {
          this._parseAreaDataSpecValue(this.state.privateAreaData.pos, 1);
        }
      });
    }).catch(() => {
      Toast.fail('c_get_fail');
      this.setState({ progressing: false });
    });

  }

  // 解析Spec协议得出用户框定的线框的左上右下坐标值，并存入this.rectDatas刷新UI
  _parseAreaDataSpecValue(coordsArrayString, type = 0) { // "[{\"area\":\"[0,0],[94,100]\"}]",
    let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');// ["[0,0]","[94,100]"]
    let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
    coordsArray = [coordsArray[0] / 100.0 * viewWidth, coordsArray[1] / 100.0 * viewHeight, coordsArray[2] / 100.0 * viewWidth, coordsArray[3] / 100.0 * viewHeight];

    // 尝试修正设置spec时转为百分比带来的误差
    coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
    coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
    coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
    coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;
    type === 0 ? this.rectDatas = coordsArray : this.privateRectDatas = coordsArray;
  }


  render() {
    return (
      <View style={{ flex: 1, backgroundColor: Util.isDark() ? "#000000" : "#fff" }}>

        <ScrollView showVerticalScrollIndicator={false}>
          {this._renderHeaderImage()}
          <Text style={styles.desc_title}>{LocalizedStrings['algorithm_desc']}</Text>
          <Text style={styles.desc_subtitle}>{LocalizedStrings['ai_fence_desc']}</Text>
          <Text style={styles.desc_subtitle}>{LocalizedStrings['ai_fence_attention']}</Text>
          <View style={[styles.whiteblank, {marginTop: 38}]} />

          <ListItemWithSwitch
            title={LocalizedStrings.detect_fence}
            value={this.state.smartMonitorSwitch}
            showSeparator={false}
            titleStyle={{ fontWeight: 'bold' }}
            onValueChange={(value) => this._onSmartMonitorSwitchChange(value)}
          />
          {
            this.state.smartMonitorSwitch?
              <ListItem
                title={LocalizedStrings['fence_area']}
                showSeparator={false}
                titleStyle={{ fontWeight: 'bold' }}
                onPress={() => {
                  this.props.navigation.navigate("MonitorAreaModifyPageV2",
                    {
                      areaData: [...this.rectDatas],
                      privateAreaData: [...this.privateRectDatas],
                      privateSwitch: this.state.privateSwitch,
                      styleType: this.state.styleType,
                      callback: (areaDataNew) => this._refreshEffectiveMonitorArea([...areaDataNew])
                    });
                }} />:null
          }

        </ScrollView>
        <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings['c_setting']}
          onModalHide={() => this.setState({ progressing: false })} />

        {this._renderAIOpenDialog()}
      </View>
    );
  }

  _renderAIOpenDialog() {
    return (
      <MessageDialog
        visible={ this.state.showAIDialog }
        title={ LocalizedStrings['tips'] }
        message={ LocalizedStrings['object_open_msg'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: true,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0
        } }
        onDismiss={ () => {
          this.setState({ showAIDialog: false });
        } }
        buttons={ [{
          text: LocalizedStrings['action_cancle'],
          // colorType: 'grayLayerBlack',
          callback: () => {
            this.setState({ showAIDialog: false });
          }
        }, {
          text: LocalizedStrings['close'],
          // colorType: 'grayLayerBlack',
          callback: () => {
            this.changeAISwitch(true,true);
            this.setState({ showAIDialog: false });
          }
        }] }/>
    );
  }

  _renderHeaderImage() {
    return (<Image style={{ width: viewWidth, height: viewHeight, marginHorizontal: 24, marginTop: 13, marginBottom: 20, borderRadius: 9 }}
      source={require("../../Resources/Images/faceRecognition/ai_pic_fence.webp")} imageStyle={{ borderRadius: 10 }} />);
  }


  // 操作智能看护开关
  _onSmartMonitorSwitchChange(value) {

    if (value && VersionUtil.ipcVersionSupport() && this.state.privateSwitch) {
      // 触发画面算法与隐私区域互斥逻辑
      this.setState({ showAIDialog: true });
      return;
    }
    this.changeAISwitch(value);

  }

  changeAISwitch(value,closePrivate = false) {
    Toast.loading('c_setting');
    let params = [{ "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_SWITCH, value: value }];
    if (closePrivate) {
      params.push({ "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_SWITCH, value: false });
    }
    AlarmUtilV2.setSpecPValue(params).then((vo) => {
      if (vo[0].code == 0) {
        let privateValue = closePrivate ? false : this.state.privateSwitch;
        this.setState({ smartMonitorSwitch: value, privateSwitch: privateValue });
        // 埋点--虚拟围栏开关状态
        value ?
          TrackUtil .reportResultEvent("AIFeatures_VirtualFence_Status", "type", 1) :
          TrackUtil.reportResultEvent("AIFeatures_VirtualFence_Status", "type", 2);

        if (!value) {
          AlarmUtilV2.setAISettingEventClose(Event.FenceIn);
          // AlarmUtilV2.setAISettingEventClose(Event.FenceOut);
        }
      } else {
        this.setState({ smartMonitorSwitch: !value });
      }
      Toast.success('c_set_success');
    }).catch((err) => {
      this.setState({ smartMonitorSwitch: !value });
      Toast.fail("action_failed", err);
    });
  }

  // 通过用户划定的矩形线框的左上和右下角图标，计算出有效区域的矩形框左上和右下角坐标并刷新显示
  _refreshEffectiveMonitorArea(rectangleCoords) {
    this.rectDatas = rectangleCoords;
  }

  componentWillUnmount() {
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.willBlurListener && this.willBlurListener.remove();
    this.setState = () => false;
  }
}
