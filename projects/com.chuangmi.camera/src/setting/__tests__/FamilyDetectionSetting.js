import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity, Dimensions
} from 'react-native';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import { Device } from 'miot/device';
import { MessageDialog } from 'miot/ui/Dialog';
import Util from "../util2/Util";
import SpecUtil, { KEY_FAMILY_CARE_SIID, KEY_FAMILY_CARE_TIME_PERIOD_PIID } from "../util/SpecUtil";

const MAX_ITEM_NUM = 8;
const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;
const TAG = "FamilyDetectionSetting";

export default class FamilyDetectionSetting extends React.Component {
    static navigationOptions = (navigation) => {
      return {// 不要导航条
        headerTransparent: true,
        header:
                null
      };
    };

    constructor(props, context) {
      super(props, context);
      this.state = {
        isLoading: false,
        listDatas: [],
        alarmValues: {},
        showDelDialog: false,
        editMode: false,
        showBarTitle: false,
        isAllSelect: false
      };
      this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
        'didFocus',
        () => {
          this.onResume();
        }
      );
      this.isPageForeGround = true;
      this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
        'didBlur',
        () => {
          this.isPageForeGround = false;
        }
      );
      this.keys = [];
      this.LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks";
      this.selectCount = 0;
      this.deleteArray = [];
      this.deleteAlarmKeys = {};
      this.titleHeight = 38;
    }

    renderTitleBar() {
      let titleStr = this.state.editMode ? LocalizedStrings['edit'] : LocalizedStrings['family_detection'];
      let rightKey = (!this.state.listDatas || this.state.listDatas.length === 0) ?
        null : this.state.editMode ?
          this.state.isAllSelect ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL
          : NavigationBar.ICON.EDIT;
      let titleBarContent = {
        title: this.state.showBarTitle ? titleStr : " ",
        type: this.state.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
        left: [
          {
            key: this.state.editMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
            onPress: () => {
              if (this.state.editMode) {
                this.state.listDatas.forEach((item) => item.select = false);
                this.selectCount = 0;
                this.setState({ editMode: false, isAllSelect: false });
              } else {
                this.props.navigation.goBack();
              }
            }
          }
        ],
        right: [
          {
            key: rightKey,
            onPress: () => {
              if (this.state.editMode) {
                if (this.selectCount >= this.state.listDatas.length) {
                  // this.state.listDatas.forEach((item) => item.select = false);
                  this.selectCount = 0;
                  this.setState({
                    listDatas: this.state.listDatas.map((item, _index) => {
                      return { ...item, select: false };
                    }),
                    isAllSelect: false
                  });
                } else {
                  this.selectCount = this.state.listDatas.length;
                  this.setState({
                    listDatas: this.state.listDatas.map((item, _index) => {
                      return { ...item, select: true };
                    }),
                    isAllSelect: true
                  });
                }
              } else {
                this.selectCount = 0;
                this.setState({ editMode: true });
              }
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        }
      };
      return (
        <NavigationBar {...titleBarContent} />
      );
    }

    componentDidMount() {
      if (Platform.OS === "android") {
        BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
      }
      this.loadData();
    }

    onItemLongClick(item) {
      item.select = true;
      this.selectCount = 1;
      this.setState({ editMode: true, isAllSelect: this.state.listDatas.length === 1 });
    }

    renderItemView(item, index) {
      // {\"start\":\"07:00\",\"end\":\"09:00\",\"repeat\":127,\"enable\":false,\"clock_idx\":0,\"name\":\"早上无人出现\"}
      return (
        <View style={{ display: "flex", flexDirection: "column" }}>
          <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20 }}>
            <TouchableOpacity
              style={{ display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%" }}
              onLongPress={() => this.onItemLongClick(item)}
              onPress={() => {
                if (this.state.editMode) {
                  item.select = !item.select;
                  item.select ? this.selectCount++ : this.selectCount--;
                  this.setState({ isAllSelect: this.state.listDatas.length === this.selectCount });
                } else {
                  this.props.navigation.navigate('FamilyTimeSet',
                    {
                      item: JSON.parse(JSON.stringify(item)),
                      callback: (data) => {
                        Object.assign(item, data);
                        this.onItemCheckChanged();
                        this.forceUpdate();
                      }
                    });
                }
              }}>
              <Text style={{ fontSize: 16, color: '#000000', fontWeight: 'bold' }}>{item.name}</Text>
              <Text style={{
                fontSize: 13,
                color: 'rgba(0,0,0,0.6)',
                marginTop: 3
              }}>{this.getDurationText(item.start, item.end)} | {Util.getRepeatString(item.repeat)}</Text>

            </TouchableOpacity>
            {this.state.editMode ? <Checkbox
              style={{ width: 20, height: 20, borderRadius: 20 }}
              checked={item.select}
              onValueChange={(checked) => {
                item.select = checked;
                item.select ? this.selectCount++ : this.selectCount--;
                this.setState({ isAllSelect: this.state.listDatas.length === this.selectCount });
              }}
            /> : <Switch
              value={item.enable}
              disabled={false}
              onValueChange={(checked) => {
                item.enable = checked;
                this.onItemCheckChanged();
              }}
            />}
          </View>
        </View>
      );
    }

    getDurationText(start, end) {
      let text = `${ start }-${ end }`;
      let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
      let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
      if (startValue > endValue) {
        text = `${ start }-${ LocalizedStrings.setting_monitor_next_day }${ end }`;
      }
      return text;
    }

    scrollViewScroll = (event) => {
      const y = event.nativeEvent.contentOffset.y;
      // let flag = y > 28;
      let flag = y > this.titleHeight;
      if (this.showTitle == flag) {
        return;
      }
      if (flag) {
        this.showTitle = true;
        this.setState({ showBarTitle: true });
      } else {
        this.showTitle = false;
        this.setState({ showBarTitle: false });
      }
    };

    render() {
      let isDark = DarkMode.getColorScheme() == "dark";
      let titleStr = this.state.editMode ? LocalizedStrings['edit'] : LocalizedStrings['family_detection'];
      return (<View style={{
        display: "flex",
        height: "100%",
        width: "100%",
        flex: 1,
        flexDirection: "column",
        alignItems: "center"
      }}>
        {this.renderTitleBar()}
        <ScrollView scrollEventThrottle={16} onScroll={this.scrollViewScroll}
          style={{ width: "100%", backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' }}
          contentContainerStyle={{ flexGrow: 1 }}>


          <View style={{ backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' }}>

            <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0}
              onLayout={({ nativeEvent: { layout: { height } } }) => {
                this.titleHeight = height - 28;
              }}>
              <Text style={{
                fontSize: 30,
                color: "rgba(0, 0, 0, 0.80)",
                fontWeight: "300",
                position: "relative",
                marginLeft: 25,
                marginTop: 3,
                marginBottom: 23
              }}>
                {titleStr}
              </Text>
            </View>
            {
              !this.state.editMode ?
                <View style={{ alignItems: "center", marginHorizontal: 24, marginTop: 0 }}>
                  <Image style={{ width: "100%", height: viewHeight, borderRadius: 9 }}
                    source={require("../../Resources/Images/faceRecognition/ai_pic_area.webp")}/>
                </View> : null
            }

            {
              !this.state.editMode ?
                <Text
                  style={[styles.desc_subtitle, { marginTop: 20 }]}>{LocalizedStrings['family_detection_desc']}</Text> : null

            }

            {
              (!this.state.editMode && this.state.listDatas.length > 0) ?
                <View style={[styles.whiteblank, { marginTop: 38 }]}/> : null

            }

            <FlatList
              style={{ width: "100%", marginBottom: 100 }}
              data={this.state.listDatas}
              renderItem={(data) => this.renderItemView(data.item, data.index)}
              contentContainerStyle={[{ flexGrow: 1, paddingHorizontal: 12 }]}
              refreshing={this.state.isLoading}>

            </FlatList>
          </View>
        </ScrollView>
        {this.state.editMode ?
          <View style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: "100%",
            position: 'absolute',
            bottom: 15
          }}>
            <TouchableOpacity
              style={{
                flexDirection: "column",
                alignItems: "center"
              }}
              onPress={() => {
                this.deleteArray = [];
                this.deleteAlarmKeys = {};
                let needDelete = false;
                this.state.listDatas.forEach((item) => {
                  if (item.select == false) {
                    if (item.name) {
                      this.deleteAlarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ item.clock_idx }`] = item.name;
                    }
                    this.deleteArray.push({
                      enable: item.enable,
                      clock_idx: item.clock_idx,
                      start: item.start,
                      end: item.end,
                      repeat: item.repeat,
                      name: item.name
                    });
                  } else {
                    needDelete = true;
                  }
                });
                if (needDelete == false) {
                  console.log(`onDeleteItem no item to delete`);
                  Toast.success('idm_empty_tv_device_tips');
                  return;
                }
                this.setState({ showDelDialog: true });
              }}>
              <Image
                style={{ width: 25, height: 25 }}
                source={Util.isDark() ? require("../../Resources/Images/icon_delete_white.png") : require("../../Resources/Images/icon_delete_normal1.png")}
                tintColor={isDark ? IMG_DARKMODE_TINT : null}
              />
              <Text style={{ color: "#000000", fontSize: 11 }}>
                {LocalizedStrings["delete_files"]}
              </Text>
            </TouchableOpacity>

          </View>
          :
          <TouchableOpacity
            style={{ position: 'absolute', right: 0, bottom: 0 }}
            disabled={this.state.listDatas.length >= MAX_ITEM_NUM}
            onPress={() => {
              this.onAddItem();
            }}>
            <Image style={{ width: 120, height: 120 }}
              source={this.state.listDatas.length >= MAX_ITEM_NUM ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp")}/>
          </TouchableOpacity>
        }
        {this.renderDeleteDialog()}
      </View>);
    }

    renderDeleteDialog() {
      return (
        <MessageDialog
          visible={this.state.showDelDialog}
          title={LocalizedStrings['plug_timer_del']}
          message={LocalizedStrings['plug_timer_del_hint']}
          messageStyle={{ textAlign: 'center' }}
          canDismiss={true}
          onDismiss={() => this.setState({ showDelDialog: false })}
          buttons={[
            {
              text: LocalizedStrings["btn_cancel"],
              // style: { color: 'lightpink' },
              callback: (_) => {
                this.setState({ showDelDialog: false });
              }
            },
            {
              text: LocalizedStrings["delete_files"],
              // style: { color: 'lightblue' },
              callback: (_) => {
                this.onDeleteItem();
                this.setState({ showDelDialog: false });
              }
            }
          ]}
        />
      );
    }

    onDeleteItem() {
      // let arrayStr = JSON.stringify({ values: array });
      let arrayStr = JSON.stringify({ idx: 2, clock: this.deleteArray });
      console.log(`onDeleteItem ${ arrayStr }`);
      this.putLongTimeAlarmList(arrayStr, this.deleteAlarmKeys).then(() => {
        this.setState({ editMode: false });
        this.selectCount = 0;
        this.loadData();
      }).catch((err) => {
        Toast.fail("c_set_fail");
      });
    }

    // {"idx":2,"clock":[{"start":"08:00","end":"09:00","repeat":127,"enable":true,"clock_idx":0}, {"start":"11:00","end":"13:00","repeat":127,"enable":true,"clock_idx":1}]}
    onAddItem() {
      this.props.navigation.navigate('FamilyTimeSet', {
        existList: this.state.listDatas,
        callback: (data) => {
          console.log(`alarmDataResultListener=${ JSON.stringify(data) }`);
          let array = [];
          let alarmKeys = {};
          let key = 0;
          this.state.listDatas.forEach((item, index) => {
            if (key == this.keys[index]) {
              key += 1;
            }
            if (item.name) {
              alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ item.clock_idx }`] = item.name;
            }
            array.push({
              enable: item.enable,
              clock_idx: item.clock_idx,
              start: item.start,
              end: item.end,
              repeat: item.repeat,
              name: item.name
            });
          });

          if (data.name) {
            alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ key }`] = data.name;
          }
          array.push({
            enable: true,
            clock_idx: key,
            start: data.start,
            end: data.end,
            repeat: data.repeat,
            name: data.name
          });
          let arrayStr = JSON.stringify({ idx: 2, clock: array });
          console.log(`onAddItem ${ arrayStr }`);

          this.putLongTimeAlarmList(arrayStr, alarmKeys).then((res) => {
            Toast.success("c_set_success");
            this.loadData();
          }).catch((err) => {
            Toast.fail("c_set_fail");
          });
        }
      });
    }

    onResume() {

    }

    onItemCheckChanged() {
      let array = [];
      let alarmKeys = {};
      this.state.listDatas.forEach((item) => {
        if (item.name) {
          alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ item.clock_idx }`] = item.name;
        }
        array.push({
          enable: item.enable,
          clock_idx: item.clock_idx,
          start: item.start,
          end: item.end,
          repeat: item.repeat,
          name: item.name
        });
      });
      // let data = JSON.stringify({ values: array });
      let data = JSON.stringify({ idx: 2, clock: array });
      console.log(`onItemCheckChanged ${ data }`);
      this.putLongTimeAlarmList(data, alarmKeys).then((res) => {
      }).catch((err) => {
        Toast.fail("c_set_fail");
      });
    }

    setAlarmKey(alarmKeys) {
      let alarmKeysStr = JSON.stringify(alarmKeys);
      console.log(alarmKeysStr);
      if (alarmKeysStr == "{}") {
        console.log("alarmKeys is {}");
        return;
      }
      let data = { did: Device.deviceID, props: alarmKeys };
      AlarmUtil.setProps(data).then((res) => {
        console.log(JSON.stringify(res));
        // this.loadData();
      }).catch((err) => {
        console.log(JSON.stringify(err));
      });
    }

    putLongTimeAlarmList(data, alarmKeys) {
      return new Promise((resolve, reject) => {
        let params = [{ sname: KEY_FAMILY_CARE_SIID, pname: KEY_FAMILY_CARE_TIME_PERIOD_PIID, value: data }];
        SpecUtil.setSpecPValue(params, TAG).then((res) => {
          if (res[0].code == 0) {
            this.setAlarmKey(alarmKeys);
            console.log(`putLongTimeAlarmList${ JSON.stringify(res) }`);
            resolve(data);
          } else {
            reject("");
          }
        }).catch((err) => {
          console.log(`putLongTimeAlarmList err=${ JSON.stringify(err) }`);
          this.loadData();
          reject(err);
        });
      });
    }


    loadData() {
      this.setState({
        isLoading: true
      });

      let params = [{ sname: KEY_FAMILY_CARE_SIID, pname: KEY_FAMILY_CARE_TIME_PERIOD_PIID }];
      SpecUtil.getSpecPValue(params, 2, "TAG").then((result) => {
        console.log("[[[[[[[[[", typeof (result), typeof (result.value), result);
        if (result[0].code === 0) {
          let value = result[0].value;
          if (typeof (value) != "undefined" && value != "") {
            let valueObj = JSON.parse(value);
            let values = valueObj.clock;
            this.keys = [];
            values.forEach((item) => {
              item.select = false;
              this.keys.push(item.clock_idx);
            });
            this.keys.sort((a1, a2) => {
              return a1 - a2;
            });
            console.log(this.keys);
            let propsArray = [];
            for (let i = 0; i < 9; i++) {
              propsArray.push(`${ this.LONG_TIME_KEY_PREFIX }${ i }`);
            }
            console.log(`============props`, propsArray);
            this.setState({
              isLoading: false,
              listDatas: values
            });
            AlarmUtil.batchGetDatas([{ did: Device.deviceID, props: propsArray }]).then((res) => {
              console.log(`============${ JSON.stringify(res) }`);

            }).catch((err) => {
              console.log(err);
            });
          } else {
            this.productData();
          }

        } else {
          this.setState({
            isLoading: false
          });
          Toast.fail("c_get_fail");
        }
      }).catch((err) => {
        this.setState({
          isLoading: false
        });
        Toast.fail("c_get_fail");
      });

    }

    productData() {
      let data = JSON.stringify({
        clock: [
          {
            enable: false,
            clock_idx: 0,
            start: "07:00",
            end: "09:00",
            repeat: 0b01111111,
            name: LocalizedStrings['detect_nobody_in_morning']
          },
          {
            enable: false,
            clock_idx: 1,
            start: "11:00",
            end: "13:00",
            repeat: 0b01111111,
            name: LocalizedStrings['nobody_have_lunch']
          },
          {
            enable: false,
            clock_idx: 2,
            start: "06:00",
            end: "20:00",
            repeat: 0b01111111,
            name: LocalizedStrings['detect_nobody_in_day']
          }
        ]
      });
      let alarmKeys = {
        "prop.s_chuangmi_clocks0": LocalizedStrings['detect_nobody_in_morning'],
        "prop.s_chuangmi_clocks1": LocalizedStrings['nobody_have_lunch'],
        "prop.s_chuangmi_clocks2": LocalizedStrings['detect_nobody_in_day']
      };
      this.putLongTimeAlarmList(data, alarmKeys).then((res) => {
        this.loadData();
      });
    }

    componentWillUnmount() {
      if (Platform.OS === "android") {
        BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
      }
    }

    onBackHandler = () => {
      if (this.state.editMode) {
        this.state.listDatas.forEach((item) => item.select = false);
        this.setState({ editMode: false });
        this.selectCount = 0;
        return true;
      } else {
        return false;
      }
    };
}

const styles = StyleSheet.create({
  singleChoiceItemContainer: {
    borderRadius: 10,
    marginHorizontal: 22
  },
  desc_title: {
    textAlign: 'left',
    color: "#000000",
    fontSize: 18,
    fontFamily: "MI Lan Pro",
    fontWeight: "bold",
    marginHorizontal: 28
  },
  desc_subtitle: {
    textAlign: 'left',
    color: "rgba(0, 0, 0, 0.6)",
    fontSize: 14,
    marginTop: 10,
    fontFamily: "MI Lan Pro",
    lineHeight: 21,
    marginHorizontal: 28
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  }
});
