import React from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  Dimensions,
  ART,
  PanResponder,
  Platform,
  BackHandler,
  PixelRatio, StyleSheet, TouchableOpacity
} from 'react-native';

let {
  Surface,
  Shape,
  Path
} = ART;

import { Device, Service, Host } from 'miot';
import { MessageDialog, NavigationBar } from 'mhui-rn';
import { LoadingDialog } from 'miot/ui/Dialog';
import Toast from '../components/Toast';
import Util from "../util2/Util";
import VersionUtil from "../util/VersionUtil";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlbumHelper from "../util/AlbumHelper";
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import AbstractDialog from "miot/ui/Dialog/AbstractDialog";
import AlarmUtil from "../util/AlarmUtil";
import AlarmUtilV2, {
  PIID_FENCE_AREA,
  PIID_FENCE_SWITCH, PIID_PRIVATE_AREA_HIDE, PIID_PRIVATE_AREA_PARAMS,
  PIID_PRIVATE_AREA_SWITCH,
  SIID_AI_CUSTOM,
  SIID_FENCE_DETECTION
} from "../util/AlarmUtilV2";

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 0; // 矩形线框的到画面边缘的外边距
const CIRCLE_RADIUS = 3; // 矩形线框这个角上实心圆的半径
const DEVIATION_VALUE = 20; // 线框宽度、圆半径、除法取整引起的误差，试用
const VALID_TOUCH_RANGE = 15; // 四个边角的有效触摸范围
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");
const options = [
  {
    name: '可爱1',
    source: require("../../Resources/Images/area_style_lovely2.webp"),
    backgroundColor: 'red',
    type: 3,
    bgSource: require('../../Resources/Images/area_style_bg_four.webp'),
  },
  {
    name: '可爱2',
    source: require("../../Resources/Images/area_style_lovely1.webp"),
    type: 2,
     backgroundColor: 'blue',
    bgSource: require('../../Resources/Images/area_style_bg_three.webp'),
  },
  {
    name: '可爱3',
    source: require("../../Resources/Images/area_style_pure1.webp"),
    type: 0,
    backgroundColor: 'yellow',
    bgSource: require('../../Resources/Images/area_style_bg_one.webp'),
  },
  {
    name: '可爱4',
    source: require("../../Resources/Images/area_style_pure2.webp"),
    type: 1,
     backgroundColor: 'green',
    bgSource: require('../../Resources/Images/area_style_bg_two.webp'),
  },
];
const TAG = "MonitorPrivateAreaModifyPage";
export default class MonitorPrivateAreaModifyPage extends React.Component {
  static navigationOptions = (navigation) => {
    return { headerTransparent: true, header: null };
  };

  constructor(props, context) {
    super(props, context);
    let type = this.props.navigation.getParam('areaType');
    let index = options.findIndex((item) => item.type == type);
    this.state = {
      progressing: false,
      showCover: true,
      showBg: true,
      styleType: type,
      styleTempType: type,
      showStyleDialog: false,
      canSave: false,
      showSaveDialog: false,
      selectName: options[index].name,
      bgSource: options[index].bgSource,
      hidePrivateAreaSwitch: false
    };
    this.isMoving = false;
    this.curTempItem = options[index];
    this.timeStamp = Date.now();
    // 用户划定线框区域的左上和右下角坐标
    this.rectDatas = this.props.navigation.getParam('areaData');
    // 围栏所选中的区域框
    this.fenceDatas = this.props.navigation.getParam('fenceData') ? this.props.navigation.getParam('fenceData') : [22, 30, 150, 120];
    this.fenceSwitch = this.props.navigation.getParam('fenceSwitch') ? this.props.navigation.getParam('fenceSwitch') : false;
    // 有效看护区域矩形背景框的左上和右下角坐标
    this.rectBackGround = this.rectBackGround = [Math.floor(this.rectDatas[0] / itemWidth) * itemWidth, Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
      Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth, Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight];
    this.distanceData = [0, 0, 0, 0];
    this.touchPosition = 0; // 开始拖拽的点的坐标位或得出的值
    this.existsSettingsImg = false;
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        this.setState({ showCover: false, showBg: false });
        let x = evt.nativeEvent.locationX;// 开始触摸点相对于父View的横坐标
        let y = evt.nativeEvent.locationY; // 开始触摸点相对于父View的纵坐标
        this.touchBeginCoordX = x;
        this.touchBeginCoordY = y;

        this.distanceData = [0, 0, 0, 0];
        let smallest = [VALID_TOUCH_RANGE, VALID_TOUCH_RANGE]; // 矩形框四个角的有效触摸范围，x轴和y轴方向均在VALID_TOUCH_RANGE以内
        let positionCoord = [0, 0]; // 用户开始触摸点的x和y轴坐标

        // 触摸点在线框左上角，则设定触摸坐标为[8,4]
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        if (this.distanceData[0] < smallest[0]) { // 触摸点在线框左上角坐标的x轴方向的有效范围内
          positionCoord[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        if (this.distanceData[1] < smallest[1]) { // 触摸点在线框左上角坐标y轴方向的有效范围内
          positionCoord[1] = 4;
        }

        // 触摸点在线框右下角，则设定触摸坐标为[2,1]
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        if (this.distanceData[2] < smallest[0]) { // 触摸点在线框右下角坐标的x轴方向的有效范围内
          positionCoord[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        if (this.distanceData[3] < smallest[1]) { // 触摸点在线框右下角坐标y轴方向的有效范围内
          positionCoord[1] = 1;
        }
        this.touchPosition = positionCoord[0] | positionCoord[1]; // 通过位或运算得出共有12，3，6，9四个值
      },

      onPanResponderMove: (evt, gestureState) => {
        /* let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY; */
        if (Math.abs(gestureState.dx) <= 5 && Math.abs(gestureState.dy) <= 5) {
          console.log("---------------------------没滑动距离")
          return;
        }
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy;// dy 从触摸操作开始时的累计纵向位移
        console.log("===========is moving",this.touchBeginCoordX,gestureState.dx,this.touchBeginCoordY,gestureState.dy)
        let pointChange = false;
        switch (this.touchPosition) {
          case 12: { // 拖动左上角 触摸点[8,4]

            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y < 0 ? 0 : y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: { // 拖动右上角 触摸点[2,4]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 3: { // 拖动右下角 触摸点[2,1]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: { // 拖动左下角 触摸点[8,1]
            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        if (pointChange) {
          this.setState({ canSave: true });
        }

      },

      onPanResponderRelease: () => {
        console.log("onPanResponderRelease");
        this.cropImage();
      },

      onPanResponderTerminate: () => {
        console.log("onPanResponderTerminate");
      }
    });

  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    setTimeout(() => {
      Host.file.isFileExists(VersionUtil.settingsImgPathV2).then((success) => {
        console.log("isFileExists===", success);
        this.existsSettingsImg = success;
        this.cropImage();
        this.forceUpdate();
      }).catch((err) => {
        console.log("err=", JSON.stringify(err));
      });

    }, 100);
  }

  async cropImage() {
    let imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }`;
    if (!this.existsSettingsImg) {
      imageSource = require("../../Resources/Images/ai2_monitor_img.webp");
    } else {
      if (Platform.OS !== "ios") {
        imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }?timestamp=${ this.timeStamp }`;
      }
    }
    if (!this.existsSettingsImg) {
      // 未获取到截图，就不要去截图了
      this.setState({ showBg: true, showCover: false });
      return;
    }
    try {
      let source = `file://${ imageSource }`;
      if (Platform.OS === "ios") {
        source = imageSource;
      }
      console.log("image source", source);

      Image.getSize(source, (width, height) => {
        console.log("pic info", width, height);
        // 分辨率差2倍
        // if (Platform.OS !== "ios" && height > 480) {
        //   width = 2 * width;
        //   height = 2 * height;
        // }
        // 864  480
        let targetName = `crop/crop_area_${ Date.now() }.jpg`;
        // ios中同名targetName,无法正常显示图片，Android可正常显示
        // let targetName = `crop_area_${Date.now()}.jpg`
        let cropWidth = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
        let cropHeight = Math.abs(this.rectDatas[3] - this.rectDatas[1]);

        let pxWidth = parseInt(width * cropWidth / viewWidth);
        let pxHeight = parseInt(width * cropHeight / viewWidth);
        let leftX = parseInt(width * this.rectDatas[0] / viewWidth);
        let leftY = parseInt(height * this.rectDatas[1] / viewHeight);

        let params = {
          offset: { x: leftX, y: leftY },
          size: { width: pxWidth, height: pxHeight },
          displaySize: { width: pxWidth, height: pxHeight }
        };

        console.log("crop params", params, viewWidth, viewHeight, pxWidth, pxHeight, leftX, leftY);
        console.log("crop settingsImgPathV2", VersionUtil.settingsImgPathV2);
        Host.file.cropImage(targetName, VersionUtil.settingsImgPathV2, params).then((res) => {
          console.log("is success", res);
          let filepath = `${ Host.file.storageBasePath }/${ targetName }`;
          if (Platform.OS !== "ios") {
            // filepath = `${ filepath }?v=${ Date.now() }`;
          }
          let uri = { uri: filepath };
          this.setState({ showCover: true, coverImageUri: uri }, () => {
            setTimeout(() => {
              this.setState({ showBg: true });
            }, 200);
          });
          // this.forceUpdate()
        }).catch((error) => {
          this.setState({ showBg: true });
          console.log("is error", error);
        });
      }, (failure) => {
        this.setState({ showBg: true });
        console.log('failure', failure);
      });
    } catch (e) {
      this.setState({ showBg: true });
    }
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings["area_privacy_edit"],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({ showSaveDialog: true });
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: this.state.canSave ? [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            this.onSubmit();
          }
        }
      ] : [],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  // 点击保存看护区域坐标数据
  onSubmit() {
    this.setState({ progressing: true });
    // 设置需要带入三种参数 坐标、样式、是否隐藏隐私区域
    let positions = [Math.ceil(this.rectDatas[0] / viewWidth * 100), Math.ceil(this.rectDatas[1] / viewHeight * 100),
      Math.ceil(this.rectDatas[2] / viewWidth * 100), Math.ceil(this.rectDatas[3] / viewHeight * 100)];
    // 有array json => object json
    let valueString = JSON.stringify({ pos: `[${ positions[0] }, ${ positions[1] }],[${ positions[2] }, ${ positions[3] }]` });
    let areaValue = { pos: `[${ positions[0] }, ${ positions[1] }],[${ positions[2] }, ${ positions[3] }]` };
    console.log("onSubmit params=", valueString);
    let valueParams = { area: areaValue, style: this.state.styleType + 1, display: false };
    console.log("隐私区域参数:",JSON.stringify(valueParams));
    let params = [{ "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_PARAMS, value: JSON.stringify(valueParams) }];
    AlarmUtilV2.setSpecPValue(params, TAG).then((vo) => {
      this.setState({ progressing: false });
      if (vo[0].code == 0) {
        this.props.navigation.getParam('callback')(this.rectDatas, this.state.styleType, this.state.hidePrivateAreaSwitch); // 回调当前用户选定的线框坐标值

        Toast.success("c_set_success");
        this.props.navigation.goBack();
      } else {
        Toast.fail("action_failed");
      }
    }).catch((err) => {
      this.setState({ progressing: false });
      Toast.fail("action_failed", err);
    });
  }

  render() {
    let imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }`;

    if (!this.existsSettingsImg) {
      imageSource = require("../../Resources/Images/ai2_monitor_img.webp");
    } else {
      if (Platform.OS !== "ios") {
        imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPathV2 }?timestamp=${ this.timeStamp }`;
      }
      imageSource = { uri: imageSource };
    }

    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    // 表示有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();

    let fence_path = Path()
      .moveTo(this.fenceDatas[0], this.fenceDatas[1])
      .lineTo(this.fenceDatas[2], this.fenceDatas[1])
      .lineTo(this.fenceDatas[2], this.fenceDatas[3])
      .lineTo(this.fenceDatas[0], this.fenceDatas[3])
      .close();

    let background_path_all = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_one = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_two = Path()
      .moveTo(this.rectDatas[0], 0)
      .lineTo(viewWidth, 0)
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(this.rectDatas[0], this.rectDatas[1])
      .close();

    let background_path_three = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(viewWidth, viewHeight)
      .lineTo(this.rectDatas[0], viewHeight)
      .close();

    let background_path_four = Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .close();

    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2]+ CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2]+ CIRCLE_RADIUS, this.rectDatas[3] + 2*CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0]+ CIRCLE_RADIUS, this.rectDatas[3] + 2*CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let cropWidth = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
    let cropHeight = Math.abs(this.rectDatas[3] - this.rectDatas[1]);

    // TODO 需要替换ImageBackground图片
    return (<View style={{
      display: "flex",
      height: "100%",
      width: "100%",
      flex: 1,
      flexDirection: "column",
      backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF"
    }}>
      {this.renderTitleBar()}
      <Text style={{
        fontSize: 12,
        color: "#666666",
        marginLeft: 27,
        marginTop: 28
      }}>{LocalizedStrings.setting_monitor_area_paint}</Text>
      <View {...this.panResponder.panHandlers}>
        <ImageBackground style={{
          width: viewWidth,
          height: viewHeight,
          marginHorizontal: 24,
          marginTop: 13,
          marginBottom: 20
        }} imageStyle={{ borderRadius: 0 }}>
          {/* 展示截图或者无截图时的预制图片 */}
          {
            !this.state.showBg ? <Image style={{
              width: viewWidth,
              height: viewHeight
            }} source={imageSource} key={this.timeStamp} /> : null
          }
          {/* 展示背景图 */}
          {
            this.state.showBg ? <Image style={{
              width: viewWidth,
              height: viewHeight
            }} resizeMode={'cover'} source={this.state.bgSource} key={this.timeStamp}
            /> : null
          }

          {this.state.showCover ?
            <View style={{
              position: 'absolute',
              width: cropWidth,
              height: cropHeight,
              left: this.rectDatas[0],
              top: this.rectDatas[1]
            }}>
              <Image
                source={this.state.coverImageUri}
                style={{
                  width: cropWidth,
                  height: cropHeight,
                  resizeMode: 'contain'
                }}
                onError={(error) => {
                  console.log("image load error", error);
                }}
              />
            </View>
            : null
          }

          <View style={{ position: 'absolute' }}>
            <Surface width={viewWidth} height={viewHeight}>
              {/* <Shape d={background_path} fill="#32BAC0" opacity="0.3" /> */}
              {!this.state.showBg ? <Shape d={background_path_one} fill="#000000" opacity="0.5" /> : null}
              {!this.state.showBg ? <Shape d={background_path_two} fill="#000000" opacity="0.5" /> : null}
              {!this.state.showBg ? <Shape d={background_path_three} fill="#000000" opacity="0.5" /> : null}
              {!this.state.showBg ? <Shape d={background_path_four} fill="#000000" opacity="0.5" /> : null}
              <Shape d={draggable_rectangle_path} fill="#32BAC0" opacity="0" />
              <Shape d={draggable_rectangle_path} stroke="#32BAC0" strokeWidth={1} />
              {this.fenceSwitch ? <Shape d={fence_path} stroke="#32BAC0" strokeDash={[15, 10]} strokeWidth={1} /> : null}

            </Surface>

          </View>

        </ImageBackground>
        <View style={{
          width: viewWidth+6,
          height: viewHeight+6,
          position: 'absolute',
          marginHorizontal: 21,
          marginTop: 10,
          marginBottom: 20
        }} imageStyle={{ borderRadius: 0 }}>

          <Surface width={ viewWidth + 6 } height={ viewHeight + 6 }>
            <Shape d={ top_left_circle } fill="#32BAC0"/>
            <Shape d={ top_right_circle } fill="#32BAC0"/>
            <Shape d={ bottom_right_circle } fill="#32BAC0"/>
            <Shape d={ bottom_left_circle } fill="#32BAC0"/>

          </Surface>

        </View>
      </View>
      {/*<ListItemWithSwitch*/}
      {/*  title={LocalizedStrings['area_privacy_hide']}*/}
      {/*  value={this.state.hidePrivateAreaSwitch}*/}
      {/*  showSeparator={false}*/}
      {/*  titleStyle={{ fontWeight: 'bold' }}*/}
      {/*  onValueChange={(value) => this._onHideAreaChange(value)}*/}
      {/*/>*/}

      <ListItem
        title={LocalizedStrings['area_privacy_style']}
        containerStyle={{ marginBottom: 20 }}
        showSeparator={false}
        titleStyle={{ fontWeight: 'bold' }}
        value={this.state.selectName}
        onPress={() => {
          let index = options.findIndex((item) => item.type == this.state.styleType);
          this.curTempItem = options[index];
          this.setState({ showStyleDialog: true, styleTempType: this.state.styleType });
        }} />

      <LoadingDialog
        visible={this.state.progressing}
        message={LocalizedStrings['c_setting']}
        onModalHide={() => this.setState({ progressing: false })} />
      {this._renderStyleDialog()}
      {this._renderBackDialog()}
    </View>);
  }

  _renderStyleDialog() {
    return (
      <AbstractDialog
        style={[styles.areaStyle]}
        visible={this.state.showStyleDialog}
        title={LocalizedStrings['area_privacy_style']}
        showSubtitle={false}
        canDismiss={false}
        onDismiss={() => {
          // this._repeatDismiss();
        }}
        // showTitle={false}
        // showButton={false}
        useNewTheme={true}
        buttons={[{
          text: LocalizedStrings["btn_cancel"],
          callback: () => {
            this.setState({ showStyleDialog: false });
          }
        },
        {
          text: LocalizedStrings['btn_confirm'],
          callback: () => {
            this.setState({ canSave: true, showStyleDialog: false, styleType: this.curTempItem.type, bgSource: this.curTempItem.bgSource, selectName: this.curTempItem.name });
          }
        }
        ]}
      >
        {this._renderStyleInnerView()}
      </AbstractDialog>
    );
  }

  _renderStyleInnerView() {
    let useWidth = Host.isPad ? screenWidth * 0.75 : screenWidth;
    let width = (useWidth - 54)/2;
    return (
      <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginHorizontal: 22, marginBottom: 6 }}>
        {
          options.map((item, index) => {
            let textStyle = {
              paddingVertical: 10,
              color: this.state.styleTempType === item.type ? '#32BAC0' : '#CBCBCB'
            };
            return (
              <TouchableOpacity key={index} onPress={() => {
                this.chooseBg(item, index);
              }}>
                <View key={index} style={{ flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                  <View style={[{ width: width, height: width, alignItems: "center", justifyContent: "center" }, this.state.styleTempType === item.type ? { borderRadius: 16, borderColor: '#32BAC0', borderWidth: 2 } : { borderRadius: 16, borderColor: '#FFFFFF', borderWidth: 2 }, index % 2 == 0 ? { marginRight: 10 } : null]}>
                    <Image source={item.source} style={{ width: width-12, height: width-12, borderRadius: 16 }} />
                  </View>
                  <Text style={textStyle}>{item.name}</Text>
                </View>
              </TouchableOpacity>
            );
          })
        }
      </View>
    );
  }

  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={LocalizedStrings['exit_change_disappear']}
        messageStyle={{ textAlign: "center" }}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
            }
          },
          {
            text: LocalizedStrings["exit"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
              this.props.navigation.goBack();
            }
          }
        ]}
      />
    );
  }
  /**
   * 选择隐私区域样式
   */
  chooseBg(item, index) {
    this.curTempItem = item;
    this.setState({ styleTempType: item.type });
  }
  /**
   * 隐私区域隐藏开关
   * 更新2023年11月15日
   * 合并spec，这里不做值更改值保存临时值
   * @param value
   */
  _onHideAreaChange(value) {
    console.log("隐私区域开关", value);
    this.setState({ hidePrivateAreaSwitch: value, canSave: true });
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }

    AlbumHelper.deleteFolderFile("crop");
  }

  onBackHandler = () => {
    // if (this.state.canSave) {
    //   this.onSubmit();
    //   return true;
    // }
    if (this.state.canSave) {
      this.setState({ showSaveDialog: true });
      return true;
    }
    return false;
  };

}

const styles = StyleSheet.create({
  areaStyle: {
    width: screenWidth,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0
  }
});