'use strict';
import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  ART, Platform, BackHandler, PixelRatio
} from 'react-native';
let { Surface, Shape, Path } = ART;

import { Device, Service } from 'miot';
import Host from "miot/Host";
import NavigationBar from "miot/ui/NavigationBar";
import { LoadingDialog } from 'miot/ui/Dialog';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Toast from '../components/Toast';
import Util from "../util2/Util";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtilV2, {
  PIID_ASLEEP_WAKEUP_SWITCH,
  PIID_COUGH_SWITCH,
  PIID_CRY_SWITCH, PIID_EXPRESSION_SWITCH,
  PIID_FENCE_AREA,
  PIID_FENCE_SWITCH, PIID_MOUTH_NORSE_SWITCH, PIID_MOVE_SWITCH, PIID_PEOPLE_SWITCH,
  PIID_PRIVATE_AREA_HIDE, PIID_PRIVATE_AREA_PARAMS,
  PIID_PRIVATE_AREA_SWITCH, PIID_SOUND_SWITCH,
  SIID_AI_CUSTOM, SIID_AI_DETECTION, SIID_FENCE_DETECTION
} from "../util/AlarmUtilV2";
import TrackUtil from '../util/TrackUtil';
import { MessageDialog } from "mhui-rn";
import VersionUtil from "../util/VersionUtil";

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距
const CIRCLE_RADIUS = 3;
const TAG = "SmartPrivateSetting";
export default class SmartPrivateSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      smartMonitorSwitch: this.props.navigation.getParam("switch") !== undefined ? this.props.navigation.getParam("switch") : false, // 智能看护总开关
      monitorAreaData: null, // 看护区域坐标数据 如 "[{\"area\":\"[0,0],[94.100]\"}]"
      areaType: 3,
      fenceSwitch: false,
      peopleSwitch: false,
      faceSwitch: false,
      motionSwitch: false,
      mouthNorseSwitch: false,
      wakeupSwitch: false,
      expressionSwitch: false,
      fenceAreaData: null, // 看护区域坐标数据 如 "[{\"area\":\"[0,0],[94.100]\"}]"
      sceneSelectedIndex: 0, // 当前选中看护模式的索引
      progressing: false,
      showAIDialog: false,
    };
    // 矩形线框的左上角和右下键的x、y坐标轴
    this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    this.rectFenceDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
  }

  componentDidMount() {
    this.setNavigationBar();
    this._getAllSpecData();
  }

  // 设置导航栏
  setNavigationBar() {
    this.props.navigation.setParams({
      title: LocalizedStrings['area_privacy_protection'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  // 获取智能看护看关，划定区域的数据、设置的看护场景的时间段的Spec数据
  /**
   * @Author: byh
   * @Date: 2024/8/3
   * @explanation:
   * 隐私区域开启，其他画面检测会失效，人形检测、口鼻遮挡、表情识别、
   * 人脸识别（云端人脸）、睡眠检测、虚拟围栏、画面检测
   *
   *********************************************************/
  _getAllSpecData() {
    this.setState({ progressing: true });
    let params = [
      { "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_SWITCH },
      // { "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_POINTS },
      { "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_PARAMS },
      { "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_SWITCH },
      { "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_AREA }
    ];
    if (VersionUtil.ipcVersionSupport()) {
      let motionParams = [
        { "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH },
        { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
        { "sname": SIID_AI_CUSTOM, "pname": PIID_EXPRESSION_SWITCH },
        { "sname": SIID_AI_CUSTOM, "pname": PIID_MOUTH_NORSE_SWITCH },
        { "sname": SIID_AI_CUSTOM, "pname": PIID_ASLEEP_WAKEUP_SWITCH }
      ]
      params.push(...motionParams);
    }
    AlarmUtilV2.getSpecPValue(params, 2, TAG).then((vo) => {
      let newStates = { progressing: false };
      let isFailed = false;
      if (vo[0]?.code == 0) {
        newStates.smartMonitorSwitch = vo[0].value;
      } else {
        isFailed = true;
      }

      if (vo[1]?.code == 0) {
        let areaParams = JSON.parse(vo[1].value);
        newStates.monitorAreaData = areaParams.area;
        // 隐私区域样式 1 2 3 4
        newStates.areaType = areaParams.style - 1;
      } else {
        isFailed = true;
      }
      newStates.fenceSwitch = vo[2].value;
      newStates.fenceAreaData = vo[3].value;
      // 画面检测算法
      if (VersionUtil.ipcVersionSupport()) {
        newStates.peopleSwitch = vo[4].value;
        newStates.motionSwitch = vo[5].value;
        newStates.expressionSwitch = vo[6].value;
        newStates.mouthNorseSwitch = vo[7].value;
        newStates.wakeupSwitch = vo[8].value;
      }

      this.setState(newStates, () => {
        isFailed ? Toast.fail('c_get_fail') : null;
        if (this.state.monitorAreaData) {
          this._parseAreaDataSpecValue(this.state.monitorAreaData.pos);
        }
        if (this.state.fenceAreaData) {
          this._parseAreaDataSpecValue(JSON.parse(this.state.fenceAreaData)[0].pos, 1);
        }
      });
    }).catch(error => {
      Toast.fail('c_get_fail');
      this.setState({ progressing: false });
    });

  }
  // 解析Spec协议得出用户框定的线框的左上右下坐标值，并存入this.rectDatas刷新UI
  _parseAreaDataSpecValue(coordsArrayString, type = 0) { // "[{\"area\":\"[0,0],[94,100]\"}]",
    let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');// ["[0,0]","[94,100]"]
    let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
    coordsArray = [coordsArray[0] / 100.0 * viewWidth, coordsArray[1] / 100.0 * viewHeight,
      coordsArray[2] / 100.0 * viewWidth, coordsArray[3] / 100.0 * viewHeight];

    // 尝试修正设置spec时转为百分比带来的误差
    coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
    coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
    coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
    coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;
    type === 0 ? this.rectDatas = coordsArray : this.rectFenceDatas = coordsArray;
  }


  render() {
    return (
      <View style={{ flex: 1, backgroundColor: Util.isDark() ? "#000000" : "#fff" }}>

        <ScrollView showVerticalScrollIndicator={false}>
          {this._renderHeaderImage()}
          <Text style={styles.desc_title}>{LocalizedStrings['algorithm_desc']}</Text>
          <Text style={styles.desc_subtitle}>{LocalizedStrings['ai_key_area_desc']}</Text>

          <Text style={styles.desc_subtitle}>{LocalizedStrings['ai_key_area_attention']}</Text>
          <View style={[styles.whiteblank, {marginTop: 38}]} />

          <ListItemWithSwitch
            title={LocalizedStrings['area_privacy_protection']}
            value={this.state.smartMonitorSwitch}
            showSeparator={false}
            titleStyle={{ fontWeight: 'bold' }}
            onValueChange={(value) => this._onSmartMonitorSwitchChange(value)}
          />
          {
            this.state.smartMonitorSwitch?<ListItem
              title={LocalizedStrings.area_privacy}
              containerStyle={{ marginBottom: 20 }}
              showSeparator={false}
              titleStyle={{ fontWeight: 'bold' }}
              onPress={() => {
                this.props.navigation.navigate("MonitorPrivateAreaModifyPage",
                  {
                    areaData: [...this.rectDatas],
                    fenceData: [...this.rectFenceDatas],
                    fenceSwitch: this.state.fenceSwitch,
                    areaType: this.state.areaType,
                    callback: (areaDataNew,type,hideArea) => this._refreshEffectiveMonitorArea([...areaDataNew],type,hideArea)
                  });
              }} />:null
          }
          <View style={{ height: 40 }} />
        </ScrollView>

        <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings['c_setting']}
          onModalHide={() => this.setState({ progressing: false })} />
        {this._renderAIOpenDialog()}
      </View>
    );
  }

  _renderAIOpenDialog() {
    return (
      <MessageDialog
        visible={ this.state.showAIDialog }
        title={ LocalizedStrings['tips'] }
        message={ LocalizedStrings['private_open_msg'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: true,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0
        } }
        onDismiss={ () => {
          this.setState({ showAIDialog: false });
        } }
        buttons={ [{
          text: LocalizedStrings['offline_divice_ok'],
          colorType: 'grayLayerBlack',
          callback: () => {
            this.setState({ showAIDialog: false });
          }
        }] }/>
    );
  }

  /* 顶部介绍图 */
  _renderHeaderImage() {
    return (<Image style={{ width: viewWidth, height: viewHeight, marginHorizontal: 24, marginTop: 13, marginBottom: 20, borderRadius: 9 }}
      source={require("../../Resources/Images/faceRecognition/ai_pic_private.webp")} imageStyle={{ borderRadius: 10 }} />);
  }

  // 操作智能看护开关
  _onSmartMonitorSwitchChange(value) {

    // 其他AI画面检测有开启的情况

    if (value && VersionUtil.ipcVersionSupport()) {
      let showAIDialog = this.state.fenceSwitch ||  this.state.peopleSwitch
        || this.state.motionSwitch || this.state.fenceSwitch
        || this.state.mouthNorseSwitch || this.state.wakeupSwitch
        || this.state.expressionSwitch;
      if (showAIDialog) {
        this.setState({ showAIDialog: true });
        return;
      }
    }


    Toast.loading('c_setting');
    let params = [{ "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_SWITCH, value: value }];
    AlarmUtilV2.setSpecPValue(params, TAG).then((vo) => {
      if (vo[0].code == 0) {
        Toast.success('c_set_success');
        this.setState({ smartMonitorSwitch: value });
      } else {
        Toast.fail("action_failed");
        this.setState({ smartMonitorSwitch: !value });
      }
    }).catch((err) => {
      this.setState({ smartMonitorSwitch: !value });
      Toast.fail("action_failed", err);
    });
    // 埋点--区域隐私保护开关状态
    // console.log("区域隐私保护开关状态================", value);
    value
      ? TrackUtil.reportResultEvent("AIFeatures_PrivacyArea_Status", "type", 1)
      : TrackUtil.reportResultEvent("AIFeatures_PrivacyArea_Status", "type", 2);
  }

  // 通过用户划定的矩形线框的左上和右下角图标，计算出有效区域的矩形框左上和右下角坐标并刷新显示
  _refreshEffectiveMonitorArea(rectangleCoords,type) {
    this.rectDatas = rectangleCoords;
    this.setState({ areaType: type });
  }

  componentWillUnmount() {

  }
}

const styles = StyleSheet.create({
  singleChoiceItemContainer: {
    borderRadius: 10,
    marginHorizontal: 22
  },
  desc_title: {
    color: "#000000",
    fontSize: 18,
    fontWeight: "bold",
    paddingHorizontal: 24,
    fontFamily: "MI Lan Pro"
  },
  desc_subtitle: {
    color: "#979797",
    fontSize: 14,
    marginTop: 10,
    lineHeight: 21,
    fontFamily: "MI Lan Pro",
    paddingHorizontal: 24
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  }
});