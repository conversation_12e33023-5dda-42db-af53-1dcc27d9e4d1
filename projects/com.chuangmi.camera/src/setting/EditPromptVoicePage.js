import React from 'react';

import {
    StyleSheet,
    View,
    Image,
    Text,
    Platform,
    ScrollView,
    Modal,
    TouchableWithoutFeedback,
    Dimensions,
    Picker,
    StatusBar,
    TouchableOpacity,
    TextInput,
    KeyboardAvoidingView,
    FlatList,
    PermissionsAndroid,
    BackHandler,
    DeviceEventEmitter
} from 'react-native';


import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import ChoiceItem from "../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem";
import ListItemWithPopMenu from "../../../../imilab-design-ui/src/widgets/settingUI/ListItemWithPopMenu";
import RoundedButtonView from "../../../../imilab-design-ui/src/widgets/settingUI/RoundedButtonView"
import Separator from "../../../../imilab-design-ui/src/widgets/settingUI/Separator"
import SlideGear from "../../../../imilab-design-ui/src/widgets/settingUI/SlideGear"
import MessageDialog from "../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog"
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {XText, XView} from "react-native-easy-app";
import Toast from "react-native-root-toast";
import {LetDevice, letDevice} from "../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {CameraMethod, LetIMIIotRequest} from "../../../../imilab-rn-sdk";
import {imiThemeManager, showToast} from "../../../../imilab-design-ui";
import {showLoading} from "../../../../imilab-design-ui";
import {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import WaveView from '../../../../imilab-design-ui/src/widgets/WaveView'
import {isIphoneXSeries} from "../../../../imilab-rn-sdk/utils/Utils";
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import { AudioRecorder, AudioUtils } from 'react-native-audio';
import Sound from 'react-native-sound';
import IMIAudioUtils from "../../../../imilab-rn-sdk/native/local-kit/IMIAudioUtils";
import IMIFfmpegUtils from "../../../../imilab-rn-sdk/native/local-kit/IMIFfmpegUtils";
import IMIIotRequest from "../../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest"
import IMIDownload from "../../../../imilab-rn-sdk/native/local-kit/IMIDownload";
import {isAndroid} from "../../../../imilab-rn-sdk/utils/Utils";
import IMIHost from "../../../../imilab-rn-sdk/native/local-kit/IMIHost";
import IMIFile from "../../../../imilab-rn-sdk/native/local-kit/IMIFile";

const screen_width = Dimensions.get('window').width;
const screen_height = Dimensions.get('window').height;
const EVENT_NAME = "IMIDownloadAudioPathScheduler - ";

let maxLength = 11;
let audioPlayerUid = 'audioPlayerUid';
let fileName = 'test.alaw';

let isCheckingPermission = false;

const temp_down_audio_path = "temp_down_audio_path";
const voiceArr =[
    {//"无",
        replyName:stringsTo('voice_for_wu'),
        replyCNUrl:'null',//大陆
        replyUSUrl:'null',//美国
        replySGUrl:'null',//新加坡
        replyDEUrl:'null',//欧洲
        replRUUrl:'null',//俄罗斯
        replIDUrl:'null',//印度
        index: 1,
        time: '5'
    },
    {//"警报",
        replyName:stringsTo('voice_for_warning'),
        replyCNUrl:'1-alarm',//大陆
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//欧洲 德国
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//印度
        index: 2,
        time: '5'
    },
    {//"叮咚声",
        replyName:stringsTo('voice_for_dingdong'),
        replyCNUrl:'2-ding_dong',//大陆
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//印度
        index: 3,
        time: '5'
    },
    {//"您好，欢迎光临"
        replyName:stringsTo('voice_for_welcome'),
        replyCNUrl:'3-welcom_female',//大陆
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//印度
        index: 4,
        time: '5'
    },
    {//"您已进入监控区域"
        replyName:stringsTo('voice_for_area'),
        replyCNUrl:'4-entered_monitoring_male',//大陆
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//印度
        time: '5'
    },
    {//"请随手关门，谢谢"
        replyName:stringsTo('voice_for_closedoor'),
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//大陆
        replyCNUrl:'5-close_door_female',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//印度
        index: 6,
        time: '5'
    },
    {//"请注意安全"
        replyName:stringsTo('voice_for_safe'),
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//大陆
        replyCNUrl:'6-safety_female',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//印度
        index: 7,
        time: '5'
    },
    {//"上下楼梯,请注意安全"
        replyName:stringsTo('voice_for_stairs'),
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//大陆
        replyCNUrl:'9-stairs_safe_female',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//印度
        index: 8,
        time: '5'
    },
    {//"危险区域，请离开"
        replyName:stringsTo('voice_for_dangerArea'),
        // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//大陆
        replyCNUrl:'10-danger_area_male',//大陆
        replyUSUrl:'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//美国
        replySGUrl:'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//新加坡
        replyDEUrl:'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//欧洲
        replRUUrl:'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//俄罗斯
        replIDUrl:'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//印度
        index: 9,
        time: '5'
    },
];

let tempPlayPath = '';
let whoosh = new Sound(tempPlayPath, '',(error) => {
    if (error) {
        console.log('资源加载失败', error);
        return ;
    }
});

//TODO 适配和测试新云存下的音频文件的上传逻辑 ///////////////////////////////////////////////////////////
/**
 * 编辑提示音
 */
export  default  class  EditPromptVoicePage extends BaseDeviceComponent {
    static propTypes = {};

    static navigationOptions = ({ navigation }) => {
        return {
            headerTransparent: true,
            header:
                null
        };
    };

    constructor(props) {
        super(props);
        this.state = {
            PeopleSwitchPushValue:false,//人形推送开关
            isLoading:false,
            dataArray:[],//编辑获取当前数组
            nameDiaLogVisible:false,
            nameValue:stringsTo('voice_for_custom'),
            nameCancleTit:stringsTo('voice_for_custom'),
            iOSNameDiaLogVisible:false,
            showRecordAlert:false,// 显示录音弹窗
            isRecording: false,// 是否正在录音
            recordTime: 0,
            recordTimeValue:0,
            recordPlay: false,
            playingItemId:'',
            showVoiceTime:false,
            isVoiceAdd:this.props.route.params.isAdd,
            location:this.props.route.params.index,
            typeStr:this.props.route.params.type,
            updateData:this.props.route.params.updateData,// 更新提示音时的数据
            // audioPath: AudioUtils.DocumentDirectoryPath + `quick_audio_${new Date().getTime()}.aac`, // 文件路径
            audioPath: IMIFile.storageBasePath + `/quick_audio_${new Date().getTime()}.aac`, // 文件路径
            hasPermission: undefined, //录音 授权状态
            stop: false,     //录音是否停止
            tempAudioPath:'',//临时保存的音频路径
            tempShowVoiceTime:false,//临时保存重新录制取消
            tempAudioTime:0,//临时保存的音频时间
            isDownUrl:false,//判断是否下载文件
            downTempAudioPath:'',//获取下载的音频path
            paramArr:[],//当前自定义数组
            uploadOpusPath:'',//上传的opus路径
            editTempTime:0,//编辑提示音时获取的音频时间用于重新录制又取消时重新赋值
            isShowDeleteAlert:false,//显示删除弹窗
            isNotChangeAudio:true,//编辑状态下不修改音频
            isCanPress:true,//是否可以点击播放
        }
    }

    UNSAFE_componentWillMount() {
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
    }

    componentWillUnmount() {
        // 音频相关
        this.countdownTimer && clearTimeout(this.countdownTimer);
        this.editdownTimer && clearTimeout(this.editdownTimer);
        this.tempCancleTimer && clearInterval(this.tempCancleTimer);
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress',this.onBackHandler);
        }
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
        if (navigation.isFocused()) {
            if (this.state.showVoiceTime){
                this.handlePauseAudio();
            }
            this._onPressBack();
            return true;
        } else {
        }
        return false;
    };
    _onPressBack = () => {
        if (this.props.route.params.callback) {
            this.props.route.params.callback('1');
        }
        this.props.navigation.pop();
    }

    componentDidMount() {
        this.getAllValue();
        this.getAudioAuthorize()
    }

    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.updateAllPropertyCloud().then((data) => {
            showLoading(false);
            // console.log('获取声光报警--',JSON.parse(data));
            let dataObject = JSON.parse(data);
            let stateProps = {};
            // alert(dataObject);
            if (this.state.typeStr == 'lightVoice') {
                if (dataObject.SoundLightCustomAudioList){
                    if (dataObject.SoundLightCustomAudioList.value.length > 0){
                        // 有声光报警自定义音频数据
                        let oneKeyArr = dataObject.SoundLightCustomAudioList.value;
                        stateProps.paramArr = oneKeyArr;
                        console.log('hhhh--',this.state.paramArr);
                        console.log('当前自定义音频数组',oneKeyArr,oneKeyArr.length);
                        if (this.state.isVoiceAdd == false){
                            console.log('编辑');
                            // 编辑
                            for (let i = 0; i < oneKeyArr.length; i++) {
                                let tempStr = JSON.parse(oneKeyArr[i]);
                                if (this.state.location == tempStr.index){
                                    stateProps.nameValue = tempStr.audio_name;
                                    stateProps.nameCancleTit = tempStr.audio_name;
                                    stateProps.recordTimeValue = tempStr.audio_time;
                                    stateProps.recordTime = tempStr.audio_time;
                                    stateProps.editTempTime = tempStr.audio_time;
                                    stateProps.downTempAudioPath = tempStr.audio_path;
                                    stateProps.uploadOpusPath = tempStr.audio_opus_path;
                                    stateProps.showVoiceTime = true;
                                }
                            }
                        }
                    }else {
                        // 无声光报警自定义音频数据
                        stateProps.paramArr = [];
                    }
                }else {
                    // 无声光报警自定义音频数据
                    stateProps.paramArr = [];
                }


            }else if (this.state.typeStr == 'onekeyVoice'){
                if (dataObject.OneKeyCustomAudioList){
                    if (dataObject.OneKeyCustomAudioList.value.length > 0){
                        // 有一键警告自定义音频数据
                        console.log('自定义音频有数据');
                        let oneKeyArr = dataObject.OneKeyCustomAudioList.value;
                        stateProps.paramArr = oneKeyArr;
                        console.log('hhhh--',this.state.paramArr);
                        console.log('当前自定义音频数组',oneKeyArr,oneKeyArr.length);
                        if (this.state.isVoiceAdd == false){
                            console.log('编辑');
                            // 编辑
                            for (let i = 0; i < oneKeyArr.length; i++) {
                                let tempStr = JSON.parse(oneKeyArr[i]);
                                if (this.state.location == tempStr.index){
                                    stateProps.nameValue = tempStr.audio_name;
                                    stateProps.nameCancleTit = tempStr.audio_name;
                                    stateProps.recordTimeValue = tempStr.audio_time;
                                    stateProps.recordTime = tempStr.audio_time;
                                    stateProps.editTempTime = tempStr.audio_time;
                                    stateProps.downTempAudioPath = tempStr.audio_path;
                                    stateProps.uploadOpusPath = tempStr.audio_opus_path;
                                    stateProps.showVoiceTime = true;
                                }
                            }
                        }
                    }else {
                        // 无一键警告自定义音频数据
                        stateProps.paramArr = [];
                    }
                }else {
                    // 无一键警告自定义音频数据
                    stateProps.paramArr = [];
                }

            }
            // 统一设置从设备端获取的值
            this.setState(stateProps);
            console.log('statprops---',stateProps);
        }).catch(error => {
            console.log('错误哈哈哈--',JSON.stringify(error));
            showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
            console.log(JSON.stringify(error))
        });
    }

    getAudioAuthorize() {
        AudioRecorder.requestAuthorization()
            .then(isAuthor => {
                console.log('是否授权: ' + isAuthor);
                // showToast('是否授权: ' + isAuthor);

                this.setState({hasPermission: isAuthor});
                if(!isAuthor) {
                    // return alert('APP需要使用录音，请打开录音权限允许APP使用')
                    // console.log('APP需要使用录音，请打开录音权限允许APP使用')
                    // showToast('APP需要使用录音，请打开录音权限允许APP使用');
                    showToast(I18n.t('audio_permission_denied'));
                    return ;
                }
                //强制从speaker输出
                // Sound.setCategory("Playback",false);
                // Sound.setActive(true);
                // Sound.setCategory("PlayAndRecord",false);
                // 需要使用扬声器和听筒切换场景，用
                // AVAudioSessionCategoryPlayAndRecord

                // if(Platform.OS === 'ios'){
                //     this.audioPath =
                //         AudioUtils.CachesDirectoryPath + `${new Date().getTime()}.aac`;
                // }else {
                //     this.audioPath = AudioUtils.DocumentDirectoryPath + `${new Date().getTime()}.aac`;
                // }
                // showToast(this.state.audioPath);

                this.prepareRecordingPath(this.state.audioPath);
                // 录音进展
                AudioRecorder.onProgress = (data) => {
                    // console.log('当前的临时是否展示---',this.state.tempShowVoiceTime);
                    if (data.currentTime >10){
                        // console.log('大于10s走这里---',data.currentTime);
                        this.setState({showVoiceTime:true,recordTime:10,recordTimeValue:10,tempAudioPath:this.state.audioPath,tempAudioTime:10,tempShowVoiceTime:false,isNotChangeAudio:false});
                        this.handleStopAudio();
                        return;
                    }
                    // console.log('当前正在录音时间---取整---',data.currentTime,Math.ceil(data.currentTime));
                    // console.log('新的取整',parseInt(data.currentTime));
                    this.setState({
                        recordTime: parseInt(data.currentTime),
                        recordTimeValue:parseInt(data.currentTime)
                    });
                    // this.setState({
                    //     recordTime: Math.ceil(data.currentTime),
                    //     recordTimeValue:Math.ceil(data.currentTime)
                    // });

                    // console.log('当前录音时间---',this.state.recordTime,Math.ceil(data.currentTime))
                };
                // 完成录音
                AudioRecorder.onFinished = (data) => {
                    // data 录音数据，可以在此存储需要传给接口的路径数据
                    // console.log(this.state.recordTime)
                    console.log('录音数据--',data);
                    // showToast(this.state.recordTime);
                };
            })
    }

    // 自定义导航栏
    _renderNavView() {
        return (
            <View style={{ width: "100%", height: 90, backgroundColor:'white',top:0}}>
                <View style={{height:90,flexDirection:'row',width:'100%',}}>
                    <View style={{color:'white',left:14,top:20,width:80,height:70,}}>
                        <TouchableOpacity
                            onPress={()=>{
                                if (this.state.showVoiceTime){
                                    this.handlePauseAudio();
                                }
                                this._onPressBack();
                                // this.props.navigation.pop()
                            }}>
                            <Text style={{
                                width:80,
                                left:0,
                                lineHeight:66,
                                alignItems: 'center',
                                // width:Utils.getScreenWidth()-50,
                                justifyContent: 'center',
                                textAlign:'left',
                                textAlignVertical:'center',
                                color:'#333333',
                                backgroundColor:'white',
                                fontSize:15,
                            }}>{stringsTo('cancel')}</Text>
                        </TouchableOpacity>
                    </View>
                    <Text style={{
                        position:'absolute',
                        width:screen_width-190,
                        lineHeight:68,
                        left:100,
                        top:20,
                        alignItems: 'center',
                        // width:Utils.getScreenWidth()-50,
                        justifyContent: 'center',
                        textAlign:'center',
                        textAlignVertical:'center',
                        color:'black',
                        backgroundColor:'white',
                        fontSize:18,
                        fontWeight:'bold',
                    }}>{this.state.isVoiceAdd ? stringsTo('voice_for_add'):stringsTo('voice_for_edit')}</Text>

                    <View style={{color:'white',position:'absolute',right:14,top:20,width:70,height:70,}}>
                        <TouchableOpacity
                            disabled={this.state.recordTime == 0 ? true :false}
                            onPress={()=>{
                                // this._saveFile();
                                console.log('保存');
                                if (this.state.showVoiceTime){
                                    this.handlePauseAudio();
                                }

                                if (!this.state.isVoiceAdd && this.state.isNotChangeAudio){
                                    // 编辑状态下且音频未修改  名称可改可不改
                                    console.log('未修改音频---');
                                    this.saveNotChangeAudio();
                                }else {
                                    this.saveAudio();
                                }
                            }}>
                            <Text style={{
                                width:70,
                                left:0,
                                lineHeight:68,
                                alignItems: 'center',
                                // width:Utils.getScreenWidth()-50,
                                justifyContent: 'center',
                                textAlign:'right',
                                textAlignVertical:'center',
                                color:this.state.recordTime == 0 ? '#B2B2B2':imiThemeManager.theme.primaryColor,
                                backgroundColor:'white',
                                fontSize:15,
                            }}>{stringsTo('imi_save')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>);
    }

    saveAudio() {
        // let timestamp = Date.parse(new Date());
        if (this.state.recordTimeValue <= 0){
            return
        }
        let timestamp = new Date().getTime();
        let tempTimeStr = timestamp.toString();
        // let pcmOutPath = AudioUtils.DocumentDirectoryPath + `/quick_audio_${new Date().getTime()}.pcm` ;
       let pcmOutPath = IMIFile.storageBasePath + `/quick_audio_${new Date().getTime()}.pcm`
        let aacTempPath = this.state.audioPath;
        var tempArr = aacTempPath.split('/');
        let lastStr = tempArr[tempArr.length-1]
        // console.log('tempArr--',tempArr)
        // console.log('tempStr',lastStr)
        let aacTopcmArr = ['ffmpeg','-i',aacTempPath,'-ar','16000','-ac','1','-acodec','pcm_s16le','-f','s16le',pcmOutPath];
        // let aacTopcmArr = ['ffmpeg','-i',aacTempPath,'-acodec','pcm_s16le','-f','s16le','-ac','1','-ar','16000',pcmOutPath]
        console.log('时间戳--',tempTimeStr,aacTempPath,pcmOutPath,aacTopcmArr);
        showLoading(stringsTo('commWaitText'), true);
        IMIFfmpegUtils.runCommand(aacTopcmArr).then((data) => {
            // showLoading(false);
            // console.log('aac转pcm成功',data);
            // let opusOutPath = AudioUtils.DocumentDirectoryPath + `/quick_audio_${new Date().getTime()}.opus` ;
            let opusOutPath = IMIFile.storageBasePath + `/quick_audio_${new Date().getTime()}.opus` ;
           //  console.log('pcm输入路径---',pcmOutPath);
           // console.log('opus输出路径---',opusOutPath);
           //  console.log('opus输出路径---',opusOutPath);
           //  console.log('pcm输入路径---',pcmOutPath);
           //  console.log('opus输出路径---',opusOutPath);
           //  console.log('opus输出路径---',opusOutPath);
            IMIAudioUtils.handPcm2Opus(pcmOutPath,opusOutPath,false).then((data) => {
                console.log('pcm转opus成功',data);
                console.log('pcm转opus成功',data);
                this.getFileUrl(opusOutPath)
            }).catch((error) => {
                showLoading(false);
                showToast(I18n.t('operationFailed'));
                console.log('pcm转opus失败--',error);
            })
        }).catch((error) => {
            console.log('aac转pcm失败--',error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
        })
        // this.getFileUrl()
    }

    getFileUrl(opuspath) {
        // alert(event);
        console.log('opuspath路径---',opuspath);
        console.log('opuspath路径---',opuspath);
        const params = {
            Path: 'api/app_file/device/upload_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                folder: 'customize_audio_alarm',
                suffix:'opus',
                timer:600000,
            }
        };
        console.log('获取上传文件参数',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // showLoading(false);
            let tempStr = JSON.parse(data);
            // console.log('获取上传文件数据--',data);
            // console.log('json转---',tempStr.path,tempStr.uploadUrl);
            // console.log('获取上传文件数据--',data);
            // console.log('json转---',tempStr.path,tempStr.uploadUrl);
            this.uploadUrl(tempStr,opuspath);
            // this.uploadUrlNew(tempStr)
        }).catch((error) => {
            console.log('获取文件上传失败--',error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    uploadUrl(dataStr,opusPath) {
        // console.log('opus上传路径---',opusPath);
        // console.log('上传url---',dataStr);
        // console.log('opus上传路径---',opusPath);
        // console.log('上传url---',dataStr);
        let uploadUrl = dataStr.uploadUrl;
        IMIIotRequest.uploadFileToIMICloud(uploadUrl,opusPath,dataStr.path,'').then((data)=>{
            // console.log('上传opus成功---',data)
            // console.log('上传opus成功---',data)
            this.setState({uploadOpusPath:dataStr.path})
            this.getAACUrl();
            // this.downAudioUrl(dataStr);
            // showLoading(false);
            // showToast(I18n.t('operationFailed'));
        }).catch((error)=>{
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('上传失败',error)
        })

    }

    getAACUrl() {
        const params = {
            Path: 'api/app_file/device/upload_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                folder: 'customize_audio_alarm',
                suffix:'aac',
                timer:600000,
            }
        };
        console.log('获取aac上传文件参数',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // showLoading(false);
            let tempStr = JSON.parse(data);
            console.log('获取aac上传文件数据--',data);
            console.log('aac-json转---',tempStr.path,tempStr.uploadUrl)
            console.log('获取aac上传文件数据--',data);
            console.log('aac-json转---',tempStr.path,tempStr.uploadUrl)
            this.uploadAACUrl(tempStr);
        }).catch((error) => {
            console.log('获取文件上传失败--',error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }
    uploadAACUrl(dataStr) {
        console.log('aac-上传url---',dataStr);
        console.log('aac-上传url---',dataStr);
        let uploadUrl = dataStr.uploadUrl;
        IMIIotRequest.uploadFileToIMICloud(uploadUrl,this.state.audioPath,dataStr.path,'').then((data)=>{
            console.log('aac---',data);
            this.uploadData(dataStr);
        }).catch((error)=>{
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('上传失败',error)
        })
    }

    uploadData(tempStr) {
       console.log('上传物模型',tempStr,this.state.typeStr);
        console.log('上传物模型',tempStr,this.state.typeStr);
        let paramsData =
            {   "index":this.state.location,
                "audio_path":tempStr.path,
                "audio_name":this.state.nameValue,
                "audio_time":this.state.recordTimeValue,
                "audio_opus_path":this.state.uploadOpusPath,
            };
        if (this.state.isVoiceAdd) {
            //新增
            this.state.paramArr.push(JSON.stringify(paramsData));
            console.log('新增数据---',this.state.paramArr);
            console.log('新增数据---',this.state.paramArr);
        } else {
            //编辑提示音
            let index;
            for (let i = 0; i < this.state.paramArr.length; i++) {
                let voiceItem = this.state.paramArr[i];
                if (voiceItem.index == this.state.location){
                    index = i
                }
            }
            this.state.paramArr.splice(index, 1, JSON.stringify(paramsData));
        }
       let params;
        if (this.state.typeStr == 'lightVoice') {
            params = {SoundLightCustomAudioList:this.state.paramArr};
            console.log('声光报警自定义音频-',paramsData,params);

        }else if (this.state.typeStr == 'onekeyVoice'){
            // params = {OneKayAlarmCustomAudioList:JSON.stringify(paramsData)};
            params = {OneKeyCustomAudioList:this.state.paramArr};
            console.log('一键报警自定义音频-',paramsData,params);
        }

    LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
        // 上传自定义音频之后，在修改提示音
        // this.updateAudioUrl(tempStr.path)
        this.updateAudioUrl(this.state.uploadOpusPath)
    // this.timer = setTimeout(
    //     () => {
    //         showToast(I18n.t('settings_set_success'));
    //         showLoading(false);
    //         this._onPressBack();
    //     },
    //     1500
    // );
}).catch((error) => {
    console.log('失败----',error);
    console.log('失败----',error);
    showToast(I18n.t('waitFailedTip'));
    showLoading(false);
   });
    }

    // 不修改音频，名称可修改或者不修改
    saveNotChangeAudio() {
        showLoading(stringsTo('commWaitText'), true);
        // console.log('当前未改变的aac路径',this.state.downTempAudioPath);
        // console.log('当前未改变的opus路径',this.state.uploadOpusPath);
        // console.log('当前选中音频',this.state.location,this.state.paramArr.length);

        let paramsData =
            {   "index":this.state.location,
                "audio_path":this.state.downTempAudioPath,
                "audio_name":this.state.nameValue,
                "audio_time":this.state.recordTimeValue,
                "audio_opus_path":this.state.uploadOpusPath,
            };
        // console.log('当前paramArr---',this.state.paramArr);
        // console.log('当前修改的param--',paramsData);

        //编辑提示音
        let index;
        for (let i = 0; i < this.state.paramArr.length; i++) {
            // let voiceItem = this.state.paramArr[i];
            let  voiceItem = JSON.parse(this.state.paramArr[i]);
            console.log('voiceItm---',voiceItem.index,this.state.location);
            if (voiceItem.index == this.state.location){
                index = i;
            }
        }
        // console.log('当前第几个---',index);
        this.state.paramArr.splice(index, 1, JSON.stringify(paramsData));
        // console.log('当前修改后的paramArr',this.state.paramArr);

        let params;
        if (this.state.typeStr == 'lightVoice') {
            params = {SoundLightCustomAudioList:this.state.paramArr};
            // console.log('声光报警自定义音频-',paramsData,params);

        }else if (this.state.typeStr == 'onekeyVoice'){
            // params = {OneKayAlarmCustomAudioList:JSON.stringify(paramsData)};
            params = {OneKeyCustomAudioList:this.state.paramArr};
            // console.log('一键报警自定义音频-',paramsData,params);
        }

        LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
            // 上传自定义音频之后，在修改提示音
            // this.updateAudioUrl(tempStr.path)
            this.updateAudioUrl(this.state.uploadOpusPath);
            // this.timer = setTimeout(
            //     () => {
            //         showToast(I18n.t('settings_set_success'));
            //         showLoading(false);
            //         this._onPressBack();
            //     },
            //     1500
            // );
        }).catch((error) => {
            // console.log('失败----',error);
            // console.log('失败----',error);
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }


    updateAudioUrl(pathStr) {
        console.log('获取提示音',pathStr)
        let params;
        if (this.state.typeStr == 'lightVoice') {
            let tempStr = JSON.parse(this.state.updateData);
            let paramsData = {"sound_light_alarm_switch":tempStr.sound_light_alarm_switch,
                // "only_people_detect_switch":tempStr.only_people_detect_switch,
                "only_people_detect_switch":8,
                "start_time":tempStr.start_time,
                "end_time":tempStr.end_time,
                "audio_path":pathStr,
                "mode":tempStr.mode,
                "duration":tempStr.duration};
            params = {SoundLightAlarm:JSON.stringify(paramsData)};
            console.log('自定义音频页面设置声光报警提示音-',paramsData,JSON.stringify(params));
        }else if (this.state.typeStr == 'onekeyVoice'){
            let tempStr = JSON.parse(this.state.updateData);
            let paramsData =
                {"one_key_alarm_switch":tempStr.one_key_alarm_switch,
                    "audio_path":pathStr,
                    "mode":tempStr.mode,
                    "duration":tempStr.duration};
            params = {OneKeyAlarm:JSON.stringify(paramsData)};
            console.log('自定义音频页面设置一键报警提示音-',paramsData,JSON.stringify(params));
        }
        LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
            this.timer = setTimeout(
                () => {
                    showToast(I18n.t('settings_set_success'));
                    showLoading(false);
                    this._onPressBack();
                },
                1500
            );
        }).catch((error) => {
            console.log('失败----',error);
            console.log('失败----',error);
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });

    }

    downAudioUrl() {
        const params = {
            Path: 'api/app_file/device/download_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path: this.state.downTempAudioPath,
                timer:600000,
            }
        };
        console.log('获取下载文件参数',params);
        showLoading(stringsTo('commWaitText'), true);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // showLoading(false);
            let tempStr = JSON.parse(data);
            console.log('获取下载文件数据--',tempStr);
            this.getDownAudioPath(tempStr);
        }).catch((error) => {
            console.log('下载文件失败--',error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    getDownAudioPath(dataStr) {
        console.log('下载data',dataStr);
        let fileName = `/quick_audio_${new Date().getTime()}.aac`;
        // // let downAudioPath = AudioUtils.DocumentDirectoryPath + fileName;
        // let downAudioPath = Sound.DOCUMENT + fileName;
        // let fileName = `${temp_down_audio_path}.aac`;
        console.log('下载路径--',dataStr.downloadUrl);
        IMIDownload.downloadToPath(EVENT_NAME, dataStr.downloadUrl, IMIFile.storageBasePath, fileName);
        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            if (event.status === IMIDownload.STATUS_START) {
                console.log('开始下载');
                this.setState({isCanPress:false});
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                console.log('正在下载');
                this.setState({isCanPress:false});
            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                // console.log(EVENT_NAME + " download error mataInfo : " + mataInfo)
                showLoading(false);
                showToast(I18n.t('waitFailedTip'));
                this.setState({isCanPress:true});
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
            if (event.status === IMIDownload.STATUS_CANCEL) {
                // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                //用过一次必须释放
                this.setState({isCanPress:true});
                this.listener && this.listener.remove();
                this.listener = null;
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                const path = `${event.downloadPath}/${fileName}`;
                this.setState({isDownUrl:true,audioPath:path,tempAudioPath:path,stop:true,recordTime:this.state.editTempTime,recordTimeValue:this.state.editTempTime},callback=>{
                    console.log('下载成功');
                    console.log('下载成功后文件路径-常量---',path);
                    console.log('下载成功路径---',this.state.audioPath);
                    showLoading(false);
                    this.handlePlayAudio()
                    this.editdownTimer = setInterval(() => { //从10秒开始倒计时
                        let time = this.state.recordTimeValue - 1;
                        if (time <= 0) { //倒计时结束，停止视频播放
                            this.editdownTimer && clearInterval(this.editdownTimer);
                            this.setState({
                                recordTimeValue:this.state.recordTime,
                                isCanPress:true,
                        });
                        } else {
                            this.setState({
                                recordTimeValue: time,
                                isCanPress:false,
                            });
                        }
                    }, 1000);
                })

                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });
    }

    _deleteUrl() {
        const params = {
            Path: 'api/app_file/device/delete_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path:this.state.downTempAudioPath,
            }
        };
        console.log('删除aac文件参数',params);
        showLoading(stringsTo('commWaitText'), true);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            this._deleteOpusUrl()
        }).catch((error) => {
            console.log('删除文件上传失败--',error);
            showLoading(false);
            showToast(I18n.t('delete_failed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }
    _deleteOpusUrl() {
        const params = {
            Path: 'api/app_file/device/delete_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path:this.state.uploadOpusPath,
            }
        };
        console.log('删除opus文件参数',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
           this.deleteData();
        }).catch((error) => {
            console.log('获取文件上传失败--',error);
            showLoading(false);
            showToast(I18n.t('delete_failed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

 deleteData() {
         let tempData = [];
         let allData = this.state.paramArr;
        if (allData.length == 1){
            tempData = [];
        }else {
            let index;
            for (let i = 0; i < allData.length; i++) {
                let tempStr = JSON.parse(allData[i]);
                if (tempStr.index == this.state.location){
                    index = i;
                }
            }
            console.log('第几个----',index);
            let result = allData.splice(index, 1);
             tempData = allData;
        }
     console.log('tempData---',tempData);
     let params;
     if (this.state.typeStr == 'lightVoice') {
         params = {SoundLightCustomAudioList:tempData};
         console.log('删除声光报警自定义音频-',params);

     }else if (this.state.typeStr == 'onekeyVoice'){
         // params = {OneKayAlarmCustomAudioList:JSON.stringify(paramsData)};
         params = {OneKeyCustomAudioList:tempData};
         console.log('删除一键报警自定义音频-',params);
     }

     LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
         let newAudioUrl;
         let voiceItem = voiceArr[1];
         if (IMIHost.serverCode == 0){
             //大陆
             newAudioUrl = voiceItem.replyCNUrl;//提示音路径
         }else  if (IMIHost.serverCode == 1){
             //新加坡
             newAudioUrl = voiceItem.replySGUrl;//提示音路径
         }else  if (IMIHost.serverCode == 3){
             //美国
             newAudioUrl = voiceItem.replyUSUrl;//提示音路径
         }else  if (IMIHost.serverCode == 4){
             //欧洲
             newAudioUrl = voiceItem.replyDEUrl;//提示音路径
         }
         console.log('newAudioUrl--',newAudioUrl);
         this.updateAudioUrl(newAudioUrl)

     }).catch((error) => {
         console.log('失败----',error);
         console.log('失败----',error);
         showToast(I18n.t('delete_failed'));
         showLoading(false);
     });
 }

    render() {
        StatusBar.setBarStyle('dark-content');//状态栏字体刷黑色
        StatusBar.setBackgroundColor('white');//状态栏背景颜色
        return (<View style={styles.container}>
                {this._renderNavView()}
                <ListItem titleStyle={{color:'#333333',fontWeight:'normal'}} title={stringsTo('voice_for_name')} value={this.state.nameValue} onPress={() => {
                    // this.props.navigation.push("ImageSetting");
                    this.setState({nameDiaLogVisible:true})
                }}/>
                {this._renderTipVoiceView()}
                {this.state.isVoiceAdd ? null :
                    <View style  = {{width:'100%',height:75,position:'absolute',bottom:0}}>
                    <RoundedButtonView buttonText={stringsTo('delete_title')}
                                                                   disabled={false}
                                                                   buttonStyle={{backgroundColor: "white", margin: 15,}}
                                                                   buttonTextStyle={{color: "#E74D4D"}}
                                                                   onPress={() => {
                                                                       console.log('删除');
                                                                       this.setState({isShowDeleteAlert: true});

                                                                       // let index;
                                                                       // for (let i = 0; i < this.state.paramArr.length; i++) {
                                                                       //     let tempStr = JSON.parse(this.state.paramArr[i]);
                                                                       //     if (tempStr.index == this.state.location){
                                                                       //         index = i;
                                                                       //     }
                                                                       // }
                                                                       // console.log('删除前数组----',this.state.paramArr);
                                                                       // console.log('第几个----',index,this.state.location);
                                                                       // let tempData = this.state.paramArr.splice(index, 1);
                                                                       // let allData = this.state.paramArr;
                                                                       // console.log('删除后的数组',tempData);
                                                                       // console.log('删除的数据---',allData);
                                                                       // console.log('删除后的数组3',this.state.paramArr);
                                                                       // console.log('删除aac路径',this.state.downTempAudioPath);
                                                                       // console.log('删除opus路径',this.state.uploadOpusPath);

                                                                   }}/>
                    </View> }

                {Platform.OS == 'ios' ? this._showiOSNameDialogView() : this._showNameDialogView()}
                {this._showRecordAlertView()}
                {this._showDelertArt()}
        </View>
        )
    }

    _showDelertArt() {
       if (!this.state.isShowDeleteAlert){
          return;
       }
       return(
           <MessageDialog
               title={stringsTo('delete_alert')}
               visible={this.state.isShowDeleteAlert}
               canDismiss={true}
               onDismiss={()=>{this.setState({isShowDeleteAlert: false})}}
               buttons={[
                   {
                       text: I18n.t("cancel"),
                       accessibilityLabel: 'cancelVoiceDelete',
                       callback: _ => {
                           this.setState({isShowDeleteAlert: false});
                       }
                   },
                   {
                       text: I18n.t("ok_button"),
                       accessibilityLabel: 'okVoiceDelete',
                       callback: _ => {
                           if (this.state.showVoiceTime){
                               this.handlePauseAudio();
                           }
                           this.setState({isShowDeleteAlert:false});
                           this._deleteUrl();
                       }
                   },
               ]}
           >
           </MessageDialog>
       )
    }

    _showiOSNameDialogView() {
        if (!this.state.nameDiaLogVisible) {
            return null;
        }
        return (
            <Modal
                animationType="none"
                transparent={true}
                visible={this.state.nameDiaLogVisible}
                onRequestClose={() => {
                    console.log('onRequestClose------');
                    this.setState({nameDiaLogVisible:false})
                }}

                // onShow={() => {
                //     this.setState({playingItemId: '', recordTime: 0 });
                // }}
                onDismiss={() => {
                    console.log('onDismiss------');
                    this.setState({nameDiaLogVisible:false})
                }}>

                <View style={{
                    flexDirection: 'column',
                    flex: 1,
                    justifyContent: 'flex-end',
                    backgroundColor: '#00000099'
                }}>
                    <TouchableWithoutFeedback onPress={() => {
                        this.setState({nameDiaLogVisible:false})
                    }}>
                        <View style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}/>
                    </TouchableWithoutFeedback>

                    <View style={{
                        flexDirection: 'column',
                        marginLeft:14,
                        marginRight:14,
                        borderRadius: 20,
                        position: 'absolute',
                        top:150,
                        // height:200,
                        width:screen_width-28,
                        borderTopLeftRadius:20,
                        borderTopRightRadius:20,
                        backgroundColor: '#FFFFFF', zIndex: 1
                    }}>
                        <Text style={{
                            fontSize: 16,
                            color: '#333333',
                            textAlign: 'center',
                            textAlignVertical: 'center',
                            lineHeight: 60,
                            // marginTop:25,
                            // marginBottom:10,
                        }}>{stringsTo('voice_for_name')}</Text>

                        <View style = {{backgroundColor:'white'}}>
                            <TextInput
                                ref="textInput"
                                style={styles.textInputStyle}
                                placeholder={stringsTo('voice_for_enter_name')}
                                placeholderTextColor={'#7F7F7F'}
                                onChangeText={ (text) => this._onChangeText(text) }
                                // value={this.state.nameValue}
                                // onBlur={this._reset.bind(this)}
                                // onFocus={this._onFocus.bind(this, 'textInput')}
                            >
                            </TextInput>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            height: 60,
                            lineHeight: 60,
                            marginTop: 14,
                            borderBottomLeftRadius:20,
                            borderBottomRightRadius:20,
                            backgroundColor: '#FFFFFF'
                        }}>
                            <TouchableWithoutFeedback
                                onPress={() => {
                                    this.setState({nameValue:this.state.nameCancleTit})
                                    this.setState({nameDiaLogVisible:false})
                                }}>
                                <View style={{
                                    width: (screen_width - 14*5) / 2,
                                    alignItems: 'center',
                                    marginLeft: 14,
                                    marginRight: 14,
                                    marginBottom:14,
                                    justifyContent: 'center',
                                    borderRadius: 22.5,
                                    height: 45,
                                    backgroundColor: '#F2F3F5',
                                }}>
                                    <Text style={{
                                        width: '100%',
                                        height: '100%',
                                        lineHeight: 45,
                                        textAlign: 'center',
                                        textAlignVertical: 'center',
                                        color: '#7F7F7F',
                                        fontSize: 15,
                                        fontWeight: 'bold'
                                    }}>{stringsTo('cancel')}</Text>
                                </View>
                            </TouchableWithoutFeedback>

                            <TouchableWithoutFeedback
                                onPress={() => {
                                    let tempStr = this.state.nameValue;
                                    let regStr = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
                                    let unStr = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g;
                                    let regEmo = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
                                    let reg2 = new RegExp("[`~!@#$^&*()=|{}':;',%％￥\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]");
                                    if (tempStr.match(regEmo) || (tempStr.match(reg2))){
                                        console.log('输入确认走判断');
                                        showToast(I18n.t('imi_input_text_tip'));
                                        this.setState({nameValue:this.state.nameCancleTit,nameDiaLogVisible:false});
                                        return;
                                    }
                                    this.setState({nameDiaLogVisible:false,nameCancleTit:this.state.nameValue});
                                }}>
                                <View style={{
                                    width: (screen_width - 14*5) / 2,
                                    alignItems: 'center',
                                    marginRight: 14,
                                    marginBottom:14,
                                    justifyContent: 'center',
                                    borderRadius: 22.5,
                                    height: 45,
                                    backgroundColor: imiThemeManager.theme.primaryColor,
                                }}>
                                    <Text style={{
                                        width: '100%',
                                        height: '100%',
                                        lineHeight: 45,
                                        textAlign: 'center',
                                        textAlignVertical: 'center',
                                        color: '#FFFFFF',
                                        fontSize: 15,
                                        fontWeight: 'bold'
                                    }}>{stringsTo('ok_button')}</Text>
                                </View>
                            </TouchableWithoutFeedback>

                        </View>

                    </View>
                </View>
            </Modal>
        );
    }


    _showNameDialogView() {
        if (!this.state.nameDiaLogVisible) {
            return null;
        }
        return(<View>
            <MessageDialog
                title={I18n.t('voice_for_name')}
                visible={this.state.nameDiaLogVisible}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        accessibilityLabel: 'cancelVoiceDeleteName',
                        callback: _ => {
                            this.setState({nameValue:this.state.nameCancleTit})
                            this.setState({nameDiaLogVisible: false});
                            // this.setState(tempRecordTimeSetting);
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        accessibilityLabel: 'okVoiceDeleteName',
                        callback: _ => {
                            // if (this.state.nameValue.length === 0) { return; }
                            // this.confirmRenameWithMac(this.state.nameValue);
                            // this.setState({nameCancleTit:this.state.nameValue})

                            let tempStr = this.state.nameValue;
                            let regStr = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
                            let unStr = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g;
                            let regEmo = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
                            let reg2 = new RegExp("[`~!@#$^&*()=|{}':;',%％￥\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]");
                            if (tempStr.match(regEmo) || (tempStr.match(reg2))){
                                showToast(I18n.t('imi_input_text_tip'));
                                this.setState({nameValue:this.state.nameCancleTit,nameDiaLogVisible:false});
                                return;
                            }
                            this.setState({nameDiaLogVisible:false,nameCancleTit:this.state.nameValue});
                            // this.setState({nameDiaLogVisible:false});
                            // this.updateRecordTime();
                        }
                    },
                ]}
            >
                <View style = {{backgroundColor:'white'}}>
                    <TextInput
                               ref="textInput"
                               style={styles.textInputStyle}
                               placeholder={stringsTo('voice_for_enter_name')}
                               placeholderTextColor={'#7F7F7F'}
                               onChangeText={ (text) => this._onChangeText(text) }
                               // value={this.state.nameValue}
                               // onBlur={this._reset.bind(this)}
                               // onFocus={this._onFocus.bind(this, 'textInput')}
                    >
      </TextInput>
                </View>
            </MessageDialog>
        </View>)
    }
    _onChangeText(textStr) {
        // console.log('hhh输入值--value---',textStr);
        this.setState({nameValue:textStr});
         // this.setState({nameValue:textStr,nameCancleTit:this.state.nameValue});

       // let tempText = textStr.replace(regStr, '');
       // let tempTextNew = tempText.replace(unStr,'');
       // console.log('tempText---',tempText);
       // console.log('new---',tempTextNew);
       //  // this.setState({ inputTextString: inputText });
       //  this.setState({nameValue:tempTextNew});

    }

    _reset() {

        this.refs.scrollView.scrollTo({y: 0});

    }

    _onFocus(refName) {

        setTimeout(()=> {

            let scrollResponder = this.refs.scrollView.getScrollResponder();
            scrollResponder.scrollResponderScrollNativeHandleToKeyboard(
                ReactNative.findNodeHandle(this.refs[refName]), 0, true);

        }, 100);
    }
    confirmRenameWithMac(value) {
        console.log('输入值--value---',value);
        if (value.length <= 0 || value == null || value.isEmpty) {
            showToast(stringsTo('voice_for_enter_name'));
            return;
        }

        // this.setState({ nameValue: value[0] });

        // RpcUtil.getRpcRequestWithMethodWithPropsArray('rename', [this.state.itemModel.mac, value[0]], (res) => {
        //     if (res.length > 0) {
        //         this.state.itemModel.name = value[0];
        //         console.log('delete_camera,修改成功');
        //         Toast.success('save_success');
        //         this.setState({ renameValue: value[0] });
        //         DeviceEventEmitter.emit('RefreshCameraList', []);
        //     } else {
        //         console.log('delete_camera,修改失败');
        //         Toast.fail('save_faild');
        //     }
        // }, (error) => {
        //     console.log('delete_camera,修改失败');
        //     Toast.fail('save_faild');
        // });
    }
    //录音弹窗
    _showRecordAlertView() {
        return(<View>
            <MessageDialog
                title={I18n.t('potlight_alarm_mode')}
                showTitle={false}
                visible={this.state.showRecordAlert}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        accessibilityLabel: 'cancelRecordMode',
                        callback: _ => {
                            if (this.state.showVoiceTime){
                                this.handleStopAudio();
                                if (this.state.isVoiceAdd == false){
                                    //编辑提示音时重新录音 点击取消，重新赋值原来的录音时间
                                    this.setState({recordTime:this.state.editTempTime,recordTimeValue:this.state.editTempTime,isNotChangeAudio:true});
                                }
                            console.log('取消录音时间p---',this.state.recordTime,this.state.recordTimeValue,this.state.editTempTime);
                                console.log('获取上次录音确定时间-',this.state.tempAudioTime,this.state.recordTime);
                                this.setState({ showRecordAlert: false, isRecording: false,tempShowVoiceTime:true,recordTime:this.state.tempAudioTime,recordTimeValue:this.state.tempAudioTime},callback=>{
                                    console.log('添加重新录制取消--'+this.state.recordTime);
                                });
                            }else {
                                this.handleStopAudio()
                                // this.handleDelAudio()
                                this.setState({ showRecordAlert: false, isRecording: false,recordTime:0,showVoiceTime:false });
                            }
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        accessibilityLabel: 'okRecordMode',
                        callback: _ => {
                            let totalTimeVaue = this.state.recordTime;
                            console.log('点击确定--录音时间---',this.state.recordTime,totalTimeVaue);
                            this.handleStopAudio();
                            this.setState({ showRecordAlert: false, isRecording: false, showVoiceTime:true,recordTimeValue:totalTimeVaue,tempAudioPath:this.state.audioPath,tempAudioTime:totalTimeVaue,tempShowVoiceTime:false,isNotChangeAudio:false},callback=>{
                                console.log('确定录音时间---'+totalTimeVaue+'临时确定时间'+this.state.tempAudioTime,)
                            });
                        }
                    },
                ]}
            >
                <Text style={{
                    fontSize: 24,
                    color: '#12ab9c',
                    textAlign: 'center',
                    textAlignVertical: 'center',
                    marginTop:25,
                    marginBottom:10,
                }}>{`00:00:${ this.state.recordTime > 9 ? this.state.recordTime : (`0${ this.state.recordTime }`) }`}</Text>
                {this._renderWaveViewLayout()}

            </MessageDialog>
        </View>)
    }

    //录音波浪
    _renderWaveViewLayout() {
        return (<View style = {{backgroundColor:'white',width:screen_width,top:20,height:56}}>
            <WaveView
                waveHeight={36}
                waveWidth={screen_width}/>
        </View>)
    }

    _renderTipVoiceView() {
        return (<View style = {styles.viewContainerNew}>
            <View style = {{flex:1}}>
                <Text ellipsizeMode="tail"
                      numberOfLines={2}
                      style = {styles.titStyle}>{stringsTo('voice_for_tip_tit_time')}</Text>
            </View>

            <View style = {{flex:1,justifyContent: 'flex-end',flexDirection:'row',}}>
                {this.state.showVoiceTime ?
                    <View style={{height:30,width:70,marginTop:20,marginRight:10,flexDirection:'row', backgroundColor: '#12ab9c1A', borderRadius: 15,opacity:this.state.recordTimeValue == this.state.recordTime ? 1 : 0.5}}>
                    <TouchableOpacity
                        disabled={this.state.isCanPress ? false:true}
                        style={{marginBottom:0,paddingBottom: 0,flexDirection:'row'}}
                        onPress={()=>{
                            console.log('recordTimeValue---recordTime',this.state.recordTimeValue,this.state.recordTime)
                            if (this.state.isCanPress){
                                // console.log('播放时不可点击');
                                // return;
                                if (this.state.isVoiceAdd){
                                    // 添加
                                    this.handlePlayAudio()
                                    this.countdownTimer = setInterval(() => { //从10秒开始倒计时
                                        let time = this.state.recordTimeValue - 1;
                                        if (time <= 0) { //倒计时结束，停止视频播放
                                            this.countdownTimer && clearInterval(this.countdownTimer);
                                            this.setState({
                                                recordTimeValue:this.state.recordTime,
                                                isCanPress:true,
                                            });
                                        } else {
                                            this.setState({
                                                recordTimeValue: time,
                                                isCanPress:false,
                                            });
                                        }
                                    }, 1000);

                                }else {
                                    // 编辑
                                    if (this.state.isDownUrl){
                                        console.log('编辑---');
                                        console.log('编辑下载后recordTimeValue---recordTime',this.state.recordTimeValue,this.state.recordTime)
                                        this.handlePlayAudio();
                                        //重新录制后播放声音
                                        this.editdownTimer = setInterval(() => { //从10秒开始倒计时
                                            let time = this.state.recordTimeValue - 1;
                                            if (time <= 0) { //倒计时结束，停止视频播放
                                                this.editdownTimer && clearInterval(this.editdownTimer);
                                                this.setState({
                                                    recordTimeValue:this.state.recordTime,
                                                    isCanPress:true,
                                                });
                                            } else {
                                                this.setState({
                                                    recordTimeValue: time,
                                                    isCanPress:false,
                                                });
                                            }
                                        }, 1000);
                                    }else {
                                        // 未下载
                                        // 在编辑页面假如重新录制声音之后然后保存上传之后在进入该页面，声音播放小了，所以重新设置下
                                        console.log('编辑未下载recordTimeValue---recordTime',this.state.recordTimeValue,this.state.recordTime);
                                        this._startRecordButtonClicked();
                                        this.tempCancleTimer = setInterval(() => { //从10秒开始倒计时
                                            this.tempCancleTimer && clearInterval(this.tempCancleTimer);
                                            this.handleStopAudio()
                                        }, 300);

                                        this.downAudioUrl();
                                    }

                                }
                            }else {
                                // console.log('时间不等走这里',this.state.recordTimeValue,this.state.recordTime,this.state.isCanPress);
                            }
                            this.setState({isCanPress:!this.state.isCanPress},callback=>{
                               console.log('当前是否可点击---',this.state.isCanPress);
                            });

                        }}>
                        <Image style={{ width: 20, height: 20, marginLeft:12,marginTop:5}}
                               source={require('../../resources/images/icon_voice_select.png')}></Image>
                        <Text style={{
                            lineHeight: 30,
                            color: '#12ab9c',
                            width: 25,
                            fontSize:12,
                            textAlign: 'right',
                            marginRight:15,
                        }}>{this.state.recordTimeValue+"s"}</Text>
                    </TouchableOpacity>
                </View>
                : null }

                <View style = {{marginRight:14,width:65,height:70}}>
                    <TouchableOpacity
                        disabled={false}
                        style={{marginBottom:0,paddingBottom: 0,flexDirection:'row'}}
                        onPress={()=>{
                            // console.log('点击录制');
                            if (!this.state.hasPermission){
                                showToast(I18n.t('audio_permission_denied'));
                                return ;
                            }

                            if (this.state.showVoiceTime){
                                this.handlePauseAudio();
                            }

                            this.setState({ showRecordAlert: true, isRecording: true ,recordTime:0,});
                            this._startRecordButtonClicked();
                        }}>
                        <Text
                            ellipsizeMode="tail"
                            numberOfLines={1}
                            style={{
                            lineHeight: 66,
                            color: '#12ab9c',
                            width: 65,
                            fontSize:15,
                            textAlign: 'right',
                        }}>{this.state.showVoiceTime ? stringsTo('voice_for_re_record') : stringsTo('voice_for_click_record')}</Text>
                    </TouchableOpacity>
                </View>
            </View>

            </View>)
    }

     // 开始录音
    _startRecordButtonClicked() {
        if (this.state.isRecording) {
            return;
        }

        // let settings = {// only worked for android
        //     RecordType: "audioRecord",
        //     AVFormatIDKey: 'G711',
        //     AVSampleRateKey: 16000,
        //     AVNumberOfChannelsKey: 1, // mono 单声道
        //     AVLinearPCMBitDepthKey: 16// 位深16
        // };
        // fileName = 'hhhhh.alaw';

            isCheckingPermission = true;
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                console.log('当前音频状态---',status);
                if (status == 0) {
                    this.handleStartAudio();
                    isCheckingPermission = false;
                } else if (status == -1) {
                    showToast(stringsTo('audio_permission_denied'));
                    isCheckingPermission = false;
                }
            })
    }

    _stopRecordButtonClicked() {
        // clearInterval(this.interval);
        // this.handleStopAudio()
        this.setState({ isRecording: false ,showRecordAlert:false});

        // Host.audio.stopRecord().then(() => {
        //     console.log('stopRecord');
        //     this.setState({ isRecording: false ,showRecordAlert:false});
        //     // this._stopPeakPowerLoop();
        // });
    }


    /**
     * AudioRecorder.prepareRecordingAtPath(path,option)
     * 录制路径
     * path 路径
     * option 参数
     */
    prepareRecordingPath = (path) => {
        const option = {
            SampleRate: 16000.0, //采样率
            Channels: 1, //通道
            AudioQuality: 'High', //音质
            AudioEncoding: 'aac', //音频编码 aac
            OutputFormat: 'mpeg_4', //输出格式
            MeteringEnabled: false, //是否计量
            MeasurementMode: false, //测量模式
            AudioEncodingBitRate: 32000, //音频编码比特率
            IncludeBase64: true, //是否是base64格式
            AudioSource: 0, //音频源
        }
        AudioRecorder.prepareRecordingAtPath(path,option)
    }

    // 开始录音
    async handleStartAudio() {
        if(this.state.stop) {
            // 初始化录音
            // if(Platform.OS === 'ios'){
            //     this.audioPath =
            //         AudioUtils.CachesDirectoryPath + `${new Date().getTime()}.aac`;
            // }else {
            //     this.audioPath = AudioUtils.DocumentDirectoryPath + `${new Date().getTime()}.aac`;
            // }
            // let audioPathNew = AudioUtils.DocumentDirectoryPath + `/quick_audio_${new Date().getTime()}.aac`;
            let audioPathNew = IMIFile.storageBasePath + `/quick_audio_${new Date().getTime()}.aac`;

            this.setState({audioPath:audioPathNew});
            this.prepareRecordingPath(audioPathNew);
        }
        try {
            await AudioRecorder.startRecording()
            // showToast('开始录音');
        } catch (err) {
            console.error(err)
        }
    }

    // 停止录音
    async handleStopAudio() {
        try {
            await AudioRecorder.stopRecording();
            // this.setState({ stop: true, recording: false });
            this.setState({ isRecording: false ,showRecordAlert:false,stop: true});
        } catch (error) {
            // console.error(error);
            this.setState({ isRecording: false ,showRecordAlert:false,stop: true});
            // 可能录制失败
            // showToast('error',error);
        }
    }

// 播放录音
    async handlePlayAudio() {
        let self = this
        let audioPathNew = '';
        if (this.state.tempShowVoiceTime){
            audioPathNew =  this.state.tempAudioPath;
        }else {
            audioPathNew = this.state.audioPath;
        }

        whoosh = new Sound(audioPathNew, '', (err) => {
            if(err) {
                // showToast('加载音频失败',err);
                console.log('播放音频失败---',err,audioPathNew);
                showToast(I18n.t('waitFailedTip'));
                // console.log('加载音频失败')
                return console.log(err)
            }
            whoosh.setVolume(1) // 设置音量
            whoosh.play(success => {
                if(success) {
                    console.log('播放完毕');
                }else {
                    console.log('播放失败')
                    // showToast('播放失败');
                }
            })
        })


        // self.whoosh = new Sound(audioPathNew, '', (err) => {
        //     if(err) {
        //         // showToast('加载音频失败',err);
        //         showToast(I18n.t('waitFailedTip'));
        //         // console.log('加载音频失败')
        //         return console.log(err)
        //     }
        //     self.whoosh.setVolume(1) // 设置音量
        //     self.whoosh.play(success => {
        //         if(success) {
        //             console.log('播放完毕');
        //         }else {
        //             console.log('播放失败')
        //             // showToast('播放失败');
        //         }
        //     })
        // })
    }
    async handlePauseAudio() {
        whoosh.pause();
    }

    // 删除录音
    async handleDelAudio() {
        // 初始化录音
        this.prepareRecordingPath(this.state.audioPath);
        let {listOptionData} = this.state
        listOptionData[11].value = ''
        this.setState({
            recordTime: 0,
            stop: false,
            listOptionData
        })
    }


}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F1F1F1',
    },
    viewContainerNew:{
        width:'100%',
        height:70,
        flexDirection: 'row',
        backgroundColor:'white',
    },
    titStyle:{
        lineHeight:66,
        color:'#333333',
        textAlign:'left',
        fontSize:15,
        marginLeft:14,
    },
    textInputStyle: {
        // 设置尺寸
        // width: '100%',
        height:45,
        margin:14,
        paddingLeft:14,
        borderRadius:15,
        // 设置背景颜色
        backgroundColor:'#F1F1F1',
        color: '#000000'
    },
    dialogModal: { // 弹窗
        position: 'absolute',
        bottom: 50, // 距离屏幕底部的边距
        width: screen_width, // 宽度
        borderTopLeftRadius: 20, // 圆角
        borderTopRightRadius: 20, // 圆角
        backgroundColor: '#FFFFFF', // 内容背景色
    },
    dialogBackground: { // 蒙层背景
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)', // 蒙层背景色
    },
});
