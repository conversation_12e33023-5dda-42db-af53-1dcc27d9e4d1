import React, {Component} from 'react';
import {View, Dimensions, StyleSheet, DeviceEventEmitter, Image} from 'react-native';

const {width} = Dimensions.get('window');
let SCREEN_WIDTH = width;
let cur_direction = -1; //当前云台转动方位
let ptz_pressed = false; //当前是否在操作云台
let ptz_pressedPanoramic = false; //当前是否在操作全景云台
import {RNLine, showLoading, showToast, Separator, colors} from '../../../../imilab-design-ui';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import HomePageLivePlayerComponent from '../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent';
import {LetDevice, BaseDeviceComponent, imiAlarmEventCloudApi} from '../../../../imilab-rn-sdk';
import PanoramicView from '../live/PanoramicView';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import {IMIStorage} from '../../../../imilab-rn-sdk';
import NetInfo from '@react-native-community/netinfo';
import TinyWindowLivePlayer from '../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/TinyWindowLivePlayer/TinyWindowLivePlayer';
import {XText, XView} from 'react-native-easy-app';
import JoystickControlView from '../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/JoystickControlView/JoystickControlView';
import {strToArrayBuffer} from '../utils/GenericUtils';
import Toast from 'react-native-root-toast';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import IMIPermission from '../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import {LetIProperties} from '../../../../imilab-rn-sdk';
import IMP2pClient from '../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;

export default class PositionSleepPage extends BaseDeviceComponent {
  constructor(props, context) {
    super(props, context);
    this.state = {
      currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED,
      isFullScreen: false,
      isCalling: false, //是否正在通话
      isSleep: false, //设备是否休眠
      spotlightSliderVisible: false, //控制聚光灯亮度滑动条的隐现
      spotlightBrightness: 0,
      isPanoramic: false,
      sdCardStatus: 0,
      isDataUsage: false,
      vipState: 0,
      panoramicType: 3,
      overAllImgStoreUrl: null,
      leftValue: 1,
      rightValue: 90,
      isEnabledOpen: false,
      sleepPosition: false,
      currentValue: 1,
      yValue: 50,
      stateType: undefined,
      showTips: false,
      currentSnapshotPath: '',
    };
    this.msg_id = 0; // 发送p2p 消息的id,每次进来都要清空，然后依次加一
    this.isFirstLoad = true;
    this.cameraNumber='1'
  }

  componentDidMount() {
    this.IMIVideoView && this.IMIVideoView.prepare();
    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.getNetWork();
    });

    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      if (this.IMIVideoView) {
        this.IMIVideoView.stop();
        // this.IMIVideoView.destroy();
      }
    });

    // Subscribe
    this.unsubscribe = NetInfo.addEventListener(state => {
      if (this.state.stateType !== state.type) {
        this.setState({stateType: state.type});
        this.getNetWork();
        if (state.isConnected) {
          LetDevice.getSingleProperty('10001').then(data => {
           this.setState({
            isSleep: !data?.value?.value
           })
         })
        }
      }
      // console.log('Is connected?', state.isConnected);
    });

    this.getSameData();
    this.getPanoramaProperty();
    //收到监听
    this.listener = DeviceEventEmitter.addListener('PanoramaCompoundFinishE', isSuccess => {
      //收到监听后想做的事情
      console.log(isSuccess); //监听
      if (parseInt(isSuccess)) {
        this.getPanoramaProperty();
        showToast(stringsTo('panoramicSuccess'));
        imiAlarmEventCloudApi
          .panoramaGetPhotoInfo(LetDevice.deviceID)
          .then(res => {
            showLoading(false);
            this.setState({panoramicType: parseInt(res.status), overAllImgStoreUrl: res.url});
          })
          .catch(error => {
            showLoading(false);
          });
      } else {
        showToast(stringsTo('panoramicError'));
        this.setState({panoramicType: 3});
      }
    });
    // LetDevice.registerDeviceEventChangeListener((data)=>{
    //     let {iotId,identifier,value} = data;
    //     if (iotId == LetDevice.deviceID){
    //         if (identifier=='PanoramaCompoundFinishE'){
    //             let {isSuccess} = value;
    //             if (parseInt(isSuccess)){
    //                 this.getPanoramaProperty();
    //                 showToast(stringsTo('panoramicSuccess'));
    //                 imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
    //                     showLoading(false);
    //                     this.setState({panoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
    //                 }).catch(error=>{
    //                     showLoading(false);
    //                 });
    //             }else {
    //                 showToast(stringsTo('panoramicError'));
    //                 this.setState({panoramicType:3});
    //             }
    //         }
    //     }
    // });

    this.devicePropertyListener = LetIProperties.addPropertyChangeListener(event => {
      let data = typeof event === 'object' ? event : JSON.parse(event);
      if (data.iotId == LetDevice.deviceID) {
        if (data.thingid == '10001') {
          this.setState({isSleep: !data.value.value});
        }
      }
    });
     let {
          cameraNumber = '1',          
        } = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model); 
        this.cameraNumber=cameraNumber
  }

  getPanoramaProperty() {
    LetDevice.updateAllPropertyCloud()
      .then(data => {
        let dataObject = JSON.parse(data);
        // let dataObject = data;
        let stateProps = {};
        dataObject?.forEach(item => {
          // console.log(item);

          //状态灯
          if (`${item?.thingId}` === `${10008}`) {
            stateProps.sleepPosition = !!item?.value?.value;
            if (stateProps.sleepPosition) {
              this.IMIVideoView && this.IMIVideoView.stop()
              IMIStorage.load({
                key: LetDevice.deviceID + 'positionSleep',
                autoSync: true,
                syncInBackground: true,
              })
                .then(res => {
                  this.setState({
                    currentSnapshotPath: res.path
                  })
                })
                .catch(_ => {});
            }
          }
        });
        //侦测时间
        /* if (dataObject.PanoramStartMotorPositon) {
          if (dataObject.PanoramStartMotorPositon.value != '') {
            let ary = dataObject.PanoramStartMotorPositon.value.split(',');
            stateProps.leftValue = parseInt(ary[0].substr(1));
          }
        }
        if (dataObject.PanoramEndMotorPositon) {
          if (dataObject.PanoramEndMotorPositon.value != '') {
            let ary = dataObject.PanoramEndMotorPositon.value.split(',');
            stateProps.rightValue = parseInt(ary[0].substr(1));
          }
        }
        if (dataObject.SleepPosition) {
          if (dataObject.SleepPosition.value == 'off' || dataObject.SleepPosition.value == '') {
            stateProps.sleepPosition = false;
          } else {
            let ary = dataObject.SleepPosition.value.split(',');
            stateProps.currentValue = parseInt(ary[0].substr(1));
            stateProps.yValue = parseInt(ary[1].substr(0, 1));
            stateProps.sleepPosition = true;
            stateProps.isEnabledOpen = true;
          }
        } */
        setTimeout(() => {
          this.setState(stateProps);
        }, 100);
      })
      .catch(error => {});
    // 这个接口记得打开不知道要不要
    /*  imiAlarmEventCloudApi
      .panoramaGetPhotoInfo(LetDevice.deviceID)
      .then(res => {
        showLoading(false);
        setTimeout(() => {
          let isEnabledOpen = parseInt(res.status) == 2 ? true : false;

          this.setState({
            panoramicType: !isEnabledOpen && res.timeOutFlag ? 3 : parseInt(res.status),
            isEnabledOpen: isEnabledOpen,
            overAllImgStoreUrl: res.url,
          });
        }, 100);
      })
      .catch(error => {
        showLoading(false);
      }); */
  }

  getSameData() {
    LetDevice.getPropertyCloud(['10001'])
      .then(data => {
        //0休眠 1关闭
        console.log('设备休眠--------SleepStatus' + data, typeof data);
        if (data === '0') {
          this.setState({isSleep: true});
        }
      })
      .catch(error => {
        console.log(JSON.stringify(error));
      });
  }

  getNetWork() {
    IMIStorage.load({
      key: LetDevice.deviceID + 'isDataUsageWarning',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({isDataUsage: res.isDataUsage});
        if (res.isDataUsage) {
          NetInfo.fetch().then(state => {
            if (state.type == 'wifi') {
              this.IMIVideoView.prepare();
            } else {
              this.IMIVideoView && this.IMIVideoView.stop();
            }
          });
        } else {
          if (!this.isFirstLoad) {
            this.IMIVideoView.prepare();
          }
        }
      })
      .catch(_ => {
        if (!this.isFirstLoad) {
          this.setState({isDataUsage: false});
          this.IMIVideoView.prepare();
        }
      });
  }

  componentWillUnmount() {
    if (isAndroid()) {
      // this.IMIVideoView && this.IMIVideoView.destroy();
    }
    //移除监听
    if (this.listener) {
      this.listener.remove();
    }
    this.devicePropertyListener && this.devicePropertyListener.remove();
    LetDevice.removeDeviceEventChangeListener();
    this._subscribe_focus && this._subscribe_focus();
    this._subscribe_blur && this._subscribe_blur();
    this.unsubscribe && this.unsubscribe();
  }

  _onPressBack = () => {
    this.props.navigation.pop();
  };

  /*  _onClickSleepButton = () => {
    //注意：0是休眠 1是关闭休眠
    let params = {
      SleepStatus: this.state.isSleep ? 1 : 0,
    };
    LetDevice.setPropertyCloud(JSON.stringify(params))
      .then(data => {
        console.log('设置休眠成功-------', data);
        this.setState({isSleep: !this.state.isSleep}, () => {
          if (this.state.isSleep) {
            this.IMIVideoView.stop();
          } else {
            this.IMIVideoView.prepare();
          }
        });
      })
      .catch(err => {
        console.log('设置休眠失败-------', err);
      });
  }; */

  /*绘制摇杆式云台View*/
  renderPtzControlView() {
    let sleepPosition = this.state.sleepPosition ? 'none' : 'box-none';
    return (
      <View
        style={{
          flexGrow: 1,
          width: '100%',
          flexDirection: 'column',
          display: 'flex',
          // marginTop: 20,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        pointerEvents={sleepPosition}>
        <JoystickControlView
          onMove={type => {
            if (!this._canStepIn()) {
              return;
            }
            ptz_pressed = false;
            clearInterval(this.setPTZIntervalID);
            this._doDirection(type);
          }}
          onLoosen={() => {
            // 暂时return
            ptz_pressed = false;
            clearInterval(this.setPTZIntervalID);
            // this._getMotorPositonStatus();
          }}
          isFullscreen={false}
          diameterPan={206}
          diameterMid={40}
        />
      </View>
    );
  }
  /*控制云台转动*/
  _doDirection(m_direction) {
    ptz_pressed = true;

    this._checkDirection(m_direction);
    if (m_direction !== 5) {
      // 每100毫秒一次操作
      clearInterval(this.setPTZIntervalID);
      this.setPTZIntervalID = setInterval(() => {
        if (!this._checkDirection(m_direction)) {
          clearInterval(this.setPTZIntervalID);
        }
      }, 100);
    } else {
      ptz_pressed = false;
    }
  }

  /*判断当前是否可操作*/
  _checkDirection(m_direction) {
    console.log('_checkDirection m_direction=' + m_direction + ',ptz_pressed=' + ptz_pressed);
    // console.log("_checkDirection m_direction="+m_direction+",ptz_pressed="+ptz_pressed);
    if (ptz_pressed) {
      if (this.closeMotorPositon) {
        this.closeMotorPositon = false;
        // console.log('云台不发送指令');
      } else {
        this.sendDirectionCmd(m_direction);
        // console.log('云台发送指令');
      }
    }

    return ptz_pressed;
  }

  /*发送控制云台的指令*/
  async sendDirectionCmd(m_direction) {
    const messageJson = JSON.stringify({
      p2p_cmd: 1, //0x1:电机控制
      msg_id: this.msg_id,
      operation: m_direction, //电机转动方向 1:left 2:lright 3:up 4:down 5:reset
    });

    const newArrayBuffer = await strToArrayBuffer(['0', '1', messageJson]);
    if (newArrayBuffer) {
      const uinArray = new Uint8Array(newArrayBuffer);

      let string1 = '';
      for (let i = 0; i < uinArray.length; i++) {
        string1 += String.fromCharCode(uinArray[i]);
      }
      console.log(string1, 'm_direction++++++++++');
      //const decoder = new global.TextDecoder('utf-8');
      //const text = decoder.decode(newArrayBuffer);
      function stringToBytes(val) {
        const result = [];
        for (let i = 0; i < val.length; i++) {
          result.push(val.charCodeAt(i));
        }
        return result;
      }

      const result = stringToBytes(`02${messageJson}`);
      console.log(JSON.stringify(result));
      IMP2pClient?.sendP2pData('2', messageJson);
    }

    console.log(25304, this.msg_id, m_direction);
    this.msg_id += 1;
  }

  //判断当前是否可以操作
  _canStepIn() {
    if (this.state.isSleep) {
      showToast(stringsTo('power_off'));
      return false;
    }
    if (this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING) {
      return true;
    }
    // if (!this.state.isOnline) {
    //   showToast(stringsTo('device_offline'));
    //   return false;
    // }
   
    // if (!this.p2pStatusSuccess) {
    //   showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
    //   return false;
    // }
    showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
    return false;
  }

  /*控制云台转动*/
  _doDirectionPanorama(h, v) {
    if (!this._checkDirectionPanorama(h, v)) {
      ptz_pressedPanoramic = true;
      this._checkDirectionPanorama(h, v);
    }
  }

  /*判断当前是否可操作*/
  _checkDirectionPanorama(h, v) {
    console.log('_checkDirection m_direction=' + h + ',ptz_pressed=' + ptz_pressed);

    if (ptz_pressedPanoramic) {
      this._setPanoramaRotateAngle(h, v);
    }

    return ptz_pressedPanoramic;
  }
  _setPanoramaRotateAngle(h, v) {
    let paramsJSON = {position: '[' + h + ',' + v + ']'};

    LetDevice.sendDeviceServerRequest('PanoramSlide', JSON.stringify(paramsJSON))
      .then(data => {
        console.log(' 控制云台 转动-------' + data);
      })
      .catch(error => {
        console.log('sendDeviceServerRequest error ' + error);
      });
  }

  /*监听直播流播放状态*/
  _onLivePlayerStatusChangeListener(status) {
    if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
      this.IMIVideoView.start();
    } else if (
      this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR &&
      status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE
    ) {
      //目前发现IOS ERROR后还会调用PAUSE，所以ERROR和暂停View重叠
      return;
    }
    this.setState({currentStatus: status});
  }

  handleOpen = () => {
    if (!this.state.sleepPosition) {
      if (!this._canStepIn()) {
        return;
      }
      this.setState({
        showTips: true
      })
    } else {
      showLoading(stringsTo('commWaitText'), true);
      this.setState({
        showTips: false
      })
      const params = {value: !this.state.sleepPosition};
      const paramJson = JSON.stringify(params);
      LetDevice.setProperties(true, LetDevice.deviceID, '10008', paramJson)
      .then(res => {
        console.log('固定位置休眠', res);
        this.IMIVideoView && this.IMIVideoView.prepare()
        showToast(I18n.t('action_success'));
        showLoading(false);
        this.setState({
            sleepPosition: !this.state.sleepPosition,
        });
      }).catch(err => {
        console.log('固定位置休眠', JSON.stringify(err));
        showLoading(false);
        showToast(I18n.t('action_fail'));
      });
    }
    
  }

   //点击截屏按钮
   _onPressScreenShot = () => {
    const cameraId=this.cameraNumber=='1'?0:1
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_Sleep_${new Date().getTime()}.jpg`;
            this.IMIVideoView?.snap(cameraId,this.currentSnapshotPath);
          } else if (status2 === -1) {
            showLoading(false);
            showToast(stringsTo('storage_permission_denied'));
          } else {
            showLoading(false);
          }
        });
      } else if (status === -1) {
        showLoading(false);
        showToast(stringsTo('storage_permission_denied'));
      } else {
        showLoading(false);
      }
    });
  };

  // 截图成功回调
  _onCommCallback = event => {
    console.log('------截图成功-----保存截图成功回调', event);
    console.log('=======', this.currentSnapshotPath);
    const params = {value: !this.state.sleepPosition};
    const paramJson = JSON.stringify(params);
    LetDevice.setProperties(true, LetDevice.deviceID, '10008', paramJson)
    .then(res => {
      console.log('固定位置休眠', res);
      showToast(I18n.t('action_success'));
      IMIStorage.save({
        key: LetDevice.deviceID + 'positionSleep',
        data: {
          path: this.currentSnapshotPath,
        },
        expires: null,
      });
      showLoading(false);
      this.IMIVideoView && this.IMIVideoView.stop()
      this.setState({
         sleepPosition: !this.state.sleepPosition,
         currentSnapshotPath: this.currentSnapshotPath
      });
      })
      .catch(err => {
        console.log('固定位置休眠', JSON.stringify(err));
        showLoading(false);
        showToast(I18n.t('action_fail'));
      });
  };

  //休眠提示
  _sleepView() {
    if (!this.state.isSleep) {
      return null;
    }
    let videoWidth = SCREEN_WIDTH - 14 * 2;
    let videoHeight = videoWidth * 9 / 16;
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: videoWidth,
          height: videoHeight,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#000',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={stringsTo('power_off')}
        />
      </View>
    );
  }

  render() {
    let videoWidth = SCREEN_WIDTH - 14 * 2;
    let videoHeight = videoWidth * 9 / 16;
    return (
      <View style={{flex: 1, backgroundColor: '#F2F3F5'}}>
        <NavigationBar
          title={stringsTo('Preset_Sleep')}
          subtitle={this.state.sleepPosition ? stringsTo('preset_opened') : stringsTo('preset_closed')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this.props.navigation.pop()}]}
          right={[]}
        />
        <Separator />
        <XText
          style={{marginTop: 24, marginLeft: 14, fontSize: 12, color: '#000'}}
          text={stringsTo('Preset_Sleep_subTitle')}
        />
        <View style={{marginTop: 12, justifyContent: 'center', alignItems: 'center', display: !this.state.sleepPosition ? '' : 'none'}}>
          <TinyWindowLivePlayer
            style={{marginLeft: 14}}
            {...this.props}
            onCommCallback={this._onCommCallback}
            videoRef={ref => (this.IMIVideoView = ref)}
            onLivePlayerStatusChange={status => this._onLivePlayerStatusChangeListener(status)}
            playerMarginHorizontal={14}
          />
          {this._sleepView()}
        </View>
        {this.state.sleepPosition && <View style={{marginTop: 12, justifyContent: 'center', alignItems: 'center'}}>
          {this.state.currentSnapshotPath && <Image style={{width: videoWidth, height: videoHeight, backgroundColor: '#cccc'}} 
            source={{uri:'file://' + this.state.currentSnapshotPath}}
          />}
          {/* 兼容ios */}
          {!this.state.currentSnapshotPath && <View style={{width: videoWidth, height: videoHeight, backgroundColor: '#cccc'}}></View>}
        </View>}

        {this.renderPtzControlView()}
        <XView
          style={{
            marginRight: 14,
            marginLeft: 14,
            height: 45,
            alignItems: 'center',
            borderRadius: 22.5,
            backgroundColor: this.state.sleepPosition ? '#EEEEEE' : 'rgba(73, 110, 224, 0.5)',
            justifyContent: 'center',
          }}
          onPress={this.handleOpen}>
          <XText
            style={{fontSize: 15, textAlign: 'center'}}
            text={this.state.sleepPosition ? stringsTo('closeStr') : stringsTo('settings_switch_on')}
          />
        </XView>

        {!this.state.fullScreen ? <RNLine style={{marginTop: 14, height: 1}} /> : null}
        {this.state.showTips && <MessageDialog
          title={null}
          showTitle={false}
          message={stringsTo('save_current_angle_sleep')}
          messageStyle={{marginTop: 30, paddingLeft: 10, paddingRight: 10}}
          visible={this.state.showTips}
          canDismiss={true}
          onDismiss={() => {
            this.setState({
              showTips: false
            })
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelSaveAngerSleep',
              callback: _ => {
                this.setState({
                  showTips: false
                })
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okSaveAngerSleep',
              callback: _ => {
                showLoading(stringsTo('commWaitText'), true);
                this._onPressScreenShot();
                this.setState({
                  showTips: false
                })
              },
            },
          ]}
        />}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  bottomLayout: {
    display: 'flex',
    width: '100%',
    height: 80,
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
  },
  bottomLayoutItem: {
    flex: 1,
    height: '100%',
    marginTop: 15,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
