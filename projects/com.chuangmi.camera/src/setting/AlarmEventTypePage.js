
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, View} from 'react-native';


import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import React from 'react';
import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import {stringsTo} from '../../../../globalization';
import {SOUND_AND_LIGHT_WARNING} from '../../constants/Spec';
import { BaseDeviceComponent, LetDevice } from '../../../../imilab-rn-sdk';
import { isAndroid } from '../../../../imilab-rn-sdk/utils/Utils';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';

const TAG = 'AlarmEventTypePage';

export default class AlarmEventTypePage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      eventTypes: [],
      triggerTypes: this.props.route.params.triggerTypes,
    };
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentDidMount() {
    this._initData(this.props.route.params.triggerTypes);
  }

  onBackHandler = () => {
    console.log('onBackAndroid  navigation.isFocused() ' + navigation.isFocused());
    if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    } else {
    }
    return false;
  };

  _onPressBack = () => {
    this.props.navigation.pop();
  };

  _initData(triggerTypes) {
    const eventTypes = [
      {title: stringsTo('picture_change'), value: triggerTypes.includes(2), id: 2},
      {title: stringsTo('people_move'), value: triggerTypes.includes(5), id: 5},
      {title: stringsTo('detect_car'), value: triggerTypes.includes(9), id: 9},
      {title: stringsTo('detect_non_vehicle'), value: triggerTypes.includes(10), id: 10},
      {title: stringsTo('someone_break_into_key_area'), value: triggerTypes.includes(16), id: 16},
    ];
    this.setState({eventTypes});
  }

  _updateTriggerType(id, isOpen) {
    console.log(TAG, `_updateTriggerType: id: ${id}, isOpen: ${isOpen}`);
    let triggerTypes = this.state.triggerTypes;
    if (isOpen) {
      if (!triggerTypes.includes(id)) {
        triggerTypes.push(id);
      }
    } else {
      if (triggerTypes.includes(id)) {
        triggerTypes = triggerTypes.filter(item => {
          return item !== id;
        });
      }
    }
    this.setState({triggerTypes}, () => {
      this._initData(triggerTypes);
    });
    const piid = SOUND_AND_LIGHT_WARNING.TRIGGER_EVENT.PIID;
    LetDevice.setProperties(
      true,
      LetDevice.deviceID,
      piid,
      JSON.stringify({
        msg_id: piid,
        value: triggerTypes,
      }),
      true,
    )
      .then(() => {
        console.log(TAG, `update success: piid: ${piid}, value: ${triggerTypes}`);
      })
      .catch(error => {
        console.log(TAG, `update failed: piid: ${piid}, value: ${triggerTypes}, error: `, error);
      });
  }

  render() {
    let {showSynchronous056} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
    // const params = navigation.state.params.props;
    global.navigation = this.props.navigation;
    return (
      <View style={styles.container}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={stringsTo('effect_event_type')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          {this.state.eventTypes.map(item => {
            return (
              <ListItmeWithSwitch
                title={item.title}
                value={item.value}
                key={item.id}
                onValueChange={value => {
                  this._updateTriggerType(item.id, value);
                }}
              />
            );
          })}
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
});
