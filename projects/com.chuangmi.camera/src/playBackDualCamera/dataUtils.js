window.allBackListDetail = {};
window.allBackList = {}
window.timeLineLists = [];

function getAllBackList() {
    return window.allBackList
}

function getAllBackListDetail() {
    return window.allBackListDetail
}

function changeAllBackList(allBackList) {
    window.allBackList = allBackList
}

function changeAllBackListDetail(allBackListDetail) {
    window.allBackListDetail = allBackListDetail;
}

function getTimeLineLists() {
    return window.timeLineLists
}

function changeTimeLineLists(timeLineLists) {
    window.timeLineLists = timeLineLists
}

function deleteContent(date, time, delTime) {
    window.allBackList[date] = window.allBackList[date].filter(item => String(item.timestamp) !== String(delTime));
    window.allBackListDetail[date][time] = window.allBackListDetail[date][time].filter(item => String(item.timestamp) !== String(delTime));
    window.timeLineLists = window.timeLineLists.filter(item => String(item.timestamp) !== String(delTime));
    if (!window.allBackListDetail[date][time] || window.allBackListDetail[date][time].length == 0) {
        delete window.allBackListDetail[date][time];
        if (Object.keys(window.allBackListDetail[date]).length === 0) {
            delete window.allBackListDetail[date]
        }
    }
}

export {
    getAllBackList,
    getAllBackListDetail,
    changeAllBackList,
    changeAllBackListDetail,
    getTimeLineLists,
    changeTimeLineLists,
    deleteContent
}