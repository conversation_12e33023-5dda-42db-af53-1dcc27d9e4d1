/*
 * 作者：sunhongda
 * 文件：IPC031MainPage.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
import React from 'react';

import {StyleSheet, TouchableOpacity, View,Text,Image, SafeAreaView, Dimensions, ScrollView,FlatList} from "react-native";

import {XImage, XText, XView} from "react-native-easy-app";

import {
    RNLine,
    colors,
    imiThemeManager,
    RoundedButtonView,
    showLoading, showToast
} from '../../../../imilab-design-ui';

import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";

import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {
    LetDevice,
    IMIGotoPage,
    BaseDeviceComponent,
    IMIStorageBean,imiAlarmEventCloudApi
} from '../../../../imilab-rn-sdk';

import Orientation from "react-native-orientation";
import I18n, {stringsTo} from "../../../../globalization/Localize";
import {IMIStorage} from "../../../../imilab-rn-sdk";
const screen_width = Dimensions.get('window').width;

export default class HourSDCardPage extends BaseDeviceComponent {

    constructor(props) {
        super(props);
        this.state = {
            fullScreen: Orientation.getInitialOrientation() !== 'PORTRAIT',
            isEdit:false,
            numAry:[],
            isSelectAll:false,
            vipState:0,
            alarmDialog:false,
            length: 0,
            playData:[{text:"123",index:0},{text:"456",index:1},{text:"789",index:2},{text:"hhhh",index:3},{text:"6666",index:4}],
        };
    }

    componentWillUnmount(){
        this._subscribe_focus && this._subscribe_focus();
    }

    componentDidMount(){

        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            // this.alarmListPlayerComponent._getAlarmListData();
        });
        showLoading(true)
        imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
            let data = JSON.parse(res);
            showLoading(true)
            this.setState({vipState:data.state});
        }).catch(error=>{
            showLoading(true);
            // showToast(JSON.stringify(error));
        });
        IMIStorage.load({
            key: LetDevice.deviceID+'alarmDialog',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({alarmDialog: res.alarmDialog});
        }).catch(_=> {
            this.setState({alarmDialog: true});
        });

        // LetDevice.updateAllPropertyCloud().then((data) => {
        //     showLoading(false);
        //     let dataObject = JSON.parse(data);
        //     //侦测时间
        //     if (dataObject.AlarmSwitch) {
        //         if (!dataObject.AlarmSwitch.value){
        //             this.setState({alarmDialog: true});
        //         }
        //     }
        //     console.log('statprops--'+ JSON.stringify(data));
        // }).catch(error => {
        //     console.log(JSON.stringify(error))
        // });



    }


    render() {
        return (<>
            <NavigationBar
                type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                title={stringsTo('play_back_tit')}
                left={[{key: NavigationBar.ICON.BACK, onPress: () => {
                        this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
                    }}]}
                right={[{
                    key: NavigationBar.ICON.CUSTOM,
                    n_source:require('../../resources/images/icon_edit.png'),
                    onPress: _ => {
                        this.setState({isEdit:true,numAry:[],isSelectAll:false})
                        // if (this.state.length) this.setState({isEdit:true,numAry:[],isSelectAll:false});
                    }
                }]}
            />

            {/*{!this.state.fullScreen ?*/}
            {/*    <RNLine style={{height: 1}}/>*/}
            {/*    :*/}
            {/*    null}*/}

            {this._renderDayFiles()}
            {/*<View style={{top:20,height:14,backgroundColor: "#F1F1F1"}} />*/}
            {/*编辑选择头部*/}
            {this._renderSelectView()}
            {this._renderBottomDeleteView()}
            {this._renderAlarmDialog()}

        </>)
    }

    //底部删除
    _renderBottomDeleteView() {
        if (!this.state.isEdit){
            return null
        }
        return (
            <XView style = {{width:'100%',height:64,position:'absolute',bottom:0,backgroundColor:'#FFFFFF',justifyContent:'center',alignItems:'center'}} onPress={() => {
                console.log('点击删除')
            }}>
                <RNLine style={{height: 1}}/>
                <XImage style={{ width: 30, height: 30,marginTop:4,justifyContent:'center'}}
                        source={require('../../resources/images/play_back_delete.png')}></XImage>
                <XText style={{width:'100%',lineHeight:17,fontSize:12,color:'#E74D4D',textAlign: 'center',}} text={stringsTo('delete_title')}/>
            </XView>
        )
    }

    _renderDayFiles() {
        // if (this.state.isEmpty || this.state.isCurrentDayEmpty) {
        //     return null;
        // }
        return (
            <FlatList
                style={{ flexGrow: 1,marginTop:4,backgroundColor:'purple' }}
                data={this.state.playData}
                renderItem={({ item, index }) => this._renderDayItem(item, index)}
                numColumns={3}
                keyExtractor={(item, index) => index.toString()}
                ListFooterComponent={<View style={{ height: 30,backgroundColor:'red'}}></View>}
            />
        )
    }

    _renderDayItem(item,index) {
        //let screenWidth = Dimensions.get('window').width;
        // let screenWidth = Dimensions.get('window').width>Dimensions.get('window').height ? Dimensions.get('window').height : Dimensions.get('window').width;

        let containerWidth = (screen_width-14*4) / 3
        let containerHeight = containerWidth * 0.8;
        //  86/108 = 0.8


        return (
            <TouchableOpacity
                onPress={() => {console.log(item.index)}}
            >
                <View style = {{ width: containerWidth,marginLeft:14,marginBottom:6,height: containerHeight,backgroundColor: item.index == 1 ? 'green':'orange',}}>
                    <View style = {{width: "100%",marginTop:6,height: containerHeight-6*2-17,position: "relative",backgroundColor:'#F9F9F9'}}>
                        <Image style={{ width: "100%",height: "100%",borderRadius: 4 }}
                               source={require('../../resources/images/full_color_img.png')}
                        >
                        </Image>

                        {this.state.isEdit ? <Image
                            style={{ width: 20, height: 20, position: "absolute", bottom: 5, right: 5 }}
                            source={require("../../resources/images/icon_select_s.png")}
                        /> : null}


                    </View>

                    <View style={{backgroundColor:'yellow'}}>
                        <Text style={{ marginTop: 6, width:containerWidth,lineHeight:17, fontSize: 12, color: "#333333",textAlign:'left',backgroundColor:'green'}}
                        >{'12:00'}
                        </Text>
                    </View>

                </View>
            </TouchableOpacity>
        )
    }

    /**
     * 全选-全不选 操作框
     */

    _renderSelectView(){
        if (this.state.isEdit){
            return(<XView style={{position: "absolute",height: 52,top:20,backgroundColor:'#FFF',width:'100%', alignItems: 'center',justifyContent: 'space-between',flexDirection: 'row'}}>
                <XText style={[styles.barText,{width:80}]} text={this.state.isSelectAll?stringsTo('unselect_all'):stringsTo('select_all')} onPress={()=>{
                    this.setState({isSelectAll:!this.state.isSelectAll,numAry:[]});
                }}/>
                <XText style={[styles.barText,{fontWeight:'bold'}]} text={I18n.t('select_title_3', {code: this.state.numAry.length})}/>
                <XText style={[styles.barText,{width:80}]} text={stringsTo('cancel')} onPress={()=>this.setState({isEdit:false})}/>
            </XView>)
        }
        return null;
    }
    /**
     * 底部tab 工具栏
     * @private
     */
    _renderTabViewTool() {
        return (
            <View style={{
                height: 50,
                backgroundColor: colors.white,
                flexDirection: 'row',
                justifyContent: 'space-between',
                padding: 14,
            }}>

                {/* Tab 增加后可通过集合形式进行动态决定添加哪一条 */}
                <XText style={[styles.tab, {color: imiThemeManager.theme.primaryColor}]}
                       text={stringsTo('alarmText')} onPress={() => {
                }}/>

                <RNLine style={{width: 1}} vertical={true}/>

                <XText style={[styles.tab, {}]} text={stringsTo('goto_live_view')}
                       onPress={() => {
                           IMIGotoPage.starNativeCameraPlayerPage(LetDevice.deviceID)
                       }}/>
            </View>);
    }

    _renderAlarmDialog(){
        return (<AlertDialog
                title={stringsTo('str_housekeeping_tip_title')}
                visible={this.state.alarmDialog}
                resource={require("../../resources/images/police_pic_empty.png")}
                message={stringsTo('str_housekeeping_tip_value')}
                messageStyle={{marginVertical:5,fontSize:12,fontWeight:'500',color:"#7F7F7F",textAlign:"left",}}
                titleStyle={{fontSize:14}}
                canDismiss={true}
                onDismiss={()=>{
                    this.setState({alarmDialog:false},()=>{
                        IMIStorage.save({
                            key: LetDevice.deviceID+'alarmDialog',
                            data: {
                                alarmDialog: false
                            },
                            expires: null,
                        });
                    });
                }}

                buttons={[
                    {
                        text: stringsTo("cancel"),
                        callback: _ => {
                            this.setState({alarmDialog:false},()=>{
                                IMIStorage.save({
                                    key: LetDevice.deviceID+'alarmDialog',
                                    data: {
                                        alarmDialog: false
                                    },
                                    expires: null,
                                });
                            });
                        }
                    },
                    {
                        text: stringsTo('go_to_open'),
                        callback: _ => {
                            this.setState({alarmDialog:false},()=>{
                                IMIStorage.save({
                                    key: LetDevice.deviceID+'alarmDialog',
                                    data: {
                                        alarmDialog: false
                                    },
                                    expires: null,
                                });
                                // this.props.navigation.push("HouseKeepSetting");
                                if (LetDevice.model == "a1FKrifIRwH" || LetDevice.model == "a1Ikkj5vsiK" || LetDevice.model == "a1znn6t1et8"){
                                    // 026 021E01 036 进入新的看家界面 原生项目转RN
                                    this.props.navigation.push("HouseKeepOldNativeSetting");
                                }else {
                                    this.props.navigation.push("HouseKeepSetting");
                                }
                            });

                        }
                    },
                ]}
            />
        );
    }

}

const styles = StyleSheet.create({

    tab: {
        flex: 1,
        textAlign: 'center',
        color: colors.gray,
        fontSize: 15,
        textShadowColor: imiThemeManager.theme.primaryColor,
    },
    videoPlayer: {
        position: 'absolute',
        top: 44,
        left: 0,
        bottom: 0,
        right: 0,
    },
    barText:{
        color:'#333333',
        fontSize:17,
        textAlign: 'center'
    },

});
