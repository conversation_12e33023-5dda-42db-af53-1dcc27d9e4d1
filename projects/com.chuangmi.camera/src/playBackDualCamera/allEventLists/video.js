import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Dimensions, ActivityIndicator, Text, BackHandler, Image, TouchableOpacity, StatusBar} from 'react-native';
import {XText, XImage, XView} from 'react-native-easy-app';
import IMIFile from '../../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import IMIPermission from '../../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import IMIToast from '../../../../../imilab-design-ui/src/widgets/IMIToast';
import {colors, IMIImageView, showLoading, showToast, imiThemeManager, RoundedButtonView, MessageDialog} from '../../../../../imilab-design-ui';
import Orientation from 'react-native-orientation';
import {aliAlarmEventCloudApi,IMILog,LetDevice} from '../../../../../imilab-rn-sdk';
import I18n, {stringsTo} from '../../../../../globalization/Localize';
import NavigationBar from '../../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import { ScrollView } from 'react-native-gesture-handler';
import IMP2pClient from '../../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import IMVodPlayView from '../../../../../imilab-rn-sdk/native/camera-kit/IMVodPlayView';
import IMICameraVideoView, {
  CAMERA_PLAYER_MODE,
} from '../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView';
import DeviceTemplatesUtils from '../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import {PLAYER_EVENT_CODE} from '../../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView';
import VideoProgressView from '../../cloudStorage/videoProgressView/VideoProgressView';
import PlayBackToolBarView from '../../../../../imi-rn-commonView/PlayerToolBarView/PlayBackToolBarView';
import ImageButton from '../../../../../imi-rn-commonView/ImageButton/ImageButton';
import PlayBackFullScreenToolBarView from '../../../../../imi-rn-commonView/PlayerToolBarView/PlayBackFullScreenToolBarView';
import {isAndroid, isPhoneX, isIos, isIphoneXSeries, isIphone14ProMax} from '../../../../../imilab-rn-sdk/utils/Utils';
import {IMINativeLifeCycleEvent} from '../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import {
  byteArrayToInt4,
  byteArrayToLong8,
} from '../../utils/GenericUtils';
import { getAllBackListDetail, deleteContent } from '../dataUtils'
import moment from 'moment';
const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const speedTitleAry = ['1X', '4X', '8X', '16X'];
const speedAccessibilityLabelTitleAry = [
  'play_back_clarity_show_1X',
  'play_back_clarity_show_2X',
  'play_back_clarity_show_4X',
  'play_back_clarity_show_/8X',
  'play_back_clarity_show_16X',
];

let isCheckingPermission = false;

let StatusBarHeight = 70;
export default class BackTimeLists extends React.Component {
  constructor(props) {
    super(props);
    const {needCheck, timeList, dataList, initCurrent, localPicList} = this.getData()
    let {timestamp, duration} = dataList[initCurrent];
    let time = moment(timestamp * 1000).format('HH:mm');
    const endTime = moment(timestamp * 1000 + duration).format('HH:mm');
    const leftText = moment(timestamp * 1000).format('mm:ss');
    const rightText = moment(timestamp * 1000 + duration).format('mm:ss');
    this.state = {
        dataList: dataList,
        title: `${time}-${endTime}`,
        leftText: leftText,
        rightText: rightText,
        isEdit: false,
        numAry: [],
        isSelectedAll: false,
        showDeleteTip: false,
        start_time: timestamp,
        offset: 0,
        currentOffset: 0,
        isPlayFromBeginning: moment(timestamp * 1000).format('mm:ss'),
        duration: parseInt(duration / 1000) ,
        end_time: parseInt((timestamp * 1000 + duration) / 1000),
        mute: true,
        timeList: timeList,
        isLoading: true,
        speed: 0,
        timeListData: JSON.parse(JSON.stringify(props.route.params.timeListDataNew)),
        isFullScreen: false,
        recording: false,
        snapshotVisible: false, //截图是否显示中
        screenShotPath: null,
        screenShotPathType: 0,
        isPlay: false, //是否播放中 true为播放中
        recordDuration: 0, //录屏时长,小于6秒提示失败
        showFullScreenTools: false,
        isShowZoomScale: false, //展示缩放比例小窗
        zoomScale: 1.0,
        showErrorView: false,
        errorFind: false,
    };
    this.needCheck = needCheck;
    this.localPic = localPicList;
    this.currentIndex = 0;
    this.isForegroundPage = true;
    this.isDragStatus = false;
    this.downloadFlag = false;
    this.currentVideoLength = 0;
    this.currentImgLength = 0;
    this.preSeq = 0;
    this.isView = false;
    this.firstLoad = true;
    this.getStatusBarHeight();
    this.isVideoFinish = false;
    this.reconnectFlag = false;
  }

  getData() {
    let initCurrent = 0;
    const needCheck = [];
    const localPicList = {};
    const timeList = {};
    const data = getAllBackListDetail();
    const dataList = data[this.props.route.params.firstKey] && data[this.props.route.params.firstKey][this.props.route.params.secondKey] || [];
    const uniqueDataList = Array.isArray(dataList) && dataList.filter((item, index, self) => 
      index === self.findIndex((t) => t.timestamp === item.timestamp)
    ) || [];
    Array.isArray(uniqueDataList) && uniqueDataList.map((res, index) => {
      const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${res.camera_id}_${res.timestamp}.jpg`
      needCheck.push(saveFilePath);
      localPicList[res.timestamp] = res.pic_loc;
      timeList[res.timestamp] = saveFilePath;
      if (res.timestamp === this.props.route.params.currentTimestamp) {
        initCurrent = index
      }
    })
    return {needCheck, timeList, dataList, initCurrent, localPicList}
  }

  loadMoreData() {
    if (this.state.dataList[0].timestamp > (new Date(moment(new Date().getTime()).format('yyyy-MM-DD HH') + ":00").getTime() / 1000)) {
      this.loadMoreTime = setInterval(() => {
        const {needCheck, timeList, dataList, localPicList} = this.getData();
        this.setState({
          dataList: dataList,
          timeList: timeList,
          timeListAll: timeList,
        })
        this.needCheck=needCheck;
        this.localPic=localPicList;
        this.loadImg(this.needCheck)
      }, 60000)
    }
  }

  loadImg(needCheck, back) {
    IMP2pClient.checkPlaybackFile(needCheck).then(res => {
      const lists = [];
      const timeList = back ? JSON.parse(JSON.stringify(this.state.timeListAll)) : this.state.timeList;
      Array.isArray(res) && res.map(item => {
          const timestamp = item.split('_').slice(-1)[0].slice(0, -4)
          lists.push(timestamp)
          delete timeList[timestamp]
      })
      this.setState({
          timeListData: lists,
          timeListDataNew: JSON.parse(JSON.stringify(lists)),
          timeList,
      }, () => {
        lists.length > 0 && this.sendData(0);
      })
  }).catch(error => {
  })
  }

  componentDidMount() {
    this.firstLoad = true;
    this.loadMoreData();
    this.loadImg(this.needCheck)
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', this.backAction);
    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.isForegroundPage = true;
      this.downloadFlag = false;
      if(this.firstLoad) {
        return
      }
      const {needCheck, timeList, dataList, localPicList} = this.getData();
      this.setState({
        dataList: dataList,
        timeList: timeList,
        timeListAll: timeList,
        showPauseView: false, 
        isLoading: this.IMIVideoView ? true : false, 
        offset: this.currentOffset
      }, () => {
        setTimeout(() => {
          this.IMIVideoView?.prepare();
          switch (this.state.speed) {
            case 0:
              this.IMIVideoView && this.IMIVideoView.speed(1);
              break;
            case 1:
              this.IMIVideoView && this.IMIVideoView.speed(4);
              break;
            case 2:
              this.IMIVideoView && this.IMIVideoView.speed(8);
              break;
            case 3:
              this.IMIVideoView && this.IMIVideoView.speed(16);
              break;
          }
        })
      })
      this.needCheck=needCheck;
      this.localPic=localPicList;
      this.loadImg(this.needCheck)
    });
    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      this.isForegroundPage = false;
      this.IMIVideoView && this.IMIVideoView?.stop();
      this.waitingData && clearInterval(this.waitingData);
      this.loadMoreTime && clearInterval(this.loadMoreTime)
    });
    this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
      if (this.state.isPlay && !isCheckingPermission) {
        this.IMIVideoView && this.IMIVideoView?.stop();
      }
    })

    this.currentIndex = 0;
    this.onP2pSendStateListener = IMP2pClient.onFileOperateAddListener(e => {
      if (!this.isForegroundPage) {
        return
      }
      if (e.iotId != LetDevice.deviceID) {
        return
      }
      this.waitingData && clearInterval(this.waitingData);
      if (e.code === 0 && e.data) {
          this.queryThreeMonthData(e.data)
        }
    });
  }

  getStatusBarHeight() {
    if (isIos()) {
      StatusBarHeight = isIphone14ProMax() ? 59 + 50 : isIphoneXSeries() ? 47 + 50 : 20 + 50;
    } else {
      StatusBarHeight = parseInt(StatusBar.currentHeight) + 50;
    }
  }

  getStatusBarHeightNew() {
    if (isIos()) {
      return isIphone14ProMax() ? 59 + 50 : isIphoneXSeries() ? 47 + 50 : 20 + 50;
    } else {
      return parseInt(StatusBar.currentHeight) + 50;
    }
  }

  uint8ArrayToBase64(uint8Array) {
    const rawData = new Uint8Array(uint8Array);
    const base64String = btoa(String.fromCharCode.apply(null, rawData));
    return base64String;
  }

  queryThreeMonthData(data) {
    const rawData = window.atob(data);
    const uint8Arraypart = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; i++) {
        uint8Arraypart[i] = rawData.charCodeAt(i);
    };
    
    if (byteArrayToInt4(uint8Arraypart, 4) == 0) {
      return;
    }
    if (byteArrayToInt4(uint8Arraypart, 4) == 2) {
        return;
    }
    if (byteArrayToInt4(uint8Arraypart, 4) == 1) {
      this.downloadTime && clearTimeout(this.downloadTime);
      this.downloadVideo(data)
      return;
    }
    // 删除的时候有轮询掉用删除状态
    if (this.downloadFlag) {
      try {
        // 删除
        const messageResult = JSON.parse(rawData?.slice(4))
        if (messageResult.thingid == 214) {
          // -1：删除失败，0删除中，1：删除成功
          if (messageResult.del_status == 1) {
            this.deleteSuccess();
          } else if (messageResult.del_status == -1) {
            this.downloadFlag = false;
            showLoading(false);
            showToast(stringsTo('delete_failed'));
            this.deleteTime && clearInterval(this.deleteTime);
          }
        }
      } catch (error) {
        console.log(error)
      }
    }
    if (byteArrayToInt4(uint8Arraypart, 4) !== 3) {
        try {
          const messageResult = JSON.parse(rawData.slice(4, -1));
          if (messageResult.cmd_type === 2) {
            if (messageResult.code === 1) {
              this.deleteSuccess();
            } else {
              this.downloadFlag = false;
              showLoading(false);
              showToast(stringsTo('delete_failed'));
              this.deleteTime && clearInterval(this.deleteTime);
            }
          }
          if (messageResult.cmd_type === -1 && messageResult.code === 1000) {
            IMILog.logI('视频播放结束', JSON.stringify({duration: this.state.duration, currentOffset: this.currentOffset}));
            this.progressView && this.progressView.onSliderValueChanged(this.state.duration);
            this.isVideoFinish = true;
            this.currentOffset = 0;
            setTimeout(() => {
              this.progressView && this.progressView.resetStatus();
            }, 500)
          }
          if (messageResult.cmd_type === 1 && messageResult.code === 1011) {
            this.downloadTime && clearTimeout(this.downloadTime);
            this.downloading && clearTimeout(this.downloading);
            // 下载异常结束，超时保持一致
            console.log('视频异常结束-------------')
            IMILog.logI('视频异常结束', JSON.stringify({preSeq: this.preSeq}));
            if (this.preSeq === 0) {
              this.downloadFlag = false;
              this.preSeq = 0;
              showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
              showLoading(false);
            }
            this.saveFile('', `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, '0')
          }

          // 获取视频文件索引失败
          if (messageResult.cmd_type === -1 && (messageResult.code === 1001 || messageResult.code === 1004 || messageResult.code === 1011)) {
            this.setState({
              errorFind: true,
            })
          }
        } catch (error) {
          console.log(error)
        }
        return;
    }
    const uint8Array = uint8Arraypart;
    const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
    const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
    const cur_pack_len = byteArrayToLong8(uint8Array, 47); // 开始时间utc , 单位s
    const total_file_len = byteArrayToInt4(uint8Array, 55);
    this.currentIndex = this.currentIndex + 1;
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${camera_id}_${timestamp}.jpg`;

    let imgFinish = false;
    this.currentImgLength += uint8Array.slice(84).length;
    if (this.currentImgLength >= total_file_len) {
      imgFinish = true
    }
    IMP2pClient.downloadPlaybackImage(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath, imgFinish ? '0' : '-1').then(res => {
        const timeList = this.state.timeList || {};
        timeList[timestamp] = res;
        this.setState({
            timeList
        })
    }).catch(e => {
        console.log(11, e);
    });
    if (this.currentIndex < this.state.timeListData.length && this.isForegroundPage) {
        this.sendData(this.currentIndex)
    }
  }

  sendData(index) {
    if (!this.isForegroundPage) {
      this.waitingData && clearInterval(this.waitingData);
      return
    }
    this.currentImgLength = 0;
    this.state.timeListData[index] && IMP2pClient.operationFile('3', [String(this.state.timeListData[index])], [String(this.localPic[this.state.timeListData[index]])]);
    // 十秒后未获取到数据就重发
    this.waitingData = setInterval(() => {
        this.currentIndex++
        this.currentImgLength = 0;
        this.state.timeListData[this.currentIndex] && IMP2pClient.operationFile('3', [String(this.state.timeListData[this.currentIndex])], [String(this.localPic[this.state.timeListData[this.currentIndex]])]);
    }, 10000)
  }

  componentWillUnmount() {
    try {
      this.isView = false;
      this.onP2pSendStateListener && this.onP2pSendStateListener.remove();
      this.IMIVideoView && this.IMIVideoView.destroy();
      this.waitingData && clearInterval(this.waitingData);
      this.loadMoreTime && clearInterval(this.loadMoreTime);
      this.downloadTime && clearTimeout(this.downloadTime);
      this.deleteTime && clearInterval(this.deleteTime);
      this.backHandler && this.backHandler.remove()
    } catch (error) {
      console.log('卸载失败2')
    }
  }
  // 下载
  _downloadPress = () => {
    if (this.downloadFlag) {
      console.log('拦截重复点击')
      return
    }
    this.downloadFlag = true;
    //需要检测权限
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            showLoading(stringsTo('alarm_download_downloading'), true);
            this.isfinish = false;
            this.currentVideoLength = 0;
            IMP2pClient.operationFile('1', [String(this.state.numAry[0])], [String(this.localPic[this.state.numAry[0]])]);
            this.setState({isEdit: false, numAry: []});
            this.downloadTime = setTimeout(() => {
              this.downloadFlag = false;
              IMIToast.showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
              showLoading(false);
            }, 10000)
          } else if (status2 === -1) {
            this.downloadFlag = false;
            showToast(stringsTo('storage_permission_denied'));
          }
        });
      } else if (status === -1) {
        this.downloadFlag = false;
        showToast(stringsTo('storage_permission_denied'));
      }
    });
  };

  downloadVideo(data) {
    const rawData = window.atob(data);
    const uint8Arraypart = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; i++) {
        uint8Arraypart[i] = rawData.charCodeAt(i);
    };
    const uint8Array = uint8Arraypart;
    const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
    const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
    const cur_pack_len = byteArrayToInt4(uint8Array, 47);
    const seq = byteArrayToInt4(uint8Array, 51);
    const total_file_len = byteArrayToInt4(uint8Array, 55);
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/${camera_id}_${timestamp}.mp4`;
    console.log('===========', this.preSeq, cur_pack_len, seq, total_file_len, this.currentVideoLength);
    
    this.downloading && clearTimeout(this.downloading)

    if (this.isfinish) {
      return
    }

    this.currentVideoLength += uint8Array.slice(84).length;
    if (this.preSeq && Math.abs(this.preSeq - seq) > 1 || this.currentVideoLength >= total_file_len) {
      this.isfinish = true
    }
    if (!this.isfinish) {
      this.downloading = setTimeout(() => {
        console.log('超时直接保存-------------')
        IMILog.logI('视频超时20s直接保存', JSON.stringify({preSeq: this.preSeq, unit: '', saveFilePath: `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, isFinish: '0'}));
        this.saveFile('', `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, '0')
      }, 20000)
    }
    this.preSeq = seq;
    IMILog.logI('视频下载传递', JSON.stringify({isfinish: this.isfinish, cur_pack_len, total_file_len, saveFilePath, currentVideoLength: this.currentVideoLength}));
    this.saveFile(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath, this.isfinish ? '0' : '-1')
  }

  saveFile = (unit, saveFilePath, isFinish) => {
    IMP2pClient.downloadPlaybackVideo(unit, saveFilePath, isFinish).then(res => {
      console.log('===========', res)
           if (res !== '-1') {
            this.downloading && clearTimeout(this.downloading)
            this.downloadFlag = false;
            this.preSeq = 0;
            // 推送到相册
            IMIFile.saveVideoToPhotosAlbum(`${saveFilePath}`, LetDevice.deviceID)
              .then(_ => {
                IMIToast.showToast(stringsTo('download_system_album'), IMIToast.TYPE.BOTTOM);
                showLoading(false);
              })
              .catch(error => {
                showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
                showLoading(false);
              });
           }
    }).catch(e => {
        console.log(11, e);
        this.downloadFlag = false;
        this.preSeq = 0;
        this.downloading && clearTimeout(this.downloading)
        showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
        showLoading(false);
    });
  }

  // 返回
  _onPressBack = () => {
    if (this.state.isEdit) {
      this.setState({isEdit: false, numAry: []});
    } else if (this.state.isFullScreen) {
      this._exitFullScreen();
    } else {
      this.props.navigation.goBack();
    }
    return true;
  };

  backAction = () => {
    if (this.downloadFlag) {
      return true
    }
    this._onPressBack()
    return true
  }

  _renderListView() {
    let dataList = this.state.dataList || [];
    dataList.sort((a, b) => b.timestamp - a.timestamp);
    return (
      <ScrollView
        raw={true}
        data={dataList}
        contentContainerStyle={{paddingBottom: 30}}
      >
        <View style={{flex: 1, flexDirection: 'row', flexWrap: 'wrap', paddingBottom: this.state.isEdit && this.state.numAry && this.state.numAry.length > 0 ? 68 : 0, paddingLeft: 14, gap: 14}}>
          {Array.isArray(dataList) && dataList.map((item, index) => {
            return this._renderItem(item, index)
          })}
        </View>
      </ScrollView>
    );
  }

  // 缩放比例view
  renderZoomScaleView() {
    if (!this.state.isShowZoomScale) {
      return null;
    }
    return (
      <View
        style={{
          backgroundColor: '#00000099',
          borderRadius: 4,
          position: 'absolute',
          top: this.state.isFullScreen ? 60 : StatusBarHeight + 15,
          // bottom: this.state.isFullScreen ? 206 : 0,
          // left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
          left: this.state.isFullScreen ? 65 : 20,
          width: 50,
          height: 30,
          zIndex: 999,
        }}>
        <Text
          accessibilityLabel={'zoom'}
          numberOfLines={1}
          ellipsizeMode={'tail'}
          style={{lineHeight: 29, fontSize: 15, color: '#FFFFFF', textAlignVertical: 'center', textAlign: 'center'}}>
          {`x${this.state.zoomScale}`}
        </Text>
      </View>
    );
  }

  handleCurrent = (item) => {
    if (this.state.isEdit) {
      const numAry = this.state.numAry;
      if (this.state.numAry.includes(String(item.timestamp))) {
        numAry.splice(this.state.numAry.indexOf(String(item.timestamp)), 1)
      } else {
        if (numAry.length >= 50) {
          showToast(stringsTo('delete_failed_limit'));
          return
        }
        numAry.push(String(item.timestamp))
      }
      this.setState({
        numAry
      })
      return
    }

    let {timestamp, duration} = item;
    let time = moment(timestamp * 1000).format('HH:mm');
    const endTime = moment(timestamp * 1000 + duration).format('HH:mm');
    const leftText = moment(timestamp * 1000).format('mm:ss');
    const rightText = moment(timestamp * 1000 + duration).format('mm:ss');
    this.setState({
      title: `${time}-${endTime}`,
      start_time: timestamp,
      offset: 0,
      duration: parseInt(duration / 1000) ,
      end_time: parseInt((timestamp * 1000 + duration) / 1000),
      isLoading: true,
      leftText: leftText,
      rightText: rightText,
      showPauseView: false, 
      showErrorView: false
    }, () => {
      this.currentOffset = 0,
      setTimeout(() => {
        this.IMIVideoView?.prepare();
        // this.progressView && this.progressView.resetStatus();
        if (!this.IMIVideoView) {
          this.setState({isLoading: false});
        }
      })
    })
    // this.props.navigation.push('BackVideo', {
    //   data: this.state.dataList
    // });
  }

  handleSelect() {
    this.setState({isEdit: true, numAry: [], isSelectedAll: false});
  }

  _renderEditView() {
    if (this.state.isEdit && this.state.numAry && this.state.numAry.length > 0) {
      return (
        <View style={{position: 'absolute',bottom: 0, height: isIos() ? 100 : 64, width: '100%', backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
          <View style={{height: 2, backgroundColor: '#F1F1F1'}} />
          <View
            style={{
              height: 62,
              width: '100%',
              backgroundColor: '#FFFFFF',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <TouchableOpacity
              onPress={() => {
                if (this.state.numAry.length === 1 ) {
                  this._downloadPress()
                } else {
                  showToast(stringsTo('max_download_limit'))
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center', marginRight: 30}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}} source={require('../../../resources/images/icon_alarm_down.png')} />
              <Text
                style={{fontSize: 16}} //设计稿改动
              >
                {stringsTo('downLoadTitle')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                if (this.state.numAry.length > 0) {
                  this.setState({showDeleteTip: true});
                } else {
                  showToast(stringsTo('select_tip'));
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center'}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}} source={require('../../../resources/images/play_back_delete.png')} />
              <Text
                style={{fontSize: 16, color: '#E74D4D'}} //设计稿改动
              >
                {stringsTo('delete_title')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  }

  deletImageArray() {
    if (this.state.numAry.includes(String(this.state.start_time))) {
      showLoading(false);
      showToast(stringsTo('delete_failed_inPlay'));
      return;
    }
    if (this.state.numAry.length > 50) {
      showLoading(false);
      showToast(stringsTo('delete_failed_limit'));
      return;
    }
    if (this.downloadFlag) {
      console.log('拦截重复点击')
      return
    }
    this.downloadFlag = true;
    const pic_locals = this.state.numAry.map(res => {
      return String(this.localPic[res])
    })
    IMP2pClient.operationFile('2', this.state.numAry, pic_locals);
    // this.deleteTime = setTimeout(() => {
    //   this.downloadFlag = false;
    //   showLoading(false);
    //   showToast(stringsTo('delete_failed'));
    // }, this.state.numAry.length <= 3 ? 10000 : this.state.numAry.length * 3000)
    // 每隔两秒去问询一次删除进度
    this.deleteTime && clearInterval(this.deleteTime);
    this.deleteTime = setInterval(() => {
      IMP2pClient.getChannelState(data => {
        if (parseInt(data, 10) === 1) {
          IMP2pClient.operationFile('4', [], []);
        } else {
          // p2p通道断开
          this.downloadFlag = false;
          showLoading(false);
          showToast(stringsTo('delete_connect_failed'));
          this.deleteTime && clearInterval(this.deleteTime);
        }
      })
    }, 2000)
  }

  findClosest(arr, num) {
    let closestGreater = null;
    let closestSmaller = null;
    let minDiffGreater = Infinity;
    let minDiffSmaller = Infinity;

    arr.forEach(item => {
        const diff = item.timestamp - num;

        // 查找比 num 大的最近的元素
        if (diff > 0) {
            if (diff < minDiffGreater) {
                minDiffGreater = diff;
                closestGreater = item;
            }
        }
        // 查找比 num 小的最近的元素
        else if (diff < 0) {
            if (-diff < minDiffSmaller) {
                minDiffSmaller = -diff;
                closestSmaller = item;
            }
        }
    });

    // 返回结果
    if (closestGreater !== null) {
        return closestGreater; // 返回最近的较大值
    } else {
        return closestSmaller; // 返回最近的较小值
    }
}

  deleteSuccess() {
    this.deleteTime && clearInterval(this.deleteTime);
    if (!this.downloadFlag) {
      return
    }
    const hasCurrent = this.state.numAry.includes(String(this.state.start_time))
    this.state.numAry.map(res => {
      deleteContent(this.props.route.params.firstKey, this.props.route.params.secondKey, res)
    })
    this.setState({isEdit: false, numAry: []});
    showLoading(false);
    showToast(stringsTo('delete_success'));
    this.downloadFlag = false;
    const {dataList} = this.getData()
    this.setState({
      dataList: dataList
    })
    if (!dataList?.length) {
      this._onPressBack()
      return
    }
    if (hasCurrent) {
      this.handleCurrent(this.findClosest(dataList, this.state.start_time))
    }
  }

  fillArray = (arr) => {
    while (arr.length < 20) {
        arr.unshift('0');
    }
    return arr;
  }

  _renderItem = (item, index) => {
    let {timestamp, duration} = item;
    let time = moment(timestamp * 1000).format('HH:mm');
    const endTime = moment(timestamp * 1000 + duration).format('HH:mm');
    let isSelected = false;
    if (this.state.numAry.length > 0) {
      isSelected = this.state.numAry.includes(String(item.timestamp))
    } else {
      isSelected = this.state.isSelectedAll;
    }

    const lists = {
      19: require('../../../resources/images/pic_move.png'),
      18: require('../../../resources/images/pic_person.png'),
      0: require('../../../resources/images/pic_sound.png')
    }

    const eventTypeList = [];
    const eventLists1 = Number(item.event_type) && Number(item.event_type).toString(2) && Number(item.event_type).toString(2).split('');
    const eventLists = this.fillArray(eventLists1)

    Object.keys(lists).map(res => {
      if (eventLists[res] == '1') {
        eventTypeList.push(lists[res])
      }
    })

    const isCurrent = this.state.start_time === timestamp;
    return (
        <XView
            key={index}
            style={{...styles.itemParent}}
            onPress={() => this.handleCurrent(item)}
            accessibilityLabel={'back_item_view_' + time}>
                <View style={{borderWidth: 2, borderColor: isCurrent ? imiThemeManager.theme.primaryColor : 'transparent', borderRadius: 4, width: (windowWidth - 4 * 14) / 3, overflow: 'hidden' }}>
                  <IMIImageView style={{...styles.itemIcon}} source={{uri: `file://${this.state.timeList[timestamp]}`}} />
                </View>
                <XView
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    flexDirection: 'row',
                    marginTop: 4,
                  }}
                >
                  {eventTypeList &&
                    eventTypeList.length > 0 &&
                    eventTypeList.map(res => {
                      return <XImage raw={true} style={{marginLeft: 0, width: 12, height: 12}} icon={res} />;
                    })}
                  <XText style={styles.itemTime} text={`${time}-${endTime}`} />
                </XView>
                {this.state.isEdit ? (
                  <XImage
                    raw={true}
                    style={{position: 'absolute', bottom: 20, right: 5, width: 20, height: 20}}
                    icon={
                      isSelected
                        ? require('../../../resources/images/icon_select_s.png')
                        : require('../../../resources/images/icon_select.png')
                    }
                  />
              ) : null}
        </XView>
    );
  };

  _onPressFullScreenTools = (showFullScreenTools) => {
    if (!showFullScreenTools) {
      this.setState({showFullScreenTools: true});
      this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
      this.fullScreenTooltsTimer = setTimeout(() => {
        if (this.timeLine1 && !this.timeLine1.isScroll()) {
          this._onCloseFullScreenTools();
        }
      }, 8000);
    } else {
      this._onCloseFullScreenTools();
    }
    
  };

  _onCloseFullScreenTools() {
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.setState({showFullScreenTools: false});
  }

  _onPressMute = () => {
    if (!this._canStepIn()) {
      return;
    }
    this.setState({mute: !this.state.mute});
  };

  getMute() {
    return this.state.mute;
  }

  setMute(mute) {
    this.setState({mute: mute});
  }


  // 截图成功回调
  _onCommCallback = event => {
    console.log('------截图成功-----保存截图成功回调', event);

    IMIFile.saveImageToPhotosAlbum(this.currentSnapshotPath, LetDevice.deviceID)
      .then(_ => {
        this.setState({
          screenShotPathType: 1,
          screenShotPath: _.data,
          snapshotVisible: true,
        });
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        console.log('------截图成功-----保存截图成功', this.currentSnapshotPath, LetDevice.deviceID);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
      })
      .catch(e => {
        console.log('截图失败', JSON.stringify(e));
      });
  };

  _renderSnapshotView(snapshotVisible) {
    if (!snapshotVisible) {
      return null;
    }
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          bottom: this.state.isFullScreen ? 106 : 20,
          left: isPhoneX() ? (this.state.isFullScreen ? 44 + 14 : 14) : 14,
          width: 140,
          height: 80,
          zIndex: 999999,
        }}>
        <ImageButton
          style={{
            width: '100%',
            height: '100%',
            borderWidth: 2,
            borderColor: 'white',
            borderRadius: 10,
          }}
          accessibilityLabel={'back_video_page_picture_show'}
          source={{uri: 'file://' + this.state.screenShotPath}}
          onPress={_ => {
            //TODO 跳转到相册预览？
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            this.setState({snapshotVisible: false});
            this.isView = true;
            this.firstLoad = false;
            if (this.state.screenShotPathType === 1) {
              this.props.navigation.push('PhotoView', {
                imageUrl: 'file://' + this.state.screenShotPath,
                isFullScreen: this.state.isFullScreen
              });
            } else {
              this.props.navigation.push('VideoPreView', {mediaData: {url: 'file://' + this.state.screenShotPath}, isFullscreen: this.state.isFullScreen});
            }
            // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
          }}
        />
      </View>
    );
  }

  _onEventChange = event => {
    console.log('event111', event);

    if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
      //this.setState({bps: event.extra.bps})
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
      this.setState({
        isLoading: false,
        isPlay: true,
        showPauseView: false,
        showErrorView: false,
        errorFind: false,
      });
  
      const current = parseInt(event.extra.currentTime / 1000);
      const second = current - this.state.start_time;
      this.isVideoFinish = false;
      if (second > 0 && second <= this.state.duration && !this.isDragStatus) {
        this.currentOffset = second;
        this.progressView && this.progressView.onSliderValueChanged(second);
      }
    } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) {
      this.reconnectFlag = true;
      this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false, errorFind: false,});
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
      if (this.reconnectFlag) {
        this.reconnectFlag = false
        if (this.currentOffset && this.currentOffset !== this.state.offset) {
          console.log('P2P重连++++++++================================', {currentOffset: this.currentOffset, offset: this.state.offset})
          IMILog.logI('P2P重连1', JSON.stringify({currentOffset: this.currentOffset, offset: this.state.offset}));
          this.setState(
            {showPauseView: false, isLoading: this.IMIVideoView ? true : false, offset: this.currentOffset, start_time: this.state.start_time, errorFind: false, showErrorView: false},
            () => {
              setTimeout(() => {
                this.IMIVideoView?.prepare();
                switch (this.state.speed) {
                  case 0:
                    this.IMIVideoView && this.IMIVideoView.speed(1);
                    break;
                  case 1:
                    this.IMIVideoView && this.IMIVideoView.speed(4);
                    break;
                  case 2:
                    this.IMIVideoView && this.IMIVideoView.speed(8);
                    break;
                  case 3:
                    this.IMIVideoView && this.IMIVideoView.speed(16);
                    break;
                }
              });
            },
          );
          return
        }
      }
      this.isDragStatus = false;
      this.setState({
        isLoading: false,
        isPlay: true,
        showPauseView: false,
        showErrorView: false,
        errorFind: false,
      });
      switch (this.state.speed) {
        case 0:
          this.IMIVideoView && this.IMIVideoView.speed(1);
          break;
        case 1:
          this.IMIVideoView && this.IMIVideoView.speed(4);
          break;
        case 2:
          this.IMIVideoView && this.IMIVideoView.speed(8);
          break;
        case 3:
          this.IMIVideoView && this.IMIVideoView.speed(16);
          break;
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
      this.setState({isLoading: false, isPlay: false, showPauseView: true, showNoData: false});
      
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
    }
  };

  // 缩放比例系数变化
  videoScaleChanged(data) {
    // if (!this._canStepIn()) {
    //   return;
    // }
    let scale = data.scaleRatio;
    let newScale = scale.toFixed(1);
    let zoomScale = this.state.zoomScale;

    if (Math.abs(zoomScale - newScale) < 0.1) {
      return;
    }

    // 进行节流操作
    let endTime = Date.now();
    if (endTime - this.startScaleTime < 50) {
      console.log('_onVideoScaleChanged', scale);
      return;
    }
    this.startScaleTime = endTime;
    this._updateScale(newScale);
  }

  _updateScale(scale) {
    if (scale) {
      if (this.angleViewTimeout) {
        // 隔一段时间就需要隐藏
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }
      this.angleViewTimeout = setTimeout(() => {
        this.setState({isShowZoomScale: false});
      }, 3000);
      this.setState({zoomScale: scale, isShowZoomScale: true, showPlayToolBar: scale > 1.03 ? false : true});
    }
  }

  onProgressChanged = (currentTime) => {
    this.setState({
      start_time: this.state.start_time,
      offset: parseInt(currentTime),
      isLoading: true,
      showPauseView: false, 
      showErrorView: false
    }, () => {
      this.currentOffset = parseInt(currentTime),
      this.isDragStatus = true;
      setTimeout(() =>{
        this.IMIVideoView?.prepare();
        if (!this.IMIVideoView) {
          this.setState({isLoading: false});
        }
      })
    })
  }

  // 进度条
  renderVideoProgressView() {
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          width: '98%', //NKGT-149
          bottom: 1,
          height: 80,
          zIndex: 999,
          opacity: this.state.snapshotVisible || (this.state.isFullScreen && !this.state.showFullScreenTools) ? 0 : 1,
        }}>
        <VideoProgressView
          ref={ref => (this.progressView = ref)}
          isPlayFromBeginning={true}
          duration={this.state.duration}
          isPlayFinish={this.state.isPlayFinish}
          onProgressValueChanged={this.onProgressChanged}
          leftText={this.state.start_time}
          rightText={this.state.rightText}
        />
      </View>
    );
  }

  // 加载中
  _loadingView() {
    if (this.state.showPauseView) {
      return;
    }
    if (!this.state.isLoading) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator style={{width: 54, height: 54}} color={'#ffffff'} size={'large'} />
        <Text
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}>
          {stringsTo('commLoadingText')}
        </Text>
      </View>
    );
  }

  // 设置对应错误文案
  _renderErrorTex() {
    if (this.state.errorCode.includes('202')) {
      return I18n.t('onPlayErrorMaxText', {code: this.state.errorCode});
    }
    return I18n.t('onPlayErrorText', {code: this.state.errorCode});
  }

  // 出错了
  _errorView() {
    if (!this.state.showErrorView) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          // text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
          text={this._renderErrorTex()}
        />

        <RoundedButtonView
          buttonText={stringsTo('error_code_common_retry')}
          buttonStyle={{
            margin: 14,
            paddingHorizontal: 15,
            height: 40,
          }}
          buttonTextStyle={{textAlign: 'center'}}
          onPress={() => {
            this.isDragStatus = true;
            this.setState({showPauseView: false, isLoading: this.IMIVideoView ? true : false, offset: this.isVideoFinish ? 0 : this.currentOffset, showErrorView: false}, () => {
              setTimeout(() => {
                this.IMIVideoView?.prepare();
                this.isVideoFinish = false;
                switch (this.state.speed) {
                  case 0:
                    this.IMIVideoView && this.IMIVideoView.speed(1);
                    break;
                  case 1:
                    this.IMIVideoView && this.IMIVideoView.speed(4);
                    break;
                  case 2:
                    this.IMIVideoView && this.IMIVideoView.speed(8);
                    break;
                  case 3:
                    this.IMIVideoView && this.IMIVideoView.speed(16);
                    break;
                }
              })
            });
          }}
        />
      </View>
    );
  }

  // 视频文件找不到
  _errorFindView() {
    if (!this.state.errorFind) {
      return;
    } 
    return (
      <XView
        pointerEvents="box-none"
        activeOpacity={1}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          backgroundColor: '#000',
          top: 0,
          zIndex: 9999,
        }} onPress={() => {
          this.state.isFullScreen && this._onPressFullScreenTools(this.state.showFullScreenTools);
        }}>
          {!this.state.isFullScreen && <NavigationBar
            type={NavigationBar.TYPE.DARK}
            backgroundColor={'transparent'}
            title={this.state.isEdit ? I18n.t('select_title_3', {code: this.state.numAry.length}) : this.state.title}
            left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
            right={[]}
          />}
          <View style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%',
          }}>
            <XText
                raw={true}
                style={{
                  textAlign: 'center',
                  color: colors.white,
                  fontSize: 15,
                  marginTop: this.state.isFullScreen ? 0 : -(this.getStatusBarHeightNew() + 20)
                }}
                text={I18n.t('no_video_data_failed')}
              />
              <RoundedButtonView
                buttonText={stringsTo('retry_connect')}
                buttonStyle={{
                  margin: 14,
                  width: 110,
                  height: 40,
                }}
                onPress={() => {
                  this.isDragStatus = true;
                  this.setState({errorFind: false, showPauseView: false, isLoading: this.IMIVideoView ? true : false, offset: this.isVideoFinish ? 0 : this.currentOffset}, () => {
                    setTimeout(() => {
                      this.IMIVideoView?.prepare();
                      this.isVideoFinish = false;
                      switch (this.state.speed) {
                        case 0:
                          this.IMIVideoView && this.IMIVideoView.speed(1);
                          break;
                        case 1:
                          this.IMIVideoView && this.IMIVideoView.speed(4);
                          break;
                        case 2:
                          this.IMIVideoView && this.IMIVideoView.speed(8);
                          break;
                        case 3:
                          this.IMIVideoView && this.IMIVideoView.speed(16);
                          break;
                      }
                    })
                  });
                }}
                accessibilityLabel={'playbackPage_retry_connect'}
              />
          </View>
      </XView>
    );
  }
  // 暂停
  _pauseView() {
    if (!this.state.showPauseView) {
      return null;
    }
    if (this.state.showErrorView) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ImageButton
          style={{width: 52, height: 52}}
          source={require('../../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          highlightedSource={require('../../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          onPress={() => {
            this._onPressPlay();
          }}
        />
      </View>
    );
  }

  _onPressPlay = () => {
    if (this.state.isLoading) {
      return;
    }
    //如果在错误状态下，点击会出现暂停、播放按钮与播放状态对应不上的问题
    if (this.state.showErrorView) {
      IMIToast.showToast(stringsTo('onlyDoInLive'), IMIToast.TYPE.BOTTOM);
      return;
    }
    this.setState({isClickPause: true, isStart: false});
    // 播放状态
    if (this.state.isPlay) {
      this.IMIVideoView?.stop();
      this.setState({isPlay: false, showPauseView: true, isLoading: false});
    } else {
      this.isDragStatus = true;
      this.setState({showPauseView: false, isLoading: this.IMIVideoView ? true : false, offset: this.isVideoFinish ? 0 : this.currentOffset}, () => {
        setTimeout(() => {
          this.IMIVideoView?.prepare();
          this.isVideoFinish = false;
          switch (this.state.speed) {
            case 0:
              this.IMIVideoView && this.IMIVideoView.speed(1);
              break;
            case 1:
              this.IMIVideoView && this.IMIVideoView.speed(4);
              break;
            case 2:
              this.IMIVideoView && this.IMIVideoView.speed(8);
              break;
            case 3:
              this.IMIVideoView && this.IMIVideoView.speed(16);
              break;
          }
        })
      });
      
    }
  };

  handleAll(){
    const currentNumAry = this.state.numAry;
    if (currentNumAry.length === 50) {
      this.setState({
        isSelectedAll: false, 
        numAry: []
      });
      return
    }
    if (this.state.dataList.length > 50) {
      const newNumAry = [...currentNumAry]
      this.state.dataList.map((res) => {
        if (!newNumAry.includes(String(res.timestamp))) {
          if (newNumAry.length < 50) {
            newNumAry.push(String(res.timestamp))
          }
        }
      });
      
      this.setState({
        isSelectedAll: false, 
        numAry: newNumAry
      });
      showToast(stringsTo('delete_failed_limit'));
      return
    }
    this.setState({
      isSelectedAll: 
      !this.state.isSelectedAll, 
      numAry: this.state.isSelectedAll ? [] : this.state.dataList.map(res => {return String(res.timestamp)})
    });
  }

  _renderPortraitScreenVideoViewArea() {
    return <NavigationBar
        type={NavigationBar.TYPE.DARK}
        backgroundColor={'transparent'}
        title={this.state.isEdit ? I18n.t('select_title_3', {code: this.state.numAry.length}) : this.state.title}
        // subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
        left={!this.state.isEdit ? [
          {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
             this._onPressBack()
          },
          accessibilityLabel: 'housekeeping_back',
        }
      ] : [{
        key: NavigationBar.ICON.CUSTOM,
        n_source: require('../../../resources/images/houseKeepingV2/icon_angel_del_white.png'),
        onPress: () => {
          this.setState({isEdit: false, numAry: []});
        },
        accessibilityLabel: 'housekeeping_back',
      }]}
      right={!this.state.isEdit ? [
          {
          key: NavigationBar.ICON.CUSTOM,
          n_source: require('../../../resources/images/houseKeepingV2/icon_angel_edit_white.png'),
          onPress: _ => this.handleSelect(),
          accessibilityLabel: 'back_editor',
        },
      ] : [{
        key: NavigationBar.ICON.CUSTOM,
        n_source: require('../../../resources/images/houseKeepingV2/icon_angel_allSelect_white.png') ,
        onPress: _ => {
          this.handleAll()
        },
        accessibilityLabel: 'back_editor',
      }]}
      />
  }

  _onPressSpeed = () => {
    if (!this._canStepIn()) {
      return;
    }
    switch (this.state.speed) {
      case 0:
        this.setState({speed: 1, mute: true});
        this.IMIVideoView && this.IMIVideoView.speed(4);
        break;
      case 1:
        this.setState({speed: 2, mute: true});
        this.IMIVideoView && this.IMIVideoView.speed(8);
        break;
      case 2:
        this.setState({speed: 3, mute: true});
        this.IMIVideoView && this.IMIVideoView.speed(16);
        break;
      case 3:
        this.setState({speed: 0});
        this.IMIVideoView && this.IMIVideoView.speed(1);
        break;
    }
  };

  // 全屏工具
  _onPressFullScreenTools = () => {
    this.setState({showFullScreenTools: true});
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.fullScreenTooltsTimer = setTimeout(() => {
      this._onCloseFullScreenTools();
    }, 5000);
  };

  // 退出全屏工具
  _onCloseFullScreenTools() {
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.setState({showFullScreenTools: false});
  }

  // 全屏
  _onPressFullScreen = () => {
    // if (!this._canStepIn()) {
    //   return;
    // }
    this.setState({isFullScreen: true}, () => {
      isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
      NavigationBar.setStatusBarHidden(true);
      this.props.navigation.setOptions({tabBarVisible: false});
    });
    this._onPressFullScreenTools();
  };

  // 退出全屏
  _exitFullScreen = () => {
    this.setState({isFullScreen: false}, () => {
      Orientation.lockToPortrait();
      NavigationBar.setStatusBarHidden(false);
    });
    this.props.navigation.setOptions({tabBarVisible: true});
    this._onCloseFullScreenTools();
    
  };

  _canStepIn() {
    //获取设备是否在线的状态可能不准确 增加一个条件
    if (!this.state.isPlay || this.state.showErrorView) {
      IMIToast.showToast(stringsTo('onlyDoInLive'), IMIToast.TYPE.BOTTOM);
      return false;
    }
    return true;
  }

  //点击截屏按钮
  _onPressScreenShot = () => {
    if (!this._canStepIn()) {
      return;
    }
    isCheckingPermission = true;
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView?.snap(this.currentSnapshotPath);
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
          }
          isCheckingPermission = false;
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
        isCheckingPermission = false;
      }
    });
  };

  //IOS在视频流暂停时,将录制的视频保存到相册
  _saveVideoToPhotosAlbum() {
    if (this.state.recordDuration < 6) {
      IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      this.IMIVideoView?.stopRecord()
        .then(_ => {})
        .catch(error => {}); //不调用，会导致iOS下次录制失败
      this.setState({recording: false, recordDuration: 0, isStart: false});
      return;
    }
    let pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
    this.IMIVideoView?.stopRecord();
    this.setState({recording: false, recordDuration: 0, isStart: false});
    IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID)
      .then(_ => {
        //转存视频
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.setState({screenShotPathType: 2,screenShotPath: _.data, snapshotVisible: true});
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
        this.IMIVideoView?.stopRecord()
          .then(_ => {})
          .catch(error => {});
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
      })
      .catch(error => {
        showToast(stringsTo('action_fail'));
      });
  }

  /**
   * 全屏状态videoView区域填充UI
   * @returns {Element}
   * @private
   */
  _renderLandscapeScreenVideoViewArea(isFullScreen, showFullScreenTools) {
    let title = speedTitleAry[this.state.speed];
    let speedAccessibilityLabel = speedAccessibilityLabelTitleAry[this.state.speed];
    return (
      <View pointerEvents="box-none" style={{width: '100%', height: '100%', display: isFullScreen && showFullScreenTools ? '' : 'none'}}>
        <PlayBackFullScreenToolBarView
          exitPress={this._exitFullScreen}
          playPress={this._onPressPlay}
          isPlay={this.state.isPlay}
          mutePress={this._onPressMute}
          mute={this.state.mute}
          muteDisabled={this.state.isPlay && this.state.speed == 0 ? false : true}
          speedTitle={title}
          speedPress={this._onPressSpeed}
          speedDisabled={!this.state.isPlay}
          speedAccessibilityLabel={speedAccessibilityLabel}
          // speedDisabled={LetDevice.model == "a1FKrifIRwH" ? true:false}
          screenshotPress={this._onPressScreenShot}
          screenshotDisabled={!this.state.isPlay}
          recording={this.state.recording}
          recordDisabled={this.state.speed == 0 ? false : true}
          fullScreenPress={this._exitFullScreen}
          isShowSpeedItem={true}
           // 隐藏录屏按钮
           ignoreRecord={true}
        />
      </View>
    )
  }

   // 操作栏
   _renderPortraitScreenPlayerToolBarArea(isFullScreen) {

    return (
      <View style={{flex: 1, flexDirection: 'column', display: isFullScreen ? 'none' : ''}}>
        <PlayBackToolBarView
          speedTitle={['1X', '4X', '8X', '16X']}
          speedPress={this._onPressSpeed}
          speedIndex={this.state.speed}
          speedDisabled={!this.state.isPlay}
          fullscreenPress={this._onPressFullScreen}
          mutePress={this._onPressMute} // 静音
          mute={!!this.state.mute}
          muteDisabled={this.state.isPlay && this.state.speed == 0 ? false : true}
          screenshotPress={this._onPressScreenShot}
          screenshotDisabled={!this.state.isPlay}
          recordDisabled={this.state.speed == 0 ? false : true}
          recording={this.state.recording}
          playPress={this._onPressPlay}
          play={this.state.isPlay}
          isShowSpeedItem={true}
          // 隐藏录屏按钮
          ignoreRecord={true}
        />
        {this._renderListView()}
      </View>
    );
  }

  // 截图成功回调
  _onCommCallback = event => {
    console.log('------截图成功-----保存截图成功回调', event);

    IMIFile.saveImageToPhotosAlbum(this.currentSnapshotPath, LetDevice.deviceID)
      .then(_ => {
        this.setState({
          screenShotPathType: 1,
          screenShotPath: _.data,
          snapshotVisible: true,
        });
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        console.log('------截图成功-----保存截图成功', this.currentSnapshotPath, LetDevice.deviceID);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
      })
      .catch(e => {
        console.log('截图失败', JSON.stringify(e));
      });
  };


  
  render() {
    console.log(this.state.start_time, this.state.end_time, this.state.offset)
    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        pointerEvents="box-none"
        style={{flex: 1, backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
        <XView
          activeOpacity={1}
          style={{flex: 1}}
          >
          <IMVodPlayView
            style={{flex: 1}}
            ref={ref => {
              if (!this.IMIVideoView && ref) {
                  this.IMIVideoView = ref;
                  ref.prepare();
                }
              }}
              mute={this.state.mute}
              dataSource={{
                iotId: LetDevice.deviceID,
                start_time: `${this.state.start_time}`,
                end_time: `${this.state.end_time}`,
                offset: `${this.state.offset}`,
              }}
              onPrepared={() => {
                console.log('=======', this.isView)
                !this.isView && !this.isDragStatus && this.progressView && this.progressView.resetStatus();
                this.isView = false;
                this.IMIVideoView.start(); 
                switch (this.state.speed) {
                  case 0:
                    this.IMIVideoView && this.IMIVideoView.speed(1);
                    break;
                  case 1:
                    this.IMIVideoView && this.IMIVideoView.speed(4);
                    break;
                  case 2:
                    this.IMIVideoView && this.IMIVideoView.speed(8);
                    break;
                  case 3:
                    this.IMIVideoView && this.IMIVideoView.speed(16);
                    break;
                }        
              }}
              onVideoViewClick={() => {
                this.state.isFullScreen && this.state.showFullScreenTools
                ? this._onCloseFullScreenTools()
                : this._onPressFullScreenTools();
              }}
              onCommCallback={this._onCommCallback}
              onEventChange={this._onEventChange}
              onPlayCompletion={() => {
              }}
              onErrorChange={event => {
                console.log('回看-----onErrorChange------   event.code ' + event.code, event);
                // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
                //IMILog.logD("王 回看错误 PayBack onErrorChange >",event.toString())
                do {
                  this.setState({isLoading: false, isPlay: false, showErrorView: true, errorCode: event?.extra?.arg1 + '(' + event?.extra?.arg3 + ')'});
                  return;
                } while (false);
              }}
              onVideoZoomScale={data => {
                console.log('onVideoZoomScale---', data);
                if (data) {
                  console.log('缩放有值走这里');
                  this.videoScaleChanged(data);
                } else {
                  console.log('缩放无值走这里');
                }
              }}
            />
             <View
              pointerEvents="box-none"
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                flexDirection: 'column',
                alignItems: 'center',
              }}>
                {this._renderSnapshotView(this.state.snapshotVisible)}
                 {this.renderVideoProgressView()}
                 {this._loadingView()}
                {this._errorView()}
                {this._errorFindView()}
                {this._pauseView()}
                {!this.state.isFullScreen && this._renderPortraitScreenVideoViewArea()}
                {this._renderLandscapeScreenVideoViewArea(this.state.isFullScreen, this.state.showFullScreenTools)}
              </View>
            </XView>
            {this._renderPortraitScreenPlayerToolBarArea(this.state.isFullScreen)}
            {!this.state.isFullScreen && this._renderEditView()}
            {this.renderZoomScaleView()}
            <MessageDialog
              title={stringsTo('delete_alert')}
              visible={this.state.showDeleteTip}
              canDismiss={true}
              onDismiss={() => {
                this.setState({showDeleteTip: false});
              }}
              buttons={[
                {
                  text: I18n.t('cancel'),
                  accessibilityLabel: 'cancelVideoDelete',
                  callback: _ => {
                    this.setState({showDeleteTip: false});
                  },
                },
                {
                  text: I18n.t('ok_button'),
                  accessibilityLabel: 'okVideoDelete',
                  callback: _ => {
                    showLoading(stringsTo('delete_title_loading'), true);
                    this.setState({showDeleteTip: false});
                    this.deletImageArray();
                  },
                },
              ]}
            />
      </View>
    );
  }
}

const styles = StyleSheet.create({
    itemTitle: {
      fontSize: 12,
      paddingLeft: 14,
      color: colors.black,
      fontWeight: 'bold'
    },
    itemParent: {
      // paddingLeft: 14,
      paddingTop: 14,
      backgroundColor: colors.white,
      // width: (windowWidth - 4 * 14) / 3,
    },
    itemTime: {
      fontSize: 11,
      color: colors.gray,
      marginLeft: 1,
      borderRadius: 10,
    },
    itemIcon: {
      borderRadius: 4,
      width: (windowWidth - 4 * 14) / 3,
      height: (windowWidth - 4 * 14) / 3 / 16 * 9,
    },
});
  
