/**
 * TimeLineScrollView.js
 * <AUTHOR>
 * @date 2020/12/25
 * @property {object} style - 外层样式
 * @property {func} onCenterValueChanged - 滑动时间轴回调
 * @property {array} dataArray - [{"EndTime":1604603979,"BeginTime":1604592039，Type:2},]
 * @property {boolean} showEvent 是否显示回看视频中的事件并用色块标记
 * @property {boolean} isFullScreen 本组件是否显示在横屏页面(横屏不显示底部事件标注颜色)
 */
import React from 'react';
import {View, ScrollView, Animated, Image, StyleSheet, Text, Dimensions, PanResponder} from 'react-native';

const itemCount = (24 * 60) / 5; //一天的格子总数
const itemWidth = 15; //5分钟的格子长度
const dayWidth = itemWidth * itemCount; //1天的格子长度
const secondWidth = itemWidth / 5 / 60; //1秒钟的格子长度
const maxScale = 2.0;
const minScale = 1.0;
//颜色标记数组，目前仅支持IPC062，不支持的项目使用的是普通录像的蓝色色块来标记回看，type需要和固件端商量好进行后扩展此数组 2022.09.20 fg
const eventColorArray = [
  {title: stringsTo('playback_no_event'), color: '#496EE0', type: 2},
  {title: stringsTo('move_event'), color: '#A6E6BD', type: 21},
  {title: stringsTo('people_event'), color: '#FAB78F', type: 22},
  {title: stringsTo('alarm_loud_switch'), color: '#F6FA8F', type: 23},
  LetDevice.model == 'a1Godgpvr3D' ? {} : {title: stringsTo('keyArea'), color: '#E87979', type: 24},
];

import PropTypes from 'prop-types';
import {stringsTo} from '../../../../../globalization/Localize';
import {LetDevice} from '../../../../../imilab-rn-sdk';

export default class TimeLineScrollView extends React.PureComponent {
  static propTypes = {
    darkType: PropTypes.bool, //背景是否为深色背景,若为深色背景则刻度线要为亮色，默认否
    style: PropTypes.any,
    dataArray: PropTypes.array,
    onCenterValueChanged: PropTypes.func,
    showEvent: PropTypes.bool,
    isFullScreen: PropTypes.bool,
  };

  static defaultProps = {
    darkType: false,
    dataArray: [],
    showEvent: false,
    isFullScreen: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      enableScroll: true,
      halfWidth: Dimensions.get('window').width / 2, //半屏宽，用于scrollView能头尾滑到中间
      currScale: minScale, //时间轴拉伸比例
    };
    this.centerTimestamp = 0; //0秒到现在的秒数
    this.isScrolling = false; //是否手动滑动中
    this.scrollEndTimeout = null; //滑动结果延迟触发器
    this._panResponder = PanResponder.create({
      // 要求成为响应者：
      onStartShouldSetPanResponder: (evt, gestureState) => {
        // alert("onStartShouldSetPanResponder");
        return evt.nativeEvent.changedTouches.length > 1;
      },
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        // alert("onStartShouldSetPanResponderCapture");
        return evt.nativeEvent.changedTouches.length > 1;
      },
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // alert("onMoveShouldSetPanResponder");
        return evt.nativeEvent.changedTouches.length > 1;
      },
      onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
        // alert("onMoveShouldSetPanResponderCapture");
        return evt.nativeEvent.changedTouches.length > 1;
      },
      onPanResponderGrant: (evt, gestureState) => {
        // alert("onPanResponderGrant");
        // 开始手势操作。给用户一些视觉反馈，让他们知道发生了什么事情！
        // gestureState.{x,y} 现在会被设置为0
        // this.setState({enableScroll: false});
      },
      onPanResponderMove: (evt, gestureState) => {
        // 最近一次的移动距离为gestureState.move{X,Y}
        // 从成为响应者开始时的累计手势移动距离为gestureState.d{x,y}
        // alert("onPanResponderMove:"+gestureState.dx);
      },
      onPanResponderTerminationRequest: (evt, gestureState) => false,
      onPanResponderRelease: (evt, gestureState) => {
        // 用户放开了所有的触摸点，且此时视图已经成为了响应者。
        // 一般来说这意味着一个手势操作已经成功完成。
        // this.setState({enableScroll: true});
      },
    });
  }

  _onLayout = event => {
    this.setState({halfWidth: event.nativeEvent.layout.width / 2});
  };

  _setZoom() {
    this.setState({currScale: maxScale});
  }

  _onCenterValueChanged(offset) {
    this.scrollEndTimeout && clearTimeout(this.scrollEndTimeout);
    this.scrollEndTimeout = setTimeout(() => {
      this.isScrolling = false;
      this.centerTimestamp = offset / secondWidth;
      this.props.onCenterValueChanged && this.props.onCenterValueChanged(this.centerTimestamp);
    }, 500);
  }

  componentWillUnmount() {
    this.scrollEndTimeout && clearTimeout(this.scrollEndTimeout);
    this.DefaultTime && clearTimeout(this.DefaultTime);
  }

  scrollToTimestamp(time) {
    if (!this.isScrolling) {
      this.centerTimestamp = time;
      this.scrollView.scrollTo({x: this.centerTimestamp * secondWidth, y: 0, animated: false}); //animated=true会触发scrollView滑动事件
    }
  }

  /**
   * 为了默认设置暂停状态没有跳转到对应位置
   * @param time
   */
  scrollToTimestampDefault(time) {
    if (!this.isScrolling) {
      this.DefaultTime && clearTimeout(this.DefaultTime);
      this.DefaultTime = setTimeout(() => {
        this.centerTimestamp = time;
        this.scrollView.scrollTo({x: this.centerTimestamp * secondWidth, y: 0, animated: false}); //animated=true会触发scrollView滑动事件
      }, 600);
    }
  }

  isScroll() {
    return this.isScrolling;
  }

  render() {
    return (
      <View>
        <View
          {...this._panResponder.panHandlers}
          onLayout={this._onLayout}
          style={[
            this.props.style,
            {
              width: '100%',
              height: 54,
              flexDirection: 'column' /*backgroundColor: "#FAFAFA"*/,
            },
          ]}>
          {/*上边线*/}
          <View
            style={{
              width: '100%',
              height: 1,
              backgroundColor: '#000000',
              opacity: 0.05,
              position: 'absolute',
            }}
          />
          {/*时间轴*/}
          <ScrollView
            contentContainerStyle={{paddingHorizontal: this.state.halfWidth}}
            ref={ref => (this.scrollView = ref)}
            style={{flex: 1}}
            showsHorizontalScrollIndicator={false}
            horizontal={true}
            scrollEnabled={this.state.enableScroll}
            bounces={false}
            scrollEventThrottle={16} // 每16毫秒更新一次
            onScroll={event => {
              if (this.isScrolling) {
                const centerTimestamp = event.nativeEvent.contentOffset.x / secondWidth;
                this.props.onScrolling(centerTimestamp);
              }
            }}
            onScrollBeginDrag={event => {
              // alert("onScrollBeginDrag:"+JSON.stringify(event.nativeEvent));
              this.isScrolling = true;
              this.scrollEndTimeout && clearTimeout(this.scrollEndTimeout);
              this.DefaultTime && clearTimeout(this.DefaultTime);
            }}
            onScrollEndDrag={event => {
              this._onCenterValueChanged(event.nativeEvent.contentOffset.x);
            }}
            onMomentumScrollBegin={event => {
              // alert("onMomentumScrollBegin:" + JSON.stringify(event.nativeEvent));
              this.isScrolling = true;
              this.scrollEndTimeout && clearTimeout(this.scrollEndTimeout);
              this.DefaultTime && clearTimeout(this.DefaultTime);
            }}
            onMomentumScrollEnd={event => {
              //scrollTo会触发
              // alert("onMomentumScrollEnd:" + this.formatSeconds(offset / secondWidth));
              this._onCenterValueChanged(event.nativeEvent.contentOffset.x);
            }}>
            <View style={{flexDirection: 'row', position: 'relative'}}>
              {/*刻度*/}
              {this._renderLineView()}
              {/*有回看视频的时间轴*/}
              <View style={{flexDirection: 'row', width: '100%', height: '100%', position: 'absolute'}}>
                {this._renderHasVideoView()}
              </View>
            </View>
          </ScrollView>
          {/*下边线*/}
          <View style={{width: '100%', height: 0.5, backgroundColor: '#000000', opacity: 0.2}} />
          {/*中心指针*/}
          <Image
            pointerEvents={'none'}
            source={require('./res/pic_timeline_select.png')}
            style={{width: 10, height: 54, position: 'absolute', alignSelf: 'center'}}
          />
        </View>
        {this._renderColorMarkHint()}
      </View>
    );
  }

  _renderLineView = () => {
    let viewLineArr = []; //每隔5分钟的一条刻度线
    let viewTextArr = [];
    for (let i = 0; i < itemCount; i++) {
      let isLong = i % 6 === 0;
      viewLineArr.push(
        <View style={{flex: 1, flexDirection: 'row'}} key={`Line${i}`}>
          <View
            style={
              isLong
                ? this.props.darkType
                  ? styles.longLineDarkStyle
                  : styles.longLineStyle
                : this.props.darkType
                ? styles.shortLineDarkStyle
                : styles.shortLineStyle
            }
          />
          {i === itemCount - 1
            ? [
                <View style={{flex: 1}} key={'Line_end_flex'} />,
                <View style={this.props.darkType ? styles.longLineDarkStyle : styles.longLineStyle} key={'Line_end'} />,
              ]
            : null}
        </View>,
      );
      if (isLong) {
        //半小时或者整点的刻度线
        let hour = parseInt(i / 12);
        viewTextArr.push(
          <Text style={this.props.darkType ? styles.hourTextDarkStyle : styles.hourTextStyle} key={`LineText${i}`}>{`${
            hour > 9 ? hour : `0${hour}`
          }:${i % 12 ? '30' : '00'}`}</Text>,
        );
      }
    }

    return (
      <Animated.View
        key={'LineView'}
        style={{
          width: dayWidth * this.state.currScale,
          backgroundColor: 'transparent',
          position: 'relative',
          height: '100%',
          flexDirection: 'row',
        }}>
        {viewLineArr}
        <View style={{position: 'absolute', width: '100%', bottom: 6, flexWrap: 'nowrap', flexDirection: 'row'}}>
          {viewTextArr}
        </View>
      </Animated.View>
    );
  };

  /*绘制有回看视频的区域*/
  _renderHasVideoView = () => {
    // let morningTime = new Date(new Date().setHours(0, 0, 0, 0)) / 1000;
    return this.props.dataArray.map((item, index) => {
      let date = new Date(item.timestamp * 1000);
      let offset = date.getHours() * 60 * 60 + date.getMinutes() * 60 + date.getSeconds();
      let colorItem = this.props.showEvent ? eventColorArray.find(vo => vo.type == item.Type) : eventColorArray[0];
      colorItem = colorItem ? colorItem : eventColorArray[4];
      
      let preStart = 0;
      let left = Math.round((offset * secondWidth * this.state.currScale));
      let width = Math.ceil(((item.duration / 1000) * secondWidth * this.state.currScale));
      try {
        if (index > 0) {
          let predate = new Date(this.props.dataArray[index-1]?.timestamp * 1000);
          let preoffset = predate.getHours() * 60 * 60 + predate.getMinutes() * 60 + predate.getSeconds();
          preStart = Math.round((preoffset * secondWidth * this.state.currScale))
        }
        if (this.props.dataArray[index-1]?.timestamp && this.props.dataArray[index-1]?.timestamp > item.timestamp) {
          if ((left + width) > preStart) {
            width = preStart - left
          }
        }
      } catch (error) {
        console.log(error)
      }
      return (
        <Animated.View
          key={`HasVideoView${index}`}
          style={{
            height: '100%',
            flexDirection: 'column',
            width: width,
            left: left,
            position: 'absolute',
          }}>
          <View style={{width: '100%', height: 1, backgroundColor: colorItem.color}} />
          <View
            style={{
              width: '100%',
              flex: 1,
              backgroundColor: colorItem.color,
              opacity: this.props.isFullScreen ? 0.3 : 0.5,
            }}
          />
        </Animated.View>
      );
    });
  };

  /*绘制各个颜色区块代表的事件类型*/
  _renderColorMarkHint() {
    if (!this.props.showEvent || this.props.isFullScreen) {
      return null;
    }
    return (
      <View style={{flexWrap: 'wrap', flexDirection: 'row', alignItems: 'center', backgroundColor: '#ffffff'}}>
        {eventColorArray.map((item, i) => (
          <View
            style={{flexDirection: 'row', marginTop: 20, marginBottom: 10, marginHorizontal: 20, alignItems: 'center'}}>
            <View style={{width: 7, height: 7, borderRadius: 90, backgroundColor: item.color}} />
            <Text style={{color: '#333333', textSize: 12, marginLeft: 10}}>{item.title}</Text>
          </View>
        ))}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  hourTextStyle: {
    color: '#000000',
    fontSize: 10,
    flex: 1,
    paddingLeft: 3,
    opacity: 0.2,
  },
  longLineStyle: {
    width: 0.5,
    // flex: 1,
    marginVertical: 6,
    backgroundColor: '#000000',
    opacity: 0.1,
  },
  shortLineStyle: {
    width: 0.5,
    // flex: 1,
    marginVertical: 16,
    backgroundColor: '#000000',
    opacity: 0.1,
  },

  hourTextDarkStyle: {
    color: '#FFFFFF',
    fontSize: 10,
    flex: 1,
    paddingLeft: 3,
    opacity: 0.2,
  },
  longLineDarkStyle: {
    width: 0.5,
    // flex: 1,
    marginVertical: 6,
    backgroundColor: '#FFFFFF',
    opacity: 0.3,
  },
  shortLineDarkStyle: {
    width: 0.5,
    // flex: 1,
    marginVertical: 16,
    backgroundColor: '#FFFFFF',
    opacity: 0.3,
  },
});
