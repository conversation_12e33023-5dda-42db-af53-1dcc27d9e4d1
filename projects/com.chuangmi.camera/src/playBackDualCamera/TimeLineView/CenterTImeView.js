import { Text } from 'react-native';
import React from "react";

export default class CenterTimeView extends React.Component {
  
  state = {
    centerTimestamp: 0
  }

  date = new Date();
  
  render() {
    if (this.state.centerTimestamp <= 0) {
      return null;
    }
    
    this.date.setTime(this.state.centerTimestamp);
    let hour = this.date.getHours();
    let minute = this.date.getMinutes();
    let seconds = this.date.getSeconds();
    let centerText = `${ hour > 9 ? hour : `0${ hour }` }:${ minute > 9 ? minute : `0${ minute }` }:${ seconds > 9 ? seconds : `0${ seconds }` }`;
    

    return (
      <Text
          accessibilityLabel={'zoom'}
          numberOfLines={1}
          ellipsizeMode={'tail'}
          style={{lineHeight: 28, fontSize: 15, color: '#FFFFFF', textAlignVertical: 'center', textAlign: 'center'}}>
          {`${centerText}`}
        </Text>
    );
  }
}