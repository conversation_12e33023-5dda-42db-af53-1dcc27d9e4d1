import React from 'react';
import {View, Image, StyleSheet, Text} from 'react-native';
import PropTypes from 'prop-types';
import TimeLineScrollView from './TimeLineScrollView';
import {stringsTo} from '../../../../../globalization/Localize';
import TextImageButton from '../../../../../imi-rn-commonView/TextImageButton/TextImageButton';
import {dateFormat} from '../../../../../imilab-rn-sdk/utils/DateUtils';

export default class TimeLineView extends React.Component {
  static propTypes = {
    style: PropTypes.any,
    darkType: PropTypes.bool,
    dataArray: PropTypes.array,
    onCenterValueChanged: PropTypes.func,
    currentDate: PropTypes.any,
    calendarClick: PropTypes.func, //点击日期选择
    isShowCalendarTitle: PropTypes.bool, //是否展示日期抬头
    setIsPlay: PropTypes.bool,
    setPauseLineTime: PropTypes.number,
    showEvent: PropTypes.bool,
    isFullScreen: PropTypes.bool,
  };

  static defaultProps = {
    darkType: false,
    dataArray: [],
    currentDate: new Date(),
    calendarClick: null,
    isShowCalendarTitle: true,
    setIsPlay: false,
    setPauseLineTime: 0,
    showEvent: false,
    isFullScreen: false,
  };

  constructor(props) {
    super(props);
    this.state = {};
  }

  scrollToTimestamp(time) {
    this.timeLine && this.timeLine.scrollToTimestamp(time);
  }

  isScroll() {
    return this.timeLine && this.timeLine.isScroll();
  }

  //组件加载成功并渲染出来
  componentDidMount() {
    if (this.props.setIsPlay) {
      this.timeLine && this.timeLine.scrollToTimestampDefault(this.props.setPauseLineTime);
    }
  }
  shouldComponentUpdate(nextProps, nextState){
    if (JSON.stringify(nextProps.dataArray) !== JSON.stringify(this.props.dataArray)) {
      return true
    }
    if (JSON.stringify(nextProps.currentDate) !== this.props.currentDate) {
      return true
    }
    return false
  }
  render() {
    return (
      <View style={[this.props.style, {flexDirection: 'column'}]}>
        {this.props.isShowCalendarTitle ? (
          <View style={{height: 30, flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{flex: 1, fontSize: 12, color: '#7F7F7F', marginLeft: 10}}>
              {dateFormat(this.props.currentDate, stringsTo('date_format_yyyy_mm_dd'))}
            </Text>
            <TextImageButton
              type={TextImageButton.TYPE.ICON_RIGHT}
              title={stringsTo('change_date')}
              source={require('./res/icon_down.png')}
              imageStyle={{width: 20, height: 20, marginLeft: 6, marginRight: 7}}
              textStyle={{fontSize: 12, color: '#7F7F7F'}}
              onPress={() => {
                this.props.calendarClick && this.props.calendarClick();
              }}
            />
          </View>
        ) : null}
        <TimeLineScrollView
          darkType={this.props.darkType}
          ref={ref => (this.timeLine = ref)}
          dataArray={this.props.dataArray}
          onCenterValueChanged={this.props.onCenterValueChanged}
          onScrolling={this.props.onScrolling}
          showEvent={this.props.showEvent}
          isFullScreen={this.props.isFullScreen}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({});
