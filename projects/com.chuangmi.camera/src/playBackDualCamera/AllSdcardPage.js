/*
 * 作者：sunhongda
 * 文件：IPC031MainPage.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
import React from 'react';

import {StyleSheet, TouchableOpacity, View,Text,Image, SafeAreaView, Dimensions, ScrollView,FlatList,BackHandler} from "react-native";

import {XImage, XText, XView} from "react-native-easy-app";

import {
    RNLine,
    colors,
    imiThemeManager,
    showLoading,
    showToast,
    RoundedButtonView
} from '../../../../imilab-design-ui';

import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";

import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {
    LetDevice,
    IMIGotoPage,
    BaseDeviceComponent,
    DateUtils,
    IMIStorageBean,
    imiAlarmEventCloudApi
} from '../../../../imilab-rn-sdk';

import Orientation from "react-native-orientation";
import I18n, {stringsTo} from "../../../../globalization/Localize";
import {IMIStorage} from "../../../../imilab-rn-sdk";
import IMIToast from "../../../../imilab-design-ui/src/widgets/IMIToast";
import {Calendar, CalendarList, LocaleConfig} from 'react-native-calendars';
import Utils, {isAndroid, isIphoneXSeries} from "../../../../imilab-rn-sdk/utils/Utils";
import moment from "moment";
import {dateFormat} from "../../../../imilab-rn-sdk/utils/DateUtils";
import ModalView from "../../../../imi-rn-commonView/ModalView/ModalView";


const screen_width = Dimensions.get('window').width;
const screen_height = Dimensions.get('window').height;


export default class AllSdcardPage extends BaseDeviceComponent {

    constructor(props) {
        super(props);
        this.state = {
            currentDate: new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 0, 0, 0),
            dateTime:'',
            playData:[{text:"123",index:0},{text:"456",index:1},{text:"789",index:2},{text:"hhhh",index:3},{text:"6666",index:4}],
            isShowCalendar:false,
            dateData: {},//日期显示
            dataArray:[],
        };
    }

    UNSAFE_componentWillMount() {
        this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack());
        this.setState({dateTime:DateUtils.dateFormat(this.state.currentDate, "yyyy-MM-dd")},callback=>{
            console.log('当前时间---',this.state.currentDate,this.state.dateTime);
        });
        this._queryDayTryAgain(true);
        this._queryMonthData();
    }

    //失败后重新加载一次
    _queryDayTryAgain(isTry){
        this._queryDayData(this.state.currentDate).then(dataArr => {
            console.log("szm 回看 查询数据：", dataArr);
            if (dataArr.length > 0) {
                this.setState({dataArray: dataArr});
            } else {
                IMIToast.showToast(stringsTo('commLoadingFailText'), IMIToast.TYPE.CENTER);
            }
        });
    }

    async _queryDayData(currDate) {
        let startDate = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
        let endDate = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 23, 59, 59);
        let startTime = parseInt(startDate.getTime() / 1000);
        let endTime = parseInt(endDate.getTime() / 1000);
        let dataArr = [];
        let isMore = true;
        const MAX_LEN = 128;
        let params = {
            "BeginTime": startTime,
            "EndTime": endTime,
            "QuerySize": MAX_LEN,//传10会返回10+条
            "Type": 0
        }
        console.log('playback QueryRecordTimeList params='+JSON.stringify(params));
        while (isMore) {
            await LetDevice.sendDeviceServerRequest("QueryRecordTimeList", params, true).then(result => {
                console.log('playback QueryRecordTimeList result='+JSON.stringify(result));
                let timelist = result.TimeList;
                let resLen = timelist.length;
                if (resLen > 0) {
                    dataArr.push(...timelist);
                    if (endTime>=timelist[resLen - 1].EndTime) {
                        isMore = false;
                    }
                    endTime = timelist[resLen - 1].EndTime;
                }
                if (resLen < MAX_LEN) {
                    isMore = false;
                }
            }).catch(err => {
                isMore = false;
                console.log('error--',err)
                // alert(err)
            });
        }
        //this.setState({dataArray: dataArr});
        return dataArr;
    }

    _queryMonthData(month) {
        LetDevice.sendDeviceServerRequest("QueryMonthRecord", {"Month": month?month:DateUtils.dateFormat(new Date(), "yyyyMM")}, true).then(result => {
            let ary = result.RecordFlags.split('');
            console.log('QueryMonthRecord--',month?month:DateUtils.dateFormat(new Date(), "yyyyMM"));
            this._setDateString(ary,month?month:DateUtils.dateFormat(new Date(), "yyyyMM"));
            // this.setState({})
            showLoading(false);
        }).catch(err => {
            showLoading(false);
            console.log("QueryMonthRecord:" + JSON.stringify(Utils.parseError(err)))
            // alert("QueryMonthRecord:" + JSON.stringify(Utils.parseError(err)));
        });
    }

    _setDateString(dataAry,month){
        let dateAry = {};
        console.log(dataAry);
        for (let m = 0; m < dataAry.length; m++){
            let day = new Date(month.substr(0,4), parseInt(month.substr(4,2))-1, m+1);
            let disabled = parseInt(dataAry[m])?false:true;
            let seleteDay = new Date(this.state.currentDate).getDate();
            let seleteMonth = new Date(this.state.currentDate).getMonth()+1;
            if (seleteMonth != parseInt(month.substr(4,2)) || seleteDay != m+1){
                dateAry[moment(day).format('yyyy-MM-DD')]={disabled: disabled, disableTouchEvent: disabled, };
            }
        }
        this.setState({dateData:dateAry});
    }


    componentWillUnmount(){
        this.backHandler && this.backHandler.remove();

    }

    componentDidMount(){


    }


    render() {
        return (<>
            <NavigationBar
                type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                title={stringsTo('play_back_tit')}
                left={[{key: NavigationBar.ICON.BACK, onPress: () => {
                        this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
                    }}]}
                // right={[{
                //     key: NavigationBar.ICON.CUSTOM,
                //     n_source:require('../../resources/images/icon_edit.png'),
                //     onPress: _ => {
                //         this.setState({isEdit:true,numAry:[],isSelectAll:false})
                //         // if (this.state.length) this.setState({isEdit:true,numAry:[],isSelectAll:false});
                //     }
                // }]}
            />

            {/*{!this.state.fullScreen ?*/}
            {/*    <RNLine style={{height: 1}}/>*/}
            {/*    :*/}
            {/*    null}*/}
            {this._renderHeader()}
            {this._renderDayFiles()}

            {this._renderShowCalender()}

        </>)
    }

    _renderHeader() {
        return(<XView style={{flexDirection:'row',height: 50,width:screen_width,backgroundColor:'yellow',}}>
            <XView style = {{flex:1,width:screen_width/2}}>
                  <XText style={{width:screen_width/2-14,marginLeft:14,lineHeight:56,fontSize:15,color:'#7F7F7F',textAlign: 'left',}} text={dateFormat(this.state.currentDate,stringsTo("date_format_yyyy_mm_dd"))}/>
            </XView>

            <XView style = {{flex:1,}}>
                <XView style={{marginRight:14,width:150,height: 50,flexDirection:'row',}} onPress={() => {
                console.log('点击切换时间')
                    this._queryMonthData(moment(this.state.currentDate).format('yyyyMM'));
                    this.setState({isShowCalendar:!this.state.isShowCalendar})
                }}>
                    <XText style={{width:130,lineHeight:50,fontSize:15,color:'#7F7F7F',textAlign: 'right',}} text={stringsTo('play_back_change_time')}/>
                    <XImage style={{ width: 20, height: 20,marginTop:13}}
                            source={require('../../resources/images/icon_show_down.png')}></XImage>
                </XView>
            </XView>
        </XView>)
    }

    _renderDayFiles() {
        // if (this.state.isEmpty || this.state.isCurrentDayEmpty) {
        //     return null;
        // }
        return (
            // <View style={styles.container}>
                <FlatList
                    style={{ flexGrow: 1,marginTop:4,backgroundColor:'purple' }}
                    data={this.state.playData}
                    renderItem={({ item, index }) => this._renderDayItem(item, index)}
                    numColumns={3}
                    keyExtractor={(item, index) => index.toString()}
                    ListFooterComponent={<View style={{ height: 30,backgroundColor:'red'}}></View>}
                    // contentContainerStyle={styles.list_container}
                />
            // </View>
        )
    }

    _renderDayItem(item,index) {
        //let screenWidth = Dimensions.get('window').width;
        // let screenWidth = Dimensions.get('window').width>Dimensions.get('window').height ? Dimensions.get('window').height : Dimensions.get('window').width;

        let containerWidth = (screen_width-14*4) / 3
        let containerHeight = containerWidth * 0.8;
        //  86/108 = 0.8


        return (
            <TouchableOpacity
                onPress={() => {
                    // 进入具体某一时间页面
                    this.props.navigation.push("HourSDCardPage");
                }}
            >
           <View style = {{ width: containerWidth,marginLeft:14,marginBottom:6,height: containerHeight,backgroundColor: item.index == 1 ? 'green':'orange',}}>
               <View style = {{width: "100%",marginTop:6,height: containerHeight-6*2-17,position: "relative",backgroundColor:'#F9F9F9'}}>
                   <Image style={{ width: "100%",height: "100%",borderRadius: 4 }}
                          source={require('../../resources/images/full_color_img.png')}
                   >
                   </Image>

                   <Image
                       style={{ width: 21, height: 21, position: "absolute", bottom: 0, right: 0 }}
                       source={require("../../resources/images/play_hour_time.png")}
                   />
            </View>

                <View style={{backgroundColor:'yellow'}}>
                    <Text style={{ marginTop: 6, width:containerWidth,lineHeight:17, fontSize: 12, color: "#333333",textAlign:'left',backgroundColor:'green'}}
                    >{'12:00'}
                    </Text>
                </View>

           </View>
            </TouchableOpacity>
       )
    }

     //显示日历控件
    _renderShowCalender() {
        if (!this.state.isShowCalendar){
            return ;
        }
        return (
            // <View style={{ position: "absolute", top:isIphoneXSeries()?85+50:75+50, height: (screen_height - 50), width: '100%',backgroundColor:'#656665'}}>
            <ModalView style={{ position: "absolute", top:isIphoneXSeries()?85+50:75+50, height: (screen_height - 50), width: '100%',backgroundColor:'#656665'}} visible={this.state.isShowCalendar} onClose={()=>{this.setState({isShowCalendar:false})}}>
            <Calendar
                    style={{width:'100%'}}
                    onDayPress={(day) => {
                        console.log('AlarmListPlayerPage onDayPress pressed formatDate' + day.dateString);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        let ddd = new Date(day.year,day.month-1,day.day, 0, 0, 0);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        console.log('ddd===',ddd,day.dateString);
                        // 刷新完成后调用确保取值正确
                        this.setState({isShowCalendar:false}, () => {
                            showLoading(stringsTo('commLoadingText'),true);
                            this._queryDayData(ddd).then(dataArr => {
                                showLoading(false);
                                if (dataArr.length>=1){
                                    // currentPlayTime = 0;
                                    this.resetSelectDay(day.dateString);
                                    this.setState({dataArray: dataArr,currentDate: ddd,},()=>{
                                        // this.IMIVideoView.prepare();
                                        // this.preparetimer && clearTimeout(this.preparetimer);
                                        // this.preparetimer=setTimeout(() =>{
                                        //     this.IMIVideoView.start()
                                        // }, 2000)
                                    });
                                } else {
                                    showToast(stringsTo('commLoadingFailText'));
                                }

                            });
                        });

                        // this.setState({isShowCalendar:false});

                    }}

                    onMonthChange={(day) => {
                        // this.setState({isShowCalendar:false});
                        console.log('AlarmListPlayerPage onMonthChange pressed' + day.month)
                        let ary = day.dateString.split('-');
                        let month = ary[0]+ary[1];
                        showLoading(true);
                        this._queryMonthData(month);
                    }}
                    hideArrows={false}
                    hideExtraDays={true}
                    maxDate={new Date()}
                    markedDates={{
                        [this.state.dateTime]: {
                            selected: true,
                            marked: false,
                            disableTouchEvent: true,
                            selectedColor: imiThemeManager.theme.primaryColor
                        },...this.state.dateData
                    }
                    }
                    theme={{
                        todayTextColor: imiThemeManager.theme.primaryColor,
                    }}
                />
            </ModalView>
        )
    }

    _onDayPress() {
        console.log('_onDayPress');
        console.log('press---',this.state.currentDate)

        // let m = this.state.dateTime.substring(5, 7);
        // let d = this.state.dateTime.substring(8);
        // this.topSelectBarRoot.funOnPress(m + "/" + d);
        //
        // this.props.onDayPress && this.props.onDayPress(this.state.dateTime)
    }

    resetSelectDay(day){
        this.state.dateData[moment(this.state.currentDate).format('yyyy-MM-DD')] = {disabled: false, disableTouchEvent: false };
        this.state.dateData[day] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
    }

}

const styles = StyleSheet.create({

    tab: {
        flex: 1,
        textAlign: 'center',
        color: colors.gray,
        fontSize: 15,
        textShadowColor: imiThemeManager.theme.primaryColor,
    },
    videoPlayer: {
        position: 'absolute',
        top: 44,
        left: 0,
        bottom: 0,
        right: 0,
    },
    barText:{
        color:'#333333',
        fontSize:17,
        textAlign: 'center'
    },

});
