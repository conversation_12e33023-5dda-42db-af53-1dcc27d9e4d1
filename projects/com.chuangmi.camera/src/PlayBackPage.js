import React, {Component} from 'react';
import {IMIStorage, LetDevice} from '../../../imilab-rn-sdk';

import PlayBackPagePlayerComponent from './playBack/PlayBackPagePlayerComponent/PlayBackPagePlayerComponent';
import {stringsTo} from '../../../globalization/Localize';
import {colors, showToast, RoundedButtonView} from '../../../imilab-design-ui';

import I18n from '../../../globalization/Localize';
import {XText, XView} from 'react-native-easy-app';
import {
  TouchableOpacity,
  Text,
  Dimensions,
  View
} from 'react-native';
import DoubleVodDemo from '../../Demo/chuangmi.argus.demo/DoubleVodDemo';

const {width, height} = Dimensions.get('window');
/**
 * PlayBackPage
 * @author: yanmin
 * @date: 2020/12/28
 */
export default class PlayBackPage extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isFullScreen: false,
      mute: true,
      recording: false,
      recordDuration: 0,
      showFullScreenTools: false,
      lensCorrect: false,
      isSleep: false, //设备是否休眠
      isOnline: this.props.route.params.isOnLine,
      isPlay: this.props.route.params.isOnLine,
      uniqueKey: 1,
      needForce: true,
    };
    this.isForegroundPage = true;
  }

  componentDidMount() {
    this._addListener();
  }

  _getSameData() {
    IMIStorage.load({
      key: LetDevice.deviceID + 'DistortionCorrection',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({lensCorrect: res.lensCorrect});
      })
      .catch(_ => {
        this.setState({lensCorrect: false});
      });
    LetDevice.getSingleProperty('10001')
      .then(data => {
        //0休眠 1关闭
        console.log('设备休眠--------SleepStatus' + data[0].value);
        this.setState({isSleep: !data?.value?.value});
      })
      .catch(error => {
        console.log('error111');
      });
  }

  _addListener() {
    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      console.log('进入前台了-----------------------------')
      //设备在线，并且是从后台返回到前台的时候，我们去恢复播放器
      //第一次进来不初始化                                 //本地直连模式不走此逻辑，在PlayBackPagePlayerComponent中处理
      if (this.props.route.params.isOnLine && !this.isForegroundPage && !LetDevice.isLocalDevice) {
        //this.IMIVideoView && this.IMIVideoView.resume();
      }
      this._getSameData();
      this.isForegroundPage = true;
      this.setState({isOnline: this.props.route.params.isOnLine});
      // console.log('focus回看全屏状态',this.leaveFullScreen);
      // if (this.leaveFullScreen){
      //this.playBackComponent && this.playBackComponent.quitFullScreen();
      // NavigationBar.setBarStyle('light-content');

      // }

      if (!this.state.needForce) {
        this.playBackComponent.emptyVideo()
        console.log('======================我强制更新了')
        this.setState({
          uniqueKey: this.state.uniqueKey + 1,
          needForce: true
        })
        return;
      }
    });

    this._unsubscribe = this.props.navigation.addListener('state', e => {
      const routes = e.data?.state?.routes;
      // 只有进入回看页面才不停止播放，不然就停止播放
      if (routes?.length > 1) {
        let needStop = true;
        routes.map(res => {
          if (res.name === 'BackVideo') {
            needStop = false;
            this.setState({
              needForce: needStop,
            })
          }
        })
      }
    });
    // this.devicePropertyListener = LetIProperties.addPropertyChangeListener(event => {
    //   console.log('event-----------------', event);
    //   let data = typeof event === 'object' ? event : JSON.parse(event);

    //   if (data.SleepStatus != undefined) {
    //     if (data.SleepStatus == '0' || data.SleepStatus == 0) {
    //       this.setState({isSleep: true});
    //       this.IMIVideoView && this.IMIVideoView.stop();
    //     } else {
    //       this.setState({isSleep: false});
    //       this.IMIVideoView && this.IMIVideoView.prepare();
    //     }
    //   }
    //   if (data.items) {
    //     if (data.items.SleepStatus) {
    //       if (data.items.SleepStatus.value == '0' || data.items.SleepStatus.value == 0) {
    //         this.setState({isSleep: true});
    //         this.IMIVideoView && this.IMIVideoView.stop();
    //       } else {
    //         this.setState({isSleep: false});
    //         this.IMIVideoView && this.IMIVideoView.prepare();
    //       }
    //     }
    //   }
    // });
    // this.deviceInfoListener = LetDevice.addInfoChangeListener(info => {
    //   let data = typeof info === 'object' ? info : JSON.parse(info);
    //   console.log('info----------' + JSON.stringify(data));
    //   if (data.key == 'isOnline') {
    //     this.setState({isOnline: data.value});
    //     if (data.value == false) {
    //       this.IMIVideoView && this.IMIVideoView.stop();
    //     } else {
    //       this.IMIVideoView && this.IMIVideoView.prepare();
    //     }
    //   }
    //   // if (data.thingType == 'DEVICE'){
    //   //     if (data.status){
    //   //         this.setState({isOnline:data.status.value});
    //   //         if (data.status.value == false){
    //   //             this.IMIVideoView&&this.IMIVideoView.stop();
    //   //         }else {
    //   //             this.IMIVideoView&&this.IMIVideoView.prepare();
    //   //         }
    //   //     }
    //   // }
    // });

    // this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
    //
    //     this.IMIVideoView&&this.IMIVideoView.stop();
    // });
    //
    // this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(()=>{
    //     if (!this.isForegroundPage)return;
    //     this.IMIVideoView&&this.IMIVideoView.prepare();
    // });

    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      this.isForegroundPage = false;
      // this.IMIVideoView && this.IMIVideoView.stop(); //跳转其他页面，暂停播放，回来后再resume()
      if (this.playBackComponent?.getIsView()) {
        this.IMIVideoView && this.IMIVideoView.stop();
      } else {
        console.log('===========', this.IMIVideoView)
        this.IMIVideoView && this.IMIVideoView.destroy && this.IMIVideoView.destroy();
      }
      // this.leaveFullScreen = this.playBackComponent.getFullscreen();
      // console.log('获取回看全屏状态---',this.playBackComponent.getFullscreen(),this.leaveFullScreen);
    });
  }

  componentWillUnmount() {
    console.log('===========注销了')
    try {
      // this._subscribe_focus && this._subscribe_focus();
      this.devicePropertyListener && this.devicePropertyListener.remove();
      this.deviceInfoListener && this.deviceInfoListener.remove();
      this._subscribe_blur && this._subscribe_blur();
      // this._enterBackground&&this._enterBackground.remove();
      // this._enterForeground&&this._enterForeground.remove();
    } catch (error) {
      console.log('异常卸载了-------')
    }
  }
  _exitFullScreen = () => {
    console.log('退出全屏');
  };

  render() {
    let showPlayBackGridEvent = this.props.route.params.showPlayBackGridEvent;
    console.log('showPlayBackGridEvent', showPlayBackGridEvent, this.props);
    // return <>
    // <DoubleVodDemo/>
    // </>
    return (
      <>
        <PlayBackPagePlayerComponent
          uniqueKey={this.state.uniqueKey}
          {...this.props}
          ref={component => (this.playBackComponent = component)}
          videoRef={ref => (this.IMIVideoView = ref)}
          onVodPlayerStatusChange={(status, speed) => this._onVodPlayerStatusChangeListener(status, speed)}
          videoSubView={(isFullScreen, showFullScreenTools) => this.renderVideoSubView(isFullScreen, showFullScreenTools)}
          isSleepStatus={this.state.isSleep}
          isOnLine={this.state.isOnline}
          albumName={LetDevice.deviceID}
        />
      </>
    );
    // return showPlayBackGridEvent ? (
    //   <PlayBackGridPagePlayerComponent
    //     {...this.props}
    //     ref={component => (this.playBackComponent = component)}
    //     videoRef={ref => (this.IMIVideoView = ref)}
    //     onVodPlayerStatusChange={status => this._onVodPlayerStatusChangeListener(status)}
    //     // lensCorrect={{use: this.state.lensCorrect, x: lensCorrect_x, y: lensCorrect_y}}
    //     videoSubView={(isFullScreen, showFullScreenTools) => this.renderVideoSubView(isFullScreen, showFullScreenTools)}
    //     isSleepStatus={this.state.isSleep}
    //     isOnLine={this.state.isOnline}
    //   />
    // ) : (
    //   <PlayBackPagePlayerComponent
    //     {...this.props}
    //     ref={component => (this.playBackComponent = component)}
    //     videoRef={ref => (this.IMIVideoView = ref)}
    //     // navBar={(bps, isFullScreen) => this._renderNavigationBar(bps, isFullScreen)}
    //     // navBarRight={[
    //     //   { //回看列表页面
    //     //     key: NavigationBar.ICON.CUSTOM,
    //     //       n_source:require('../resources/images/icon-play-list.png'),
    //     //     onPress: _ => {
    //     //         // if (LetDevice.isShareUser){
    //     //         //     showToast(stringsTo('shareUser_tip'));
    //     //         //     return ;
    //     //         // }
    //     //         this.props.navigation.navigate("AllSdcardPage");
    //     //     }
    //     // }
    //     // ]}
    //     onVodPlayerStatusChange={status => this._onVodPlayerStatusChangeListener(status)}
    //     // lensCorrect={{use: this.state.lensCorrect, x: lensCorrect_x, y: lensCorrect_y}}
    //     videoSubView={(isFullScreen, showFullScreenTools) => this.renderVideoSubView(isFullScreen, showFullScreenTools)}
    //     isSleepStatus={this.state.isSleep}
    //     isOnLine={this.state.isOnline}
    //   />
    // );
  }

  renderVideoSubView(isFullScreen, showFullScreenTools) {
    //竖屏时没有要显示的控件，竖屏显示拨号键、云台
    return (
      <XView
        style={{width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center', position: 'absolute'}}
        pointerEvents={'box-none'}>
        {this._sleepView()}
        {this._deviceOffLineView()}
      </XView>
    );
  }

  //休眠提示
  _sleepView() {
    if (!this.state.isSleep) {
      return null;
    }
    return (
      <XView
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={stringsTo('power_off')}
        />

        <RoundedButtonView
          buttonText={stringsTo('wake_up')}
          buttonStyle={{
            margin: 14,
            width: 110,
            height: 40,
          }}
          onPress={() => {
            if (LetDevice.isShareUser) {
              showToast(stringsTo('shareUser_tip'));
              return true;
            }

            const paramJson = JSON.stringify({msg_id: '10001', value: true});
            console.log('下发参数', paramJson);
            LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
              .then(() => {
                this.setState({isSleep: 0}, () => {
                  //this.IMIVideoView && this.IMIVideoView.prepare();
                });
              })
              .catch(() => {
                this.setState({isSleep: 1});
                showToast(I18n.t('operationFailed'));
              });
          }}
        />
      </XView>
    );
  }

  //休眠提示 健壮维护离线视频播放
  _deviceOffLineView() {
    if (this.state.isOnline) {
      return null;
    } else {
      if (this.state.isPlay) {
        return null;
      }
    }
    return (
      <XView
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 13,
          }}
          text={`${stringsTo('device_offline_aready')}  ${stringsTo('offlineTime')}${LetDevice.offlineTime}`}
        />
      </XView>
    );
  }

  _onVodPlayerStatusChangeListener(status, speed) {
    // 从图片预览过来
    if (this.playBackComponent.getIsView()) {
      return
    }
    if (status == PlayBackPagePlayerComponent.VOD_PLAYER_STATUS.PREPARED) {
      console.log(222222, status);

      this.IMIVideoView.start();
      switch (speed) {
        case 0:
          this.IMIVideoView && this.IMIVideoView.speed(1);
          break;
        case 1:
          this.IMIVideoView && this.IMIVideoView.speed(4);
          break;
        case 2:
          this.IMIVideoView && this.IMIVideoView.speed(8);
          break;
        case 3:
          this.IMIVideoView && this.IMIVideoView.speed(16);
          break;
      }
    } else if (status == PlayBackPagePlayerComponent.VOD_PLAYER_STATUS.PLAYING) {
      if (this.state.isPlay !== true) {
        this.setState({
          isPlay: true,
        });
      }
    } else if (
      this.state.currentStatus == PlayBackPagePlayerComponent.VOD_PLAYER_STATUS.ERROR &&
      status == PlayBackPagePlayerComponent.VOD_PLAYER_STATUS.PAUSE
    ) {
      if (this.state.isPlay !== false) {
        this.setState({
          isPlay: false,
        });
      }
    }
  }

  //
  // _onPressBack = (isFullScreen) => {
  //     if (isFullScreen) {
  //         Orientation.lockToPortrait();
  //         this.props.navigation.setOptions({tabBarVisible: true});
  //     } else {
  //         this.props.navigation.goBack();
  //     }
  // };
}
