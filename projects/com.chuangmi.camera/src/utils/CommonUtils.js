import {LetDevice, LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import GlobalUtil from './GlobalUtil';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';

export default class CommonUtils {
  /**
   * 格式化时间数据
   * @param ml 毫秒数
   * return 分秒或者时分秒
   */
  static formatTime(ml) {
    let seconds = ml / 1000;
    let hourInt = parseInt(seconds / 3600);
    let minuteInt = parseInt((seconds % 3600) / 60);
    let secondInt = parseInt((seconds % 3600) % 60);
    let minuteString = minuteInt > 9 ? minuteInt : `0${minuteInt}`;
    let secondString = secondInt > 9 ? secondInt : `0${secondInt}`;
    let hourString = hourInt > 9 ? hourInt : `0${hourInt}`;
    if (hourInt > 0) {
      return `${hourString}:${minuteString}:${secondString}`;
    }
    return `${minuteString}:${secondString}`;
  }

  /**
   * 一维数据分组
   * @param array 元数据
   * @param subGroupLength 子数组长度
   * @returns [[],[]] 分组后数据
   */
  static groupArr(array, subGroupLength) {
    let index = 0;
    let newArray = [];
    while (index < array.length) {
      newArray.push(array.slice(index, (index += subGroupLength)));
    }
    return newArray;
  }

  /**
   * 比较固件版本，是否支持倒序返回数据
   * sdDataReverseVersion 用于标记该项目是否支持SD卡数据倒序返回
   * 从此版本开始，固件开始支持倒序返回数据 不同项目此版本号会有所不同
   */
  static checkIsSDRequestReverse() {
    let goalVersion = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model).sdDataReverseVersion;
    if (!goalVersion) {
      GlobalUtil.needReverse = false;
      return;
    }

    const params = {
      Path: '/thing/info/get',
      APIVersion: '1.0.4',
      ParamMap: {
        iotId: LetDevice.deviceID,
        productKey: LetDevice.model,
      },
    };

    LetIMIIotRequest.sendIotServerRequest(params)
      .then(data => {
        console.log(' getFirmwarePlayBackGridEventVersion  then->' + data);

        data = JSON.parse(data);

        let currentVersion = data.firmwareVersion;
        // let goalVersion = "060101_2.3.2_0159";//大于该版本

        if (this.compareVersion(goalVersion, currentVersion)) {
          GlobalUtil.needReverse = true;
        } else {
          GlobalUtil.needReverse = false;
        }
      })
      .catch(error => {
        console.log('getFirmwarePlayBackGridEventVersion error ' + error);
        //获取异常也使用新方案请求
        GlobalUtil.needReverse = true;
      });
  }

  static isEmpty(obj) {
    if (typeof obj === 'undefined' || obj == null || obj === '') {
      return true;
    } else {
      return false;
    }
  }

  static compareVersion(curVersion, newVersion) {
    if (this.isEmpty(newVersion) || this.isEmpty(curVersion)) {
      return false;
    } else if (newVersion.equals(curVersion)) {
      return false;
    }

    let newSpilUnderLine = newVersion.split('_');
    let curSpilUnderLine = curVersion.split('_');
    if (newSpilUnderLine.length < 3 || newSpilUnderLine.length < 3) {
      return false;
    }
    let newSpilComma = newSpilUnderLine[1].split('.');
    let curSpilComma = curSpilUnderLine[1].split('.');
    if (newSpilComma.length < 3 || curSpilComma.length < 3) {
      return false;
    }

    let newNum =
      parseInt(newSpilComma[0]) * 1000 +
      parseInt(newSpilComma[1]) * 300 +
      parseInt(newSpilComma[2]) * 10 +
      parseInt(newSpilUnderLine[2]);
    let curNum =
      parseInt(curSpilComma[0]) * 1000 +
      parseInt(curSpilComma[1]) * 300 +
      parseInt(curSpilComma[2]) * 10 +
      parseInt(curSpilUnderLine[2]);
    if (newNum > curNum) {
      return true;
    } else {
      return false;
    }
  }
  static deepEqual(obj1, obj2) {
  if (obj1 === obj2) {
    return true;
  }
  
  if (obj1 == null || obj2 == null) {
    return obj1 === obj2;
  }
  
  if (typeof obj1 !== typeof obj2) {
    return false;
  }
  
  if (typeof obj1 !== 'object') {
    return obj1 === obj2;
  }
  
  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false;
  }
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) {
    return false;
  }
  
  for (let key of keys1) {
    if (!keys2.includes(key)) {
      return false;
    }
    
    if (!this.deepEqual(obj1[key], obj2[key])) {
      return false;
    }
  }
  
  return true;
}
}
