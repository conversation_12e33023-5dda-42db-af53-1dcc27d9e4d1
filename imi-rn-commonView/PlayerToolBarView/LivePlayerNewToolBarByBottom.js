/**
 * LivePlayerToolBarView.js 直播的toolbar
 *
 * @property {func} sleepPress - 休眠点击事件
 * @property {boolean} sleepDisabled - 是否可点击
 *
 * @property {string} qualityTitle - 清晰度文案
 * @property {func} qualityPress - 清晰度点击事件
 * @property {boolean} qualityDisabled - 清晰度是否可点击
 *
 * @property {func} mutePress - 静音点击事件
 * @property {boolean} muteDisabled - 静音是否可点击
 * @property {boolean} mute - 是否静音
 *
 * @property {func} fullscreenPress - 全屏点击事件
 * @property {boolean} fullscreenDisabled - 是否可点击
 *
 * @property {array} moreItems - 更多自定义按钮 参考[{item:{isText: true, data: ["流畅"], onPress: null, disabled: true,dataIndex:0},insertIndex:0},]
 *
 * 示例：
 * <LivePlayerToolBarView
 *  qualityData={this.state.qualityData}
 *  qualityIndex={this.state.qualityIndex}
 *  fullscreenPress={this._onPressFullScreen}
 *  screenshotPress={this._onPressScreenshot}
 *  recordPress={this._onPressRecord}
 *  recording={this.state.recording} />
 *
 * <AUTHOR>
 * @date 2020/11/24
 */

import React, {Component} from 'react';
import PlayerToolBarView from './PlayerToolBarView';
import PlayerNewToolBarView from './PlayerNewToolBarView';
import PropTypes from 'prop-types';
import {stringsTo} from '../../globalization/Localize';

export default class LivePlayerNewToolBarByBottom extends Component {
  static propTypes = {
    // sleepPress: PropTypes.func,
    // sleepDisabled: PropTypes.bool,
    // sleep: PropTypes.bool,
    qualityTitle: PropTypes.string,
    qualityPress: PropTypes.func,
    qualityDisabled: PropTypes.bool,
    accessibilityLabel: PropTypes.string,
    mutePress: PropTypes.func,
    muteDisabled: PropTypes.bool,
    mute: PropTypes.bool,

    screenshotPress: PropTypes.func,
    screenshotDisabled: PropTypes.bool,

    fullscreenPress: PropTypes.func,
    fullscreenDisabled: PropTypes.bool,

    moreItems: PropTypes.array,
    isShowPanoramicView: PropTypes.bool, //圆盘还是通话显示,056/059
    isCallingStatue: PropTypes.bool, //是否点击通话
  };

  static defaultProps = {
    // sleepPress: null,
    // sleepDisabled: false,
    // sleep: false,

    qualityTitle: '流畅',
    accessibilityLabel: 'home_page_clarity_show_low',
    qualityPress: null,
    qualityDisabled: false,

    mutePress: null,
    muteDisabled: false,
    mute: true,

    fullscreenPress: null,
    fullscreenDisabled: false,

    moreItems: [],
    isCallingStatue: false, //是否点击通话
  };

  constructor(props, context) {
    super(props, context);
    this.state = {};
  }

  render() {
    
    let items = [
      {
        isText: false,
        data: [require('./res/player_toolbar_mute1.png'), require('./res/landscape_volume1.png')],
        onPress: this.props.mutePress,
        disabled: this.props.muteDisabled,
        dataIndex: this.props.mute ? 0 : 1,
        accessibilityLabel: ['home_page_voice_off', 'home_page_voice_on'],
      },
      {
        isText: true,
        data: [this.props.qualityTitle],
        onPress: this.props.qualityPress,
        disabled: this.props.qualityDisabled,
        dataIndex: 0,
        isCallingStatue: this.props.isCallingStatue,
        accessibilityLabel: [this.props.accessibilityLabel],
      },

      {
        isText: false,
        data: [require('./res/icon_live_full.png')],
        onPress: this.props.fullscreenPress,
        disabled: this.props.fullscreenDisabled,
        dataIndex: 0,
        isCallingStatue: this.props.isCallingStatue,
        accessibilityLabel: ['home_page_full_screen'],
      },
    ];
    if (this.props.moreItems.length > 0) {
      this.props.moreItems.forEach((item, index) => {
        if (item.insertIndex < items.length) {
          items.splice(item.insertIndex, 0, item.item);
        } else {
          items.push(item.item);
        }
      });
    }

    return <PlayerNewToolBarView items={items} />;
  }
}
