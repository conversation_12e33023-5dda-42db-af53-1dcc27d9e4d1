/**
 * PlayerToolBarView.js 播放器的toolbar
 *
 * @property {array} items - 按钮集合[{
 *      isText:bool-是否是文字按钮,
 *      data:array-文案/图片,
 *      onPress:func-点击事件,
 *      disabled:bool-是否可点击,
 *      dataIndex:当前文案/图片序号
 * }]
 *
 * 示例：
 * <PlayerToolBarView
 * items={[
 *  {isText: true, data: ["流畅"], onPress: null, disabled: true,dataIndex:0},
 *  {isText: false, data: [require("./res/player_toolbar_volume.png")], onPress: null, disabled: true,dataIndex:0}
 * ]}/>
 *
 * <AUTHOR>
 * @date 2020/11/24
 */

import React, {Component} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';

import PropTypes from 'prop-types';
import TouchableOpacityText from '../TouchableOpacityText/TouchableOpacityText';
import TouchableOpacityImage from '../TouchableOpacityImage/TouchableOpacityImage';
import {XText} from 'react-native-easy-app';
import {isAndroid, isIos} from '../../imilab-rn-sdk/utils/Utils';

export default class PlayerToolBarView extends Component {
  static propTypes = {
    items: PropTypes.array,
    isShowLine: PropTypes.bool,
  };

  static defaultProps = {
    items: [],
    isShowLine: true,
  };

  constructor(props, context) {
    super(props, context);
    this.state = {};
  }

  render() {
    return (
      <View style={{height: 68, width: '100%', flexDirection: 'row', backgroundColor: '#FFFFFF', position: 'relative', borderBottomLeftRadius: 8, borderBottomRightRadius: 8}}>
        {this.props.items.map((item, index) => {
          return item.isText ? (
            <TouchableOpacity
              key={`playerToolBarItem${index}`}
              style={{flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: item.bottomTitle ? 4 : 0}}
              disabled={item.disabled}
              onPress={item.onPress}
              accessibilityLabel={item.accessibilityLabel ? item.accessibilityLabel[item.dataIndex] : ''}>
              <View style={{alignItems: 'center', justifyContent: 'center'}}>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: 1.67,
                    borderRadius: 2.67,
                    borderColor: item.disabled ? '#33333333' : '#333333',
                    minHeight: 20,
                    minWidth: 30,
                    paddingVertical: isAndroid() ? 0 : 3, //Android会自动给字体加margin,为了保证双端看起来高度一致，分别处理
                    paddingHorizontal: 2,
                  }}>
                  <XText
                    style={{
                      color: item.disabled ? '#7F7F7F33' : '#7F7F7F',
                      fontSize: 11,
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontFamily: isIos() ? null : '',
                    }}
                    text={item.data[item.dataIndex]}
                  />
                </View>
                {item.hasOwnProperty('bottomTitle') && item.bottomTitle ? (
                  <Text
                    style={{
                      fontSize: 10,
                      color: item.disabled ? '#7F7F7F33' : '#000',
                      marginTop: 4,
                      fontFamily: isIos() ? null : '',
                    }}>
                    {item.bottomTitle}
                  </Text>
                ) : null}
              </View>
            </TouchableOpacity>
          ) : (
            <TouchableOpacityImage
              key={`playerToolBarItem${index}`}
              style={{flex: 1, justifyContent: 'center', alignItems: 'center', opacity: item.disabled ? 0.2 : 1}}
              onPress={item.onPress}
              disabled={item.disabled}
              imageStyle={{width: 40, height: 40, opacity: item.isCallingStatue && item.isCallingStatue ? 0.8 : 1}}
              source={item.data[item.dataIndex]}
              accessibilityLabel={item.accessibilityLabel ? item.accessibilityLabel[item.dataIndex] : ''}
              title={item.hasOwnProperty('bottomTitle') ? item.bottomTitle : null}
            />
          );
        })}
      </View>
    );
  }
}
