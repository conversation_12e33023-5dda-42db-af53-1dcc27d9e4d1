/**
 * HomePageLivePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 * @property {string}  pageBackgroundColor 直播播放器下方空白页面的颜色
 *
 * 示例:
 * <HomePageLivePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </HomePageLivePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/11/25
 */

import React, {Component} from 'react';
import {
  View,
  Text,
  BackHandler,
  ActivityIndicator,
  StyleSheet,
  TouchableWithoutFeedback,
  Modal,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
    IMIGotoPage,
    LetDevice,
    IMIPackage,
    IMIStorage,
    IMILog,
    PlayerClass,
    IntercomModeKey, INTERCOM_MODE
} from '../../imilab-rn-sdk';
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from '../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView';

import IMIFile from '../../imilab-rn-sdk/native/local-kit/IMIFile';
import I18n, {stringsTo, locales} from '../../globalization/Localize';
import LivePlayerToolBarView from '../PlayerToolBarView/LivePlayerToolBarView';
import NavigationBar from '../NavigationBar/NavigationBar';
import Orientation from 'react-native-orientation';
import TouchableOpacityText from '../TouchableOpacityText/TouchableOpacityText';
import ModalView from '../ModalView/ModalView';
import {getScreenWidth, isAndroid, isIos, isIphoneXSeries} from '../../imilab-rn-sdk/utils/Utils';
import PropTypes from 'prop-types';

import IMIToast from '../../imilab-design-ui/src/widgets/IMIToast';

import LivePlayerFullScreenToolBarView from '../PlayerToolBarView/LivePlayerFullScreenToolBarView';
import {XText} from 'react-native-easy-app';
import {colors, RoundedButtonView} from '../../imilab-design-ui';
import ImageButton from '../ImageButton/ImageButton';

import IMIPermission from '../../imilab-rn-sdk/native/local-kit/IMIPermission';
import {showToast} from '../../imilab-design-ui/src/widgets/Loading';
import Toast from 'react-native-root-toast';
import * as Typography from '../../imilab-design-ui/src/style/Typography';
import {timeFilter} from '../../imilab-rn-sdk/utils/DateUtils';

import IMILogUtil from '../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import moment from 'moment';
import {IMINativeLifeCycleEvent} from '../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import DeviceTemplatesUtils from '../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import {CONST} from '../../imilab-design-ui/src/style/ThemeManager';

import IMP2pClient from '../../imilab-rn-sdk/native/local-kit/IMP2pClient';

import LivePlayerNewToolBarByBottom from '../PlayerToolBarView/LivePlayerNewToolBarByBottom';

const LIVE_PLAYER_STATUS = {
  PREPARED: 'prepared', //准备完毕，调用start即可播放
  LOADING: 'loading',
  PLAYING: 'playing', //正在播放
  PAUSE: 'pause',
  ERROR: 'error',
};
const CAMERA_ID = 0;//单摄的id
Object.freeze(LIVE_PLAYER_STATUS);
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;

const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
let lastClickSnapPhoto = 0; //上一次点击截图的时间
let canRecord = true; //允许点击录屏
let isStopping = false; //正在结束录制中
let time;
let bpsArray = []; //存放最新5次网速值的数组
let timeErr10005 = 0; // 1005连接次数错误
const {width, height} = Dimensions.get('window');
let Screen_Width = width;
let COMMON_HELP_URL = 'link://feedback/pages/toProblemFeedback'; //帮助与反馈
let COMMON_REBIND_URL = 'link://feedback/pages/toAddDeviceGuide '; //重新绑定
let StatusBarHeight = 70;

let isCheckingPermission = false;
const kWindowWidth = Math.min(width,height); // use this.winPortraitWidth instead
// const kWindowHeight = Math.min(width,height); // use this.winPortraitWidth instead
export default class HomePageLivePlayerComponent extends Component {
  static LIVE_PLAYER_STATUS = LIVE_PLAYER_STATUS;

  constructor(props, context) {
    super(props, context);
    this.state = {
      // qualityData: [ stringsTo("quality_sd"), stringsTo("quality_fhd"),stringsTo("quality_2k"),],
      qualityIndex: this.props.defaultQualityIndex,
      qualityVisible: false,
      bps: 0,
      isFullScreen: false,
      //BYH 2021年12月28日15:24:37修正 !LetDevice.category == "doorbell"-->LetDevice.category != "doorbell"
      //此判断就可以了，不用加pk限定，现在除了门铃进来打开声音其他都是默认静音
      mute: true, // 暂时先undefined,
      inBack: false,
      recording: false,
      calling: false,
      recordDuration: 0,
      isStart: false,
      showFullScreenTools: false,

      isLoading: true,
      isPlaying: false,
      showErrorView: false,
      showPauseView: false,
      errorCode: null,

      snapshotVisible: false,
      screenShotPath: null,
      screenShotPathType: 0,
      isShowErrorHelp: false, //连接失败弹框提示
      isShowOffLineHelp: false, //离线弹框
      p2pType: null,
      handleOk: false,
      positionX: 0,
      positionY: 0,
      positionShow: false,
      canShowPotion: true,
      showToolPlayer: false, //是否显示工具栏
      cruiseIng:false,// 是否正在巡航
     
    };
    this.isForegroundPage = true; //是否显示在前台
    this.isNewFullScreen = false; // 从后台进前台是否全屏
    this.macAddress = ''; //重新绑定、重新连接跳到配网页面，需要从个Mac地址过去
    this.currentSnapshotPath = ''; // 保存的截图名称
    this.getStatusBarHeight();
    this.leaveStatus = false;
    this.initBps = 0;
    this.currentMute = true;
    this.firstBps = -1;
    this.first10011 = false
    this.showToolPlayerTimer = null; // 显示工具栏的定时器
    
  }

  static propTypes = {
    showNavBar: PropTypes.bool,
    showPlayItem: PropTypes.bool,
    navBar: PropTypes.func,
    videoRef: PropTypes.func,
    navBarRight: PropTypes.array,

    toolBarMoreItems: PropTypes.array,
    videoSubView: PropTypes.func,

    coverView: PropTypes.func,
    loadingView: PropTypes.func,
    pauseView: PropTypes.func,
    errorView: PropTypes.func,
    isSleepStatus: PropTypes.bool, //休眠状态
    isMove: PropTypes.bool, //全屏触摸转动云台
    isOnLine: PropTypes.bool, //在线状态
    isCalling: PropTypes.bool, //通话是否打开
    isShowZoomScale: false, //展示缩放比例小窗
    zoomScale: 1.0,
    onLivePlayerStatusChange: PropTypes.func,

    fullScreenToolBarMoreItems: PropTypes.array,
    lensCorrect: PropTypes.shape({
      use: PropTypes.bool.isRequired,
      x: PropTypes.number,
      y: PropTypes.number,
    }),
    dataSource: PropTypes.object,
    onVideoClick: PropTypes.func,
    pageBackgroundColor: PropTypes.string,
    qualityData: PropTypes.array, //清晰度数据
    albumName: PropTypes.string, //相册名称
    onCheckPermissionStatusChange: PropTypes.func, //回调是否处于权限检测状态中
    onVoiceCallErrorOccurred: PropTypes.func,
    componentContainerRef: PropTypes.func, //回调组件容器的引用，方便调用者以此来获取容器的宽高等参数
    onDeviceStatusChange: PropTypes.func, // 回调设备当前的状态
    isShowPanoramicView: PropTypes.bool, //圆盘还是通话显示,056/059
    isCallingStatue: PropTypes.bool, //是否点击通话
    netConnected: PropTypes.bool, //是否断网,同步双端显示
    onPrepared: PropTypes.func,
  };

  static defaultProps = {
    navBar: undefined,
    showNavBar:true,
    showPlayItem:true,
    navBarRight: [],
    toolBarMoreItems: [],
    bottomBarMoreItems: [],
    fullScreenToolBarMoreItems: [],
    lensCorrect: {use: false, x: 0, y: 0},
    dataSource: {
      iotId: LetDevice.deviceID,
    },
    qualityData: [
      {
        title: stringsTo('quality_low'),
        index: 0,
        accessibilityLabel: 'home_page_clarity_show_low',
      },
      {
        title: stringsTo('quality_sd'),
        index: 1,
        accessibilityLabel: 'home_page_clarity_show_sd',
      },
      {
        title: stringsTo('quality_fhd_3k'),
        index: 2,
        accessibilityLabel: 'home_page_clarity_show_fhd',
      },
    ],
    defaultQualityIndex: 0,
    isSleepStatus: false,
    isOnLine: true,
    isCalling: false,
    isMove: false,
    albumName: LetDevice.devNickName,
    isShowPanoramicView: false, //圆盘还是通话显示,056/059
    isCallingStatue: false, //是否点击通话
    netConnected: true, //是否断网,同步双端显示
  };

  getStatusBarHeight() {
    if (isIos()) {
      StatusBarHeight = isIphoneXSeries() ? 47 + 50 : 20 + 50;
    } else {
      StatusBarHeight = parseInt(StatusBar.currentHeight) + 50;
    }
  }

  UNSAFE_componentWillMount() {
    Orientation.lockToPortrait();

    //获取设备的mac地址
    LetDevice.getAssignedPropertyCloud('MAC')
      .then(data => {
        console.log('mac', typeof data, data);
        this.macAddress = data;
      })
      .catch(error => {
        console.log('mac' + error);
      });

    this._getStreamVideoQuality();

    this.backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      
      if (this.state.isFullScreen) {
        this._exitFullScreen();
        return true;
      }
      if (!this._canStepInCall()) {
        return true;
      }
      if (this.props.navigation.canGoBack()) {
        this.props.navigation.goBack()
        return true;
      }
      console.log('000000147885525')
      IMIGotoPage.exit();
    });

    /*本页面获得焦点*/
    this._subscribeFocus = this.props.navigation.addListener('focus', () => {
      console.log('focus');
      // Orientation.addOrientationListener(this._orientationDidChange);
    });

    /*本页面失去焦点*/
    this._subscribeBlur = this.props.navigation.addListener('blur', () => {
      console.log('blur');
      this.currentMute = this.state.mute;
      Orientation.removeOrientationListener(this._orientationDidChange);
    });

    /*退到后台*/
    this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
      if (this.leaveStatus) {
        return;
      }
      console.log('enterBackgroundListener---xy-进入后台--全屏状态', this.state.isFullScreen);
      /*if(this.state.isFullScreen){
                console.log('实时页全屏时进入后台那么直接退出全屏');
                if(isAndroid()){
                    this._exitFullScreen();
                }else{
                    this.needExitFullScreen = true;
                }
            }*/
      if (isCheckingPermission) {
        return
      }
      this.leaveStatus = true;
      this.currentMute = this.state.mute;
      if (this.state.recording) {
        //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
        console.log('录制中进入台---------', this.state.recording);
        this._stopRecord();
      }
      this.IMIVideoView && this.IMIVideoView.setIsMute(true);
      this.setState({mute: true, inBack: true, handleOk: false});
    });

    // ios开声音进入云存购买
    this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(() => {
      if (isAndroid()) {
        return
      }
      if (this.leaveStatus) {
        return;
      }
      console.log('enterBackgroundListener---xy-进入后台--全屏状态', this.state.isFullScreen);
      /*if(this.state.isFullScreen){
                console.log('实时页全屏时进入后台那么直接退出全屏');
                if(isAndroid()){
                    this._exitFullScreen();
                }else{
                    this.needExitFullScreen = true;
                }
            }*/
      if (isCheckingPermission) {
        return
      }
      this.leaveStatus = true;
      this.currentMute = this.state.mute;
      if (this.state.recording) {
        //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
        console.log('录制中进入台---------', this.state.recording);
        this._stopRecord();
      }
      this.IMIVideoView && this.IMIVideoView.setIsMute(true);
      this.setState({mute: true, inBack: true, handleOk: false});
    });

    this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
      if (isCheckingPermission) {
        return
      }
      this.leaveStatus = false;
      // 只有不是初始化的的时候才能在进入播放时调用静音与否
      if (this.state.mute !== undefined) {
        this.IMIVideoView && this.IMIVideoView.setIsMute(true);
        this.setState({mute: true});
      }
      /*if (this.needExitFullScreen) {
                this.delayExitFullScreen = setTimeout(()=>{
                    this._exitFullScreen();
                    this.delayExitFullScreen && clearTimeout(this.delayExitFullScreen);
                },700);

            }*/
    });
    // ios开声音进入云存购买后返回
    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      if (isAndroid()) {
        return
      }
      if (isCheckingPermission) {
        return
      }
      this.leaveStatus = false;
      if (this.state.mute !== undefined) {
        this.IMIVideoView && this.IMIVideoView.setIsMute(true);
        this.setState({mute: true});
      }
    });
  }

  componentDidMount() {
    // setTimeout(() => this.IMIVideoView.start(), 2000)
    this.leaveStatus = false;
    this.props.componentContainerRef && this.props.componentContainerRef(this.componentContainer);
    this.setState({mute: this.props.leaveMute});

    this.onP2pSendStateListenerXY = IMP2pClient.onFileOperateAddListener(e => {
      if (e.iotId != LetDevice.deviceID) {
        return
      }
      try {
        const rawData = window?.atob(e.data);
      
        const result = JSON.parse(rawData?.slice(4))





       
         //&&result.method==='cruise_req'
         
        if(result.code==0&&result.method==='cruise_req'){
       
           this.setState({cruiseIng:result.value==1})
          return
        }
        if (result.p2p_cmd == 1) {
          
      console.log("移动云台11111", JSON.stringify(result));
          if(result?.operation==6){
 this.props?.getPtzAreaCallback&&this.props?.getPtzAreaCallback(result)
 return
          
          }
         
          
          IMILog.logI('移动云台1111', JSON.stringify(result))
          this.positionTimeout && clearTimeout(this.positionTimeout)
          this.positionPtzAreaTimeout && clearTimeout(this.positionPtzAreaTimeout)
          const positionX = (100 - result.position_x*1) / 2.5;
          const positionY = (100 - result.position_y*1) / 2.5;
          // console.log('移动云台============', result)
          this.setState({positionX: positionX > 39 ? 39 : positionX, positionY: positionY > 39 ? 39 : positionY, positionShow: this.state.canShowPotion});
          this.positionTimeout = setTimeout(() => {
           
            this.setState({positionShow: false});
          }, 3000);
          //因为移动时数据太多，做个防抖处理
          
            this.props?.getPtzAreaUpdate&&this.props?.getPtzAreaUpdate(result)
          
        }
      } catch (error) {
      }
     
    });
  }

  componentWillUnmount() {
    
    try {
      Orientation.removeOrientationListener(this._orientationDidChange);
    // this._subscribe_focus && this._subscribe_focus();
      this._subscribe_blur && this._subscribe_blur();
      //this.IMIVideoView?.destroy && this.IMIVideoView?.destroy();
      //this.IMIVideoView?.stop && this.IMIVideoView?.stop();

      this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
      this.backHandler && this.backHandler.remove();
      this._enterForeground && this._enterForeground.remove();
      this._enterBackground && this._enterBackground.remove();
      this._onPauseListener && this._onPauseListener.remove();
      this._onResumeListener && this._onResumeListener.remove();
      this.onP2pSendStateListener = null
      this.onP2pSendStateListenerXY = null
    } catch (error) {
      
    }
    //this.eventListener && this.eventListener.remove();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.showRTCView !== this.props.showRTCView) {
      // console.log('this.IMIVideoView-------', this.props.showRTCView, this.IMIVideoView);
      if (!this.props.showRTCView) {
        // this.IMIVideoView = null;
        // this.setState({mute: this.currentMute});
      } else {
        if (!this.props.netConnected) {
          showToast(stringsTo('network_not_connected'));
        }
        if (this.props.isFcous && !this.props.isDataUsage) {
          this.props.netConnected && this.props.isOnLine && this.IMIVideoView?.prepare();
        }
        
        this.setState({
          isPlaying: false,
          isLoading: this.props.netConnected ? true : false,
          showPauseView: false,
        });
      }
    }
    if (this.state.inBack && !this.leaveStatus && this.props.isFcous && this.props.canPlay) {
      this.setState({
        inBack: false,
      }, () => {
        this.props.netConnected && this.props.isOnLine && this.IMIVideoView?.prepare();
      });
    }
  }

  _getStreamVideoQuality() {
    LetDevice.getPropertyCloud('StreamVideoQuality')
      .then(data => {
        let selectedIndex = 0;
        for (let i = 0; i < this.props.qualityData.length; i++) {
          let qualityData = this.props.qualityData[i];
          if (qualityData.index == data) {
            selectedIndex = i;
          }
        }
        this.setState({qualityIndex: selectedIndex});
      })
      .catch(error => {
        // alert(JSON.stringify(Utils.parseError(error)));
      });
  }

  /*062设备直连模式下获取直播流画质*/
  _getStreamVideoQualityByTcp() {
    //需要在播放器发送start后才能获取到，否则返回为0
    if (LetDevice.isLocalDevice && LetDevice.isOnline) {
      LetDevice.sendLocalDeviceCommand('StreamVideoQuality', 272, 'get', JSON.stringify({})).then(res => {
        console.log('-----------' + JSON.parse(res).StreamVideoQuality.value);
        let selectedIndex = 0;
        for (let i = 0; i < this.props.qualityData.length; i++) {
          let qualityData = this.props.qualityData[i];
          if (qualityData.index == JSON.parse(res).StreamVideoQuality.value) {
            selectedIndex = i;
          }
        }
        this.setState({qualityIndex: selectedIndex});
      });
    }
  }

  //判断当前是否可以操作
  _canStepIn() {
    if (!this.props.isOnLine) {
      console.log('设备离线，不可操作');
      showToast(stringsTo('device_offline'));
      return false;
    }
    if (this.props.isSleepStatus) {
      console.log('设备休眠，不可操作');
      showToast(stringsTo('power_off'));
      return false;
    }
    if (!this.state.isPlaying) {
      console.log('直播流未开始，不可操作');
      showToast(I18n.t('onlyDoInLive'));
      return false;
    }
    return true;
  }

  _canIsCalling() {
    console.log('通话状态，state:', this.props.isCalling);
    if (this.props.isCalling) {
      showToast(stringsTo('imi_speaking_block'));
      return false;
    }
    return true;
  }

  _orientationDidChange = orientation => {
    //加此监听逻辑用来解决方向锁定关闭后，横放手机再点全屏按钮，页面显示不对的问题
    if (orientation === 'LANDSCAPE') {
      if (!this.state.isFullScreen && this.state.isPlaying) {
        //不先全屏和不加延时，直接走点击全屏按钮逻辑无法正确切换全屏
        Orientation.lockToPortrait();
        this.delayFullScreen = setTimeout(() => {
          this._onPressFullScreen();
          this.delayFullScreen && clearTimeout(this.delayFullScreen);
        }, 50);
      }
      this.props.onOrientationChange && this.props.onOrientationChange(true);
    } else {
      console.log('监听退出全屏');
      if (this.isNewFullScreen) {
        // 原来进入后台时是全屏状态，再次进入也要设置为全屏状态
        this.enterFullScreen();
        console.log('前台全屏');
      } else {
        this.state.isFullScreen && this._exitFullScreen();
      }
      this.props.onOrientationChange && this.props.onOrientationChange(false);
    }
  };

  _onPressFullScreen = () => {
    if (!this._canStepIn()) {
      return;
    }
    isAndroid() ? Orientation.lockToPortrait() : null;
    isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
    this.setState({isFullScreen: true});
    NavigationBar.setStatusBarHidden(true);
    this.props.navigation.setOptions({tabBarVisible: false});
    this._onPressFullScreenTools();
    //暂时先去掉
    //this.IMIVideoView.standardViewRatio();
    IMILogUtil.uploadClickEventForCount('FullScreen'); //统计点击全屏的情况
    this.props.onOrientationChange && this.props.onOrientationChange(true);
  };

  _exitFullScreen = () => {
    Orientation.lockToPortrait();
    this.setState({isFullScreen: false});
    NavigationBar.setStatusBarHidden(false);
    this.props.navigation.setOptions({tabBarVisible: true});
    this._onCloseFullScreenTools();
    this.props.onOrientationChange && this.props.onOrientationChange(false);
  };

  _onPressBack = () => {
    if (this.state.isFullScreen) {
      this._exitFullScreen();
      return true;
    }
    if (!this._canStepInCall()) {
      return true;
    }
    if (this.props.navigation.canGoBack()) {
      this.props.navigation.goBack()
      return true;
    }
    IMIGotoPage.exit();
  };

  //判断是否通话中、录像中
  _canStepInCall() {
    if (this.props.isCalling) {
      showToast(stringsTo('imi_speaking_block'));
      return false;
    }
    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return false;
    }
    return true;
  }

  _onPressFullScreenTools = (showFullScreenTools) => {
    if(!showFullScreenTools) {
      this.setState({showFullScreenTools: true});
      this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
      this.fullScreenTooltsTimer = setTimeout(() => {
        if (this.props.isMove) {
          return;
        } //全屏长按触摸 取消消失
        this._onCloseFullScreenTools();
      }, 8000);
    } else {
      this._onCloseFullScreenTools();
    }
    
  };

  _onCloseFullScreenTools() {
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.setState({showFullScreenTools: false});
  }

  _onPressMute = () => {
    if (!this._canStepIn()) {
      return;
    }
    this.IMIVideoView && this.IMIVideoView.setIsMute(!this.state.mute);
    this.setState({mute: !this.state.mute});


    this.currentMute = !this.state.mute;
  };

  /*告知外部调用者监听状态*/
  getMute() {
    return this.state.mute;
  }

  getCurrentMute() {
    return this.currentMute
  }

  setCurrentMute(mute) {
    this.currentMute = mute;
  }

  /*外部调用者通知该组件监听状态的变化*/
  setMute(mute) {
    this.setState({mute: mute});
  }


  setPositionShow(flag) {
    this.setState({canShowPotion: flag});
  }

  /*外部调用者通知该组件是否在通话*/
  setCalling(isCalling) {
    this.setState({calling: isCalling});
  }

  //获取录屏状态
  getRecordStatus() {
    return this.state.recording;
  }

  // 获取是否为异常状态
  getErrorStatus() {
    return this.state.showErrorView;
  }

  //设置是否显示暂停按钮
  setPauseView(showPauseView) {
    this.setState({showPauseView: showPauseView});
  }

  //设置是否显示加载中状态
  setLoadingView(showLoadingView) {
    this.setState({isLoading: showLoadingView});
  }
  //同步清晰度
  setQualityIndex(index) {
    console.log('王--setQualityIndex', index);
    this.setState({qualityIndex: index});
    if (this.state.recording) {
      console.log('录屏结束-------------');
      console.log('调用_stopRecord——2');
      this._stopRecord();
    }
  }
  setIsForegroundPage(isForegroundPage) {
    this.isForegroundPage = isForegroundPage;
    if (isCheckingPermission) {
      return
    }
    if (isForegroundPage) {
      this.setState({
        mute: this.currentMute
      })
    }
  }

  setIsNewFullScreen(isNewFullScreen) {
    this.isNewFullScreen = isNewFullScreen;
    console.log('当前Home全屏状态', this.isNewFullScreen, isNewFullScreen);
  }

  // 052全屏状态是有报警按钮，如果看家助手/一键警告 未打开，
  //  需要退出全屏进入看家助手/一键警告
  quitFullScreen() {
    Orientation.lockToPortrait();
    this.setState({isFullScreen: false});
    NavigationBar.setStatusBarHidden(false);
    this.props.navigation.setOptions({tabBarVisible: true});
    this._onCloseFullScreenTools();
  }

  /*告知外部调用者监听状态*/
  getFullscreen() {
    return this.state.isFullScreen;
  }

  enterFullScreen() {
    isAndroid() ? Orientation.lockToPortrait() : null;
    isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
    this.setState({isFullScreen: true});
    NavigationBar.setStatusBarHidden(true);
    this.props.navigation.setOptions({tabBarVisible: false});
    this._onPressFullScreenTools();
    //this.IMIVideoView.standardViewRatio();
    this.isNewFullScreen = false;
    console.log('设置全屏');
  }
  // 更新报错状态
  updateErrStatus() {
    if (this.state.showPauseView || this.state.showErrorView) {
      this.props?.setStopSleep(false);
    }
    this.setState({showPauseView: false, showErrorView: false});
  }

  updateCheckingPermission(value) {
    isCheckingPermission = value
  }
  //点击截屏按钮
  _onPressScreenShot = () => {
  
    IMILogUtil.uploadClickEventForCount('VideoSnapImage'); //统计点击截屏的情况
    
    /*if(!this._canIsCalling())  return;*/
    if (new Date().getTime() - lastClickSnapPhoto < 1000) {
      //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
      return;
    }
    // if (!this._canStepIn()) {
    //   return;
    // }
    lastClickSnapPhoto = new Date().getTime();

    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
    isCheckingPermission = true;

    if (this.state.snapshotVisible) {
      this.setState({snapshotVisible: false});
    }
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      console.log('startCheckPermission', status);

      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          console.log('sd卡权限', status2);

          if (status2 === 0) {
            this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView.screenShot(CAMERA_ID,this.currentSnapshotPath)
              .then(_ => {
                // console.log('------截图成功', this.currentSnapshotPath, LetDevice.model);
                // IMIFile.saveImageToPhotosAlbum(this.currentSnapshotPath, LetDevice.model).then(_ => {
                //   this.setState({
                //     screenShotPath: this.currentSnapshotPath,
                //     snapshotVisible: true,
                //   });
                //   IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
                //   console.log('------截图成功-----保存截图成功', this.currentSnapshotPath);
                //   this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                //   this.snapshotTimeout = setTimeout(() => {
                //     //3秒后截屏缩略图自动隐藏
                //     this.setState({snapshotVisible: false});
                //   }, 3000);
                // });
              })
              .catch(() => {
                console.log('失败了111');

                showToast(stringsTo('action_fail'));
              });
            // if (IMIPackage.minApiLevel >= 10009 && LetDevice.model == 'a1MSKK9lmbs') {
            //   //IPC062 500万像素截图

            // } else {
            //   //其他项目的常规截图
            //   this.IMIVideoView.screenShot(currentSnapshotPath)
            //     .then(_ => {
            //       console.log('------截图成功');
            //       IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, this.props.albumName).then(_ => {
            //         this.setState({
            //           screenShotPath: currentSnapshotPath,
            //           snapshotVisible: true,
            //         });
            //         IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
            //         console.log('------截图成功-----保存截图成功', currentSnapshotPath);
            //         this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            //         this.snapshotTimeout = setTimeout(() => {
            //           //3秒后截屏缩略图自动隐藏
            //           this.setState({snapshotVisible: false});
            //         }, 3000);
            //       });
            //     })
            //     .catch(() => showToast(stringsTo('action_fail')));
            // }

            isCheckingPermission = false;
            this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
            isCheckingPermission = false;
            this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
          }
        });
      } else if (status === -1) {
        isCheckingPermission = false;
        this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
        showToast(stringsTo('storage_permission_denied'));
      }
    });
  };

  //点击录屏按钮
  _onPressRecord = () => {
    if (!this._canStepIn()) {
      return;
    }
    // 停止操作按钮必须录制大于0s
    // 20250721 NKGT-63，支持0s的时候点击停止录制
    // if (!canRecord) {
    //   showToast(stringsTo('click_too_fast'));
    //   return
    // }
    /*    if(!this._canIsCalling())  return;*/
    console.log('调用 _onPressRecord');
    IMILogUtil.uploadClickEventForCount('VideoRecord'); //统计点击录屏的情况
    if (this.state.recording) {
      console.log('_onPressRecord-------------停止录屏');
      console.log('调用_stopRecord——1');
      this._stopRecord();
    } else {
      isCheckingPermission = true;
      this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
      IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
        if (status === 0) {
          IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
            if (status2 === 0) {
              time = moment(new Date().getTime()).format('yyyyMMDD') + '_' + new Date().getTime();
              const currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称;
              // if (isIos()) {
              //   pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
              // }
              console.log('_onPressRecord-----准备录屏', this.currentSnapshotPath);
              canRecord = false;
              this.IMIVideoView.startRecord(CAMERA_ID,currentSnapshotPath);
              // .then(_ => {
              //   console.log('_onPressRecord----------开始录屏');
              //   this.setState({recording: true, recordDuration: 0});
              // })
              // .catch(e => {
              //   console.log(e, '回调失败');

              //   showToast(stringsTo('action_fail'));
              // });
              isCheckingPermission = false;
              this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            }
            // else if (status2 === -1) {
            //   showToast(stringsTo('storage_permission_denied'));
            //   this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            // }
          });
        } else if (status === -1) {
          showToast(stringsTo('storage_permission_denied'));
        }
      });
    }
  };

  //停止录像并保存在相册
  _stopRecord(enterBackground = false) {
    console.log('调用_stopRecord——---');
    isStopping = true;
    this.IMIVideoView.stopRecord(CAMERA_ID);
  }

  //IOS在视频流暂停时,将录制的视频保存到相册
  _saveVideoToPhotosAlbum(enterBackground = false) {
    console.log('_saveVideoToPhotosAlbum------', this.state.recordDuration, this.state.isPlaying);
    if (this.state.recordDuration < 6) {
      //Android因为直播流停止recordDuration会直接为0
      if (this.state.recordDuration == -1) {
        IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
      } else {
        IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      }
      this.IMIVideoView.stopRecord(CAMERA_ID)
        .then(_ => {})
        .catch(error => {}); //不调用，会导致iOS下次录制失败
      this.setState({recording: false, recordDuration: 0, isStart: false});
      this.props?.setStopSleep(false)
      return;
    }
    let pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称;
    // if (isIos()) {
    //   pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
    // }
    this.IMIVideoView.stopRecord(CAMERA_ID)
      .then(_ => {})
      .catch(error => {}); //不调用，会导致iOS下次录制失败
    this.setState({recording: false, recordDuration: 0, isStart: false});
    this.props?.setStopSleep(false)
    IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName)
      .then(_ => {
        //转存视频
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        /*this.IMIVideoView.stopRecord().then(_=>{}).catch(error => {});//不调用，会导致iOS下次录制失败*/
        if (enterBackground) {
          //进入后台导致的自动停止录制，不显示缩略图
          return;
        }
        if (this.isForegroundPage) {
          this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
          this.setState({
            screenShotPathType: 2,
            screenShotPath: _.data,
            snapshotVisible: true,
          });
          this.snapshotTimeout = setTimeout(() => {
            //3秒后截屏缩略图自动隐藏
            this.setState({snapshotVisible: false});
          }, 3000);
        }
      })
      .catch(error => {
        // console.log('保存报错了',error);
        showToast(stringsTo('action_fail'));
      });
  }

  _onPressInStartSpeak = () => {
    this.IMIVideoView.startSpeak();
  };

  _onPressOutStopSpeak = () => {
    this.IMIVideoView.stopSpeak();
  };

  _onPressQuality(modeIndex, selectIndex, flag) {
    if (this.state.qualityIndex === selectIndex && !flag) {
      return;
    }
    IMILog.logI('设置清晰度', JSON.stringify({modeIndex, selectIndex, flag}))
    console.log('设置清晰度', modeIndex, selectIndex, flag);

    this._setStreamVideoQuality(modeIndex, selectIndex);
  }

  /*设置直播流画质*/
  _setStreamVideoQuality(modeIndex, selectIndex) {
    //10030
    if (LetDevice.isOnline) {
      this.onP2pSendStateListener = IMP2pClient.onFileOperateAddListener(e => {
        if (e.iotId != LetDevice.deviceID) {
          return
        }
        // console.log('=====================我收到了',e)
        try {
          const rawData = window?.atob(e.data);
        
          const result = JSON.parse(rawData?.slice(4))
          if (result.thingid === 206 && result.value.code === 0) {
            this.setState({qualityIndex: selectIndex});
            IMIStorage.save({
              key: LetDevice.deviceID + 'qualityIndex',
              data: {
                  qualityIndex: selectIndex,
              },
              expires: null,
            });
          }
        } catch (error) {
          console.log('非流畅的流',error)
        }
       
      });
      this.IMIVideoView?.setVideoResolution(String(modeIndex),CAMERA_ID);
    }
  }

  /*优化网速值有较大跳变的问题，取平均值*/
  _getAverageBps(currentBps) {
    let average = 0;
    if (bpsArray.length < 5) {
      bpsArray.push(currentBps);
    } else {
      bpsArray.shift();
      bpsArray.push(currentBps);
    }
    let total = bpsArray.reduce((a, b) => {
      return a + b;
    });
    return parseInt(total / bpsArray.length);
  }

  /**
   * 首页实时流进入，会出黑屏情况，从现象观看设备在线
   * 能想到的情况是插件进入，设备
   */
  _isBpsAlways0() {
    if (bpsArray.length < 5) {
      return false;
    }
    let total = bpsArray.reduce((a, b) => {
      return a + b;
    });

    return total === 0;
  }

  // 截图成功回调
  _onCommCallback = (event) => {
    console.log('------截图成功-----保存截图成功回调', event,);
  
     console.log('------截图成功-----保存截图成功回调11111',);
    IMIFile.saveImageToPhotosAlbum(this.currentSnapshotPath, LetDevice.deviceID)
      .then(_ => {
        this.setState({
          screenShotPathType: 1,
          screenShotPath: _.data,
          snapshotVisible: true,
        });
     
       
         if (this.props.addFrequentlyLocation) {

   
    
this.props.addFrequentlyLocationPhoto&&this.props.addFrequentlyLocationPhoto( _.data)

    }
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        console.log('------截图成功-----保存截图成功', this.currentSnapshotPath, LetDevice.deviceID);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
      })
      .catch(e => {
        console.log('截图失败', JSON.stringify(e));
      });
  };
  // 录像结束回调
  _onIRecordState = event => {
    if (!this.state.isStart) {
      if (event?.code !== 0) {
        this.tempSnapShotPath = null;
        this.setState({recording: false, recordDuration: 0, isStart: false});
        IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
        return
      }
      this.setState({isStart: true});
      return;
    }
    isStopping = false
    if (event?.type == 1) {
      canRecord = true
    }
    if (this.state.isStart && this.state.recordDuration < 6) {
      console.log('_stopRecord-------------录制时长过短', this.state.recordDuration);
      IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      this.setState({recording: false, recordDuration: 0, isStart: false});
      this.props?.setStopSleep(false)
      return;
    }
    if (event?.code === 0) {
      IMIFile.saveVideoToPhotosAlbum(event?.extra, this.props.albumName)
        .then(_ => {
          //转存视频
          this.setState({recording: false, recordDuration: 0, isStart: false});
          this.props?.setStopSleep(false)
          console.log('_stopRecord-----停止录制并且成功保存录像');
          IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
          // if (enterBackground) {
          //   console.log('_stopRecord-----在后台，不显示缩略图');
          //   return;
          // }
          this.setState({
            screenShotPathType: 2,
            screenShotPath: _.data,
            snapshotVisible: true,
          });

          this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
          this.snapshotTimeout = setTimeout(() => {
            //3秒后截屏缩略图自动隐藏
            this.setState({snapshotVisible: false});
          }, 3000);
          if (this.isForegroundPage) {
            //this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            //let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            //this.IMIVideoView.snap(currentSnapshotPath).then(_ => {
            //截屏一张作为悬浮缩略图，不转存
            // console.log('_stopRecord-----在前台，显示缩略图');
            // this.setState({
            //   screenShotPath: currentSnapshotPath,
            //   snapshotVisible: true,
            // });
            // this.snapshotTimeout = setTimeout(() => {
            //   //3秒后截屏缩略图自动隐藏
            //   this.setState({snapshotVisible: false});
            // }, 3000);
            //});
          }
        })
        .catch(error => {
          this.tempSnapShotPath = null;
          this.setState({recording: false, recordDuration: 0, isStart: false});
          this.props?.setStopSleep(false);
          IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
        });
    } else {
      this.tempSnapShotPath = null;
      this.setState({recording: false, recordDuration: 0, isStart: false});
      this.props?.setStopSleep(false);
      IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
    }
    // IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName)
    //   .then(_ => {
    //     //转存视频
    //     this.setState({recording: false, recordDuration: 0});
    //     console.log('_stopRecord-----停止录制并且成功保存录像');
    //     IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
    //     // if (enterBackground) {
    //     //   console.log('_stopRecord-----在后台，不显示缩略图');
    //     //   return;
    //     // }
    //     if (this.isForegroundPage) {
    //       this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
    //       let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
    //       this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
    //         //截屏一张作为悬浮缩略图，不转存
    //         console.log('_stopRecord-----在前台，显示缩略图');
    //         this.setState({
    //           screenShotPath: currentSnapshotPath,
    //           snapshotVisible: true,
    //         });
    //         this.snapshotTimeout = setTimeout(() => {
    //           //3秒后截屏缩略图自动隐藏
    //           this.setState({snapshotVisible: false});
    //         }, 3000);
    //       });
    //     }
    //   })
    //   .catch(error => {
    //     this.setState({recording: false, recordDuration: 0});
    //     IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
    //   });
  };
  _onEventChange = event => {
    // console.log('直播流----_onEventChange', event);
    if (!this.props.netConnected) {
      this.setState({
        isLoading: false,
        showPauseView: false,
      });
      return
    }
    if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
      // console.log('==============',this.props.initStatus, this.props.isWifi, this.props.isDataUsage, this.state.handleOk)
      // if(this.props.initStatus === 1 && !this.props.isWifi && this.props.isDataUsage && !this.state.handleOk) {
      //   this.IMIVideoView && this.IMIVideoView.stop();
      //   return
      // }
      // this.props.doOnline(true);
      const params = {
        event,
        time: [new Date().getTime(), new Date()]
      }
      IMILog.logI('直播流时间记录', '10013' + JSON.stringify({...params, sleepButton: this.props.disabledStatus, isOnline: this.props.isOnLine, netConnected:this.props.netConnected}));
      // console.log('直播流----_onEventChange,回调网速值');
      this.setState({isLoading: false}); //视频在播放,但显示为暂停
      if (this.props.disabledStatus && !this.state.recording) {
        this.props?.setStopSleep(false);
      }
      // console.log('直播流代码---',event.code);
      this.initBps = this.initBps === 0 ? this._getAverageBps(event.extra.arg1) : this.initBps
      this.setState({bps: this._getAverageBps(event.extra.arg1), p2pType: event.extra.arg2});
      console.log('=======', this.state.isLoading, this.state.isPlaying, this.state.isLoading && !this.state.isPlaying)
      // if (this.state.isLoading && !this.state.isPlaying) {
        //loading消失而实时流不是playing状态
        this.setState({
          isLoading: false,
          isPlaying: true,
          showPauseView: false,
        });
        this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
      // }
      if (this.state.isPlaying && this.state.showPauseView) {
        this.setState({showPauseView: false}); //视频在播放,但显示为暂停
      }
      //cameraPlayerPage 播放器准备时显示加载动画，这里某些时候实时流在走，loading不消失

      // event.extra.bps>0&&this.state.isLoading&&!this.state.isPlaying&&this.setState({isLoading:false,isPlaying:true});//此句为了规避正常播放时突然出现loading框的问题
      //10005时3次连接问题 如果大于0kb就清除
      if (event.extra.bps > 0) {
        timeErr10005 = 0;
      }

      //实时流会偶现直播黑屏，网速值为0，复现不好复现，这里增加对网络bps的检测
      //如果五次网速值都为0，并且是播放状态，可能就出现问题了
      // if (this._isBpsAlways0() && this.state.isPlaying && !this.initBps) {
      //   this.setState({showPauseView: true});
      // } else {
      //   this.setState({showPauseView: false});
      // }
      this.setState({showPauseView: false});
    } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) {
      //调用video.start()后的第一个回调
      console.log('直播流----_onEventChange,开始启用');
      //  IMILog.logD("王 实时流 _onEventChange >LOADING ",event.toString())
      // this.props.doOnline(true);
      this.initBps = 0;
      this.setState({
        isLoading: true,
        isPlaying: false,
        showPauseView: false,
        showErrorView: false,
      });
      if (this.props.disabledStatus) {
        this.props?.setStopSleep(false);
      }
      this.props.onVoiceCallErrorOccurred && this.props.onVoiceCallErrorOccurred()
      this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
      if (this.state.recording) {
        //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
        console.log('调用_stopRecord——3');
        this._stopRecord();
      }
      this._getStreamVideoQualityByTcp();
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BPS) {
      this.firstBps = event.extra.arg1;
      if (this.first10011) {
        this.setState({bps: this.firstBps});
        this.firstBps = -1;
        this.first10011 = false;
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
      // this.props.doOnline(true);
      console.log('直播流----_onEventChange,出现关键帧');
      this.first10011 = true;
      if (this.firstBps !== -1) {
        this.setState({bps: this.firstBps});
        this.firstBps = -1;
        this.first10011 = false;
      }
      !this.props.isDataUsage && this.setState({isLoading: false, isPlaying: true});
      !this.props.isDataUsage && this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
      if (this.props.disabledStatus) {
        this.props?.setStopSleep(false);
      }
      let qualityData = this.props.qualityData[this.state.qualityIndex];
      // 设置清晰度(用于每次进入插件、P2P重连时切换正常清晰度)
      this._onPressQuality(qualityData.modeIndex, this.state.qualityIndex, true);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
      //  IMILog.logD("王 实时流 _onEventChange >PAUSE  ",event.toString())
      console.log('直播流----_onEventChange,停止播放', event.code); //每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
      console.log('直播流----_onEventChange,停止播放', event.code);
      this.initBps = 0;
      this.setState({isLoading: false, isPlaying: false, showPauseView: true}); //Android会调用两次，所以停止了也没有暂停按钮
      this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);
      if (this.state.recording) {
        //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
        if (!this.isForegroundPage) {
          //进入后台，这里不用不停止,进入后台的事件中已经调用停止了。
          return;
        }
        this._stopRecord();
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
      console.log('直播流----_onEventChange,开始播放');
      //this.setState({isLoading:false});
    } 
    // else if (event.code === 104) {
    //   if (CONST.isAndroid && event.extra.mode_value_key == false) {
    //     this.IMIVideoView.clearRecord();
    //   }
    // } 
    else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_SLEEP) {
     
      // this.props.doSleep(event?.extra?.arg2 === 1 ? true : false);
      if (event.extra.arg2 === 1 && this.state.recording) {
        this._stopRecord();
      }
    }
  };

  _onRecordTimeChange = event => {
    console.log('录屏-----时长回调', event?.extra);
    if (!isNaN(event?.extra)) {
      if (event?.extra > 0) {
        canRecord = true;
      }
      this.setState({recording: true, recordDuration: event?.extra}, callback => {
        // if (this.state.recordDuration == 1 && isIos()) {
        //   // 临时截图
        //   let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
        //   this.tempSnapShotPath = currentSnapshotPath;
        //   this.IMIVideoView.snap(currentSnapshotPath, LetDevice.model)
        //     .then(_ => {
        //       console.log('截屏路径 临时路径---', this.tempSnapShotPath);
        //     })
        //     .catch(e => {
        //       console.log('截屏路径 临时路径---', e);
        //     });
        // }
      });
      this.props?.setStopSleep(true);
    }
    // this.setState({recording: true, recordDuration: 0})
    // this.setState({recordDuration: event.extra}, callback => {
    //   if (this.state.recordDuration == 1) {
    //     // 临时截图
    //     let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
    //     this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
    //       this.tempSnapShotPath = currentSnapshotPath;
    //       // console.log('临时路径---',this.tempSnapShotPath);
    //     });
    //   }
    // });
  };

  netWorkChange() {
    this.setState({
      isLoading: false,
      isPlaying: false,
      showPauseView: true,
      showErrorView: false,
    });
    this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);
    this.IMIVideoView.stopRecord(CAMERA_ID)
      .then(_ => {})
      .catch(error => {}); //不调用，会导致iOS下次录制失败
  }

  /**
   * 竖屏状态视屏区域填充UI
   *  导航栏
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenVideoViewArea() {
    if(!this.props.showNavBar){
      return null
    }
    return (
      // <>
      //     <View style={{position: "absolute", width: "100%"}}>
      //         {
      this.props.navBar ? (
        this.props.navBar(this.state.bps, this.state.p2pType, this.state.isFullScreen,this.state.cruiseIng)
      ) : (
        <NavigationBar
          type={NavigationBar.TYPE.DARK}
          backgroundColor={'transparent'}
          title={LetDevice.devNickName}
          subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : ' '}
          left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
          right={this.props.navBarRight ? this.props.navBarRight : []}
        />
      )
      //         }
      //     </View>
      // </>
    );
  }

  /**
   * 清晰度选择器
   * @returns {Element}
   * @private
   */
  _renderLandscapeScreenQualityPopView() {
    return (
      <ModalView visible={this.state.qualityVisible} onClose={_ => this.setState({qualityVisible: false})}>
        <View
          style={{
            flex: 1,
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          {this.props.qualityData.map((item, index) => {
            let qualityData = this.props.qualityData[index];
            return (
              <TouchableOpacityText
                key={`qualityItem_${index}`}
                title={qualityData.title}
                accessibilityLabel={qualityData.accessibilityLabel}
                style={{
                  width: 120,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: index === this.state.qualityIndex ? '#4A70A5' : 'rgba(255,255,255,0.3)',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginVertical: 10,
                }}
                textStyle={{
                  color: 'rgba(255,255,255,0.9)',
                  fontSize: 15,
                  fontWeight: 'bold',
                }}
                onPress={_ => {
                  this.setState({qualityVisible: false});
                  this._onPressQuality(qualityData.modeIndex, index);
                }}
              />
            );
          })}
        </View>
      </ModalView>
    );
  }

  /**
   * 全屏状态videoView区域填充UI
   * @returns {Element}
   * @private
   */
  _renderLandscapeScreenVideoViewArea() {
    let qualityData = this.props.qualityData[this.state.qualityIndex];
    // let languageStr = locales[0]?.languageCode;
    // let tempStr = 'zh';
    // if (languageStr.indexOf(tempStr) != -1) {
    //   let tempQualityData = [
    //     {
    //       title: '标清',
    //       index: 1,
    //       modeIndex: 2,
    //       accessibilityLabel: 'home_page_clarity_show_720',
    //     },
    //     {
    //       title: '高清',
    //       index: 2,
    //       modeIndex: 3,
    //       accessibilityLabel: 'home_page_clarity_show_1080',
    //     },
    //     {
    //       title: '超清',
    //       index: 3,
    //       modeIndex: 5,
    //       accessibilityLabel: 'home_page_clarity_show_25',
    //     },
    //   ];
    //   qualityData = tempQualityData[this.state.qualityIndex];
    // }
    const disabledStatus = !this.props.isOnLine || !this.props.netConnected || this.state.showErrorView
    return this.state.showFullScreenTools ? (
      <LivePlayerFullScreenToolBarView
        // style={{position: "absolute"}}
        exitPress={this._exitFullScreen}
        qualityTitle={qualityData.title}
        qualityPress={_ => {
          if (!this._canStepIn()) {
            return;
          }
          if (this.state.recording) {
            showToast(I18n.t('screen_recording'));
            return;
          }
          this.setState({qualityVisible: true});
        }}
        qualityDisabled={
          disabledStatus ? true : this.props.isShowPanoramicView
            ? this.state.recording || this.props.isCallingStatue
            : this.state.recording || this.props.isSleepStatus
        }
        mutePress={this._onPressMute}
        mute={this.state.mute}
        muteDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
        screenshotDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
        recordDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
        screenshotPress={this._onPressScreenShot}
        recordPress={this._onPressRecord}
        recording={this.state.recording}
        moreItems={this.props.fullScreenToolBarMoreItems}
        accessibilityLabel={qualityData.accessibilityLabel}
      />
    ) : null;
  }

  /**
   * 竖屏状态下半屏区域填充UI  按钮
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenPlayerToolBarArea() {
    
    let qualityData = this.props.qualityData[this.state.qualityIndex];
    // let languageStr = locales[0]?.languageCode;
    // let tempStr = 'zh';
    // if (languageStr.indexOf(tempStr) != -1) {
    //   let tempQualityData = [
    //     {
    //       title: '标清',
    //       index: 1,
    //       modeIndex: 2,
    //       accessibilityLabel: 'home_page_clarity_show_720',
    //     },
    //     {
    //       title: '高清',
    //       index: 2,
    //       modeIndex: 3,
    //       accessibilityLabel: 'home_page_clarity_show_1080',
    //     },
    //     {
    //       title: '超清',
    //       index: 3,
    //       modeIndex: 5,
    //       accessibilityLabel: 'home_page_clarity_show_25',
    //     },
    //   ];
    //   qualityData = tempQualityData[this.state.qualityIndex];
    // }

    const disabledStatus = !this.props.isOnLine || !this.props.netConnected || this.state.showErrorView
    return (
      <View style={{flex: 1, flexDirection: 'column'}}>
        {this.props.showPlayItem&&<LivePlayerToolBarView
          qualityTitle={qualityData.title}
          accessibilityLabel={qualityData.accessibilityLabel}
          qualityPress={_ => {
            //清晰度选择
            if (!this._canStepIn()) {
              return;
            }
            if (this.state.recording) {
              showToast(I18n.t('screen_recording'));
              return;
            }
            this.setState({qualityVisible: true});
          }}
          qualityDisabled={
            disabledStatus ? true : this.props.isShowPanoramicView
              ? this.state.recording || this.props.isCallingStatue
              : this.state.recording || this.props.isSleepStatus
          } //是否是059
          muteDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          screenshotDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          recordDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          fullscreenDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          isCallingStatue={this.props.isSleepStatus}
          fullscreenPress={this._onPressFullScreen} //全屏
          mutePress={this._onPressMute} //音量
          mute={this.props.isSleepStatus ? true : this.state.mute}
          screenshotPress={this._onPressScreenShot} //截图
          recordPress={this._onPressRecord} //录像
           onStartCall={this.props.onStartCall} //打电弧
          isCalling={this.props.isCalling}
          recording={this.state.recording}
          moreItems={this.props.toolBarMoreItems}
          bottomBarMoreItems={this.props.bottomBarMoreItems}
          showBottomTitle={DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH').showLiveToolbarBottomTitle}
        />}
        {this.props.children}
      </View>
    );
  }
 /**
   * 竖屏状态下的功能按钮
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenToolBarArea() {
    
    if(!this.props.showPlayItem){
      return null
    }
    let qualityData = this.props.qualityData[this.state.qualityIndex];
    const disabledStatus = !this.props.isOnLine || !this.props.netConnected || this.state.showErrorView
    return (
      <View >
        <LivePlayerNewToolBarByBottom
          qualityTitle={qualityData.title}
          accessibilityLabel={qualityData.accessibilityLabel}
          qualityPress={_ => {
            //清晰度选择
            if (!this._canStepIn()) {
              return;
            }
            if (this.state.recording) {
              showToast(I18n.t('screen_recording'));
              return;
            }
            this.setState({qualityVisible: true});
          }}
          qualityDisabled={
            disabledStatus ? true : this.props.isShowPanoramicView
              ? this.state.recording || this.props.isCallingStatue
              : this.state.recording || this.props.isSleepStatus
          } //是否是059
          muteDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          screenshotDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          recordDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          fullscreenDisabled={disabledStatus ? true : this.props.isShowPanoramicView ? this.props.isCallingStatue : this.props.isSleepStatus} //是否是059
          isCallingStatue={this.props.isSleepStatus}
          fullscreenPress={this._onPressFullScreen} //全屏
          mutePress={this._onPressMute} //音量
          mute={this.props.isSleepStatus ? true : this.state.mute}
          screenshotPress={this._onPressScreenShot} //截图
          recordPress={this._onPressRecord} //录像
          recording={this.state.recording}
          moreItems={this.props.toolBarMoreItems}
          showBottomTitle={DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH').showLiveToolbarBottomTitle}
        />
       
      </View>
    );
  }
  _renderRecordingView() {
    let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : '00:00';

    return (
      <View
        style={{
          width: 64,
          height: 26,
          backgroundColor: 'rgba(0,0,0,0.6)',
          borderRadius: 4,
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 10 + (this.state.isFullScreen && !this.state.showFullScreenTools ? 50 : 0),
        }}>
        <View
          style={{
            backgroundColor: '#E74D4D',
            opacity: 0.9,
            width: 6,
            height: 6,
            borderRadius: 3,
          }}
        />
        <Text style={[{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}, Typography.textFix]}>
          {duration}
        </Text>
      </View>
    );
  }

  _loadingView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (this.props.isSleepStatus) {
      return;
    }
    if (!this.state.isLoading) {
      return;
    }
    if (!this.props.netConnected) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator style={{width: 54, height: 54}} color={'#ffffff'} size={'large'} />
        <Text
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}>
          {stringsTo('commLoadingText')}
        </Text>
      </View>
    );
  }

  // 无网络
  handleNetReload() {
    this.setState({
      showErrorView: false,
      isLoading: true,
      recording: false,
      isStart: false,
      handleOk: true,
    });
    this.props?.initRTCView(true);
    this.props?.setStopSleep(false)
  }

  hanldeHelp() {
    this.setState({isShowErrorHelp: true});
  }

  hanldeLoading() {
    this.setState({isLoading: false});
  }
  // 设置对应错误文案
  _renderErrorTex() {
    if (this.state.errorCode.includes('202')) {
      return I18n.t('onPlayErrorMaxText', {code: this.state.errorCode});
    }
    return I18n.t('onPlayErrorText', {code: this.state.errorCode});
  }

  _errorView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (!this.props.netConnected) {
      return;
    } //是否断网 断网会进入暂停
    if (this.props.isSleepStatus) {
      return;
    }
    if (!this.state.showErrorView) {
      return;
    }
    if (this.state.isLoading) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={this._renderErrorTex()}
        />

        <View
          style={{
            width: '100%',
            justifyContent: 'space-evenly',
            flexDirection: 'row',
          }}>
          <RoundedButtonView
            buttonText={stringsTo('error_code_common_retry')}
            buttonStyle={{
              marginTop: 30,
              paddingHorizontal: 20,
              height: 40,
              borderRadius: 7,
            }}
            buttonTextStyle={{textAlign: 'center'}}
            onPress={() => {
              console.log('异常点击');
              this.setState({
                handleOk: true,
                showErrorView: false,
                isLoading: true,
                recording: false,
                isStart: false,
              });
              this.props?.setStopSleep(false)
              this.props?.initRTCView(true);
            }}
            accessibilityLabel={'error_code_common_retry'}
          />
          <RoundedButtonView
            buttonText={stringsTo('error_help')}
            buttonStyle={{
              marginTop: 30,
              paddingHorizontal: 20,
              height: 40,
              borderRadius: 7,
            }}
            buttonTextStyle={{textAlign: 'center'}}
            onPress={() => {
              //播放器发生错误时，点击查看帮助
              this.setState({isShowErrorHelp: true});
            }}
            accessibilityLabel={'error_help'}
          />
        </View>
      </View>
    );
  }

  _pauseView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (this.props.isSleepStatus) {
      return;
    }
    if (!this.state.showPauseView) {
      return null;
    }
    if (!this.props.netConnected) {
      return null;
    }
    if (this.props.netConnected) {
      if (this.state.showErrorView) {
        return null;
      } //IOS上会先ERROR后PAUSE，如果已经显示errorView，则不显示pauseView
    }
    if (this.state.isLoading) {
      return;
    } //
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ImageButton
          style={{width: 52, height: 52}}
          source={require('./res/icon_play.png')}
          highlightedSource={require('./res/icon_play_p.png')}
          onPress={() => {
            console.log('暂停点击');
            if (!this.props.netConnected) {
              showToast(stringsTo('network_not_connected'));
              return;
            }
            this.setState({
              handleOk: true,
              showPauseView: false,
              isLoading: true,
              recording: false,
              isStart: false,
            });
            this.props?.setStopSleep(false)
            this.props?.initRTCView(true);
          }}
          accessibilityLabel={'home_page_icon_play'}
        />
      </View>
    );
  }

  // 当前位置
  _renderPositionView() {
    if(!this.state.canShowPotion) {
      return
    }
     
    return (
      <View style={{
        width: 50,
        height: 50,
        borderColor: '#999',
        borderWidth: 1,
        borderRadius: 4,
        backgroundColor: 'rgba(0,0,0,0.5)',
        padding: 1,
        position: 'absolute',
        top: this.props.navigationBarHeight + 30,
        left: this.state.isFullScreen ? 80: 20,
        zIndex: 999,
      }}>
        <View
          style={{
            width: 10,
            height: 10,
            backgroundColor: '#fff',
            position: 'absolute',
            left: this.state.positionX,
            top: this.state.positionY,
            borderRadius: 2,
            zIndex: 999,
          }}
        ></View>
      </View>
    )
  }
  _renderSnapshotView() {
    if (!this.state.snapshotVisible) {
      return null;
    }
    let tempSnapShotPath = this.state.screenShotPath;
    if (isIos() && this.tempSnapShotPath) {
      tempSnapShotPath = this.tempSnapShotPath;
    }
    console.log('截屏路径------------', tempSnapShotPath);
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          bottom: this.state.isFullScreen ? 206 : 19,
          left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
          width: 140,
          height: 80,
          zIndex: 999,
        }}>
        <ImageButton
          style={{
            width: '100%',
            height: '100%',
            borderWidth: 2,
            borderColor: 'white',
            borderRadius: 10,
          }}
          source={{uri: 'file://' + tempSnapShotPath}}
          onPress={_ => {
            if (
              LetDevice.model == 'a1MZkl613tF' ||
              LetDevice.model == 'a1B1tBn2SdK' ||
              LetDevice.model == 'a1EVaCvJ43g'
            ) {
              return;
            }
            if (this.state.recording) {
              showToast(I18n.t('screen_recording'));
              return;
            }
            /* if (this.state.calling) {
                            showToast(I18n.t('camera_calling'));
                            return;
                        }*/
            if (!this._canIsCalling()) {
              return;
            }
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            this.setState({snapshotVisible: false});
            if (this.state.screenShotPathType === 1) {
                this.props.navigation.push('PhotoView', {
                  imageUrl: 'file://' + this.state.screenShotPath,
                  isFullScreen: this.state.isFullScreen,
                });
              } else {
                this.props.navigation.push('VideoPreView', {
                  mediaData: {url: 'file://' + this.state.screenShotPath},
                  isFullscreen: this.state.isFullScreen,
                });
            }
            // IMIGotoPage.startAlbumPage(this.props.albumName);
          }}
          accessibilityLabel={'home_page_picture_show'}
        />
      </View>
    );
  }


  //进入相册
  goToAlbum() {
    let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
    if (showRNAlbum || LetDevice.model === 'a1l4Z7lJ1ns' || LetDevice.model === 'a1yMb5JVWDa') {
      //510项目使用rn相册
      // if (isAndroid() && IMIPackage.minApiLevel>=10006){
      //     this.props.navigation.push('CameraListPage');
      // }else if (isIos() &&IMIPackage.minApiLevel>=10005){
      //     this.props.navigation.push('CameraListPage');
      // }else {
      //     IMIGotoPage.startAlbumPage(LetDevice.deviceID);
      // }

      // if (IMIPackage.minApiLevel < 10007) {
      //   IMIGotoPage.startAlbumPage(LetDevice.deviceID);
      // } else {
      this.props.navigation.push('CameraListPage');
      // }
    } else {
      IMIGotoPage.startAlbumPage(LetDevice.deviceID);
    }
  }
  //播放器
  render() {
    const VideoHeight=kWindowWidth*9/16

    StatusBar.setBarStyle('dark-content');

    // this.IMIVideoView = {};
    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        style={{
          flex: 1,
          backgroundColor: this.props.pageBackgroundColor ? this.props.pageBackgroundColor : '#F2F3F5',
          flexDirection: 'column',
         
        }}>

          {/* 导航栏 */}
        {this.state.isFullScreen?null:this._renderPortraitScreenVideoViewArea()}
        <View style={{paddingTop:this.state.isFullScreen?0:0,
          position: 'relative',

        }} >

           
          <IMICameraVideoView
            playerClass={PlayerClass.LIVE}
            optionMap={{[IntercomModeKey]: INTERCOM_MODE.SingleTalk}}
            style={{
              height:this.state.isFullScreen?kWindowWidth:VideoHeight
            }}
            ref={ref => {
              console.log('this.IMIVideoView ref11------', this.props.initStatus, this.props.canPlay);
              if (!this.IMIVideoView && ref && !(this.props.firstIn && !this.props.isOnLine) && this.props.initStatus === 1) {
                this.IMIVideoView = ref;
                this.isForegroundPage && this.props.canPlay && this.IMIVideoView?.prepare();
                this.props.videoRef && this.props.videoRef(this.IMIVideoView);
              }
            }}
            mute={this.state.mute}
            //dataSource={{iotId: LetDevice.deviceID, start_time: '1728979626', end_time: '0', offset: '0'}}
            dataSource={this.props.dataSource}
            onPrepared={() => {
              console.log('直播流-----onPrepared------');
              // this.IMIVideoView?.start();
              this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PREPARED);
            }}
            onCommCallback={ this._onCommCallback}
            onIRecordState={this._onIRecordState}
            onEventChange={this._onEventChange}
            onP2pSendState={this.props.onP2pSendState} //获取p2p文件，也是用来判断是否能进入回看
            //onSendData={this.props.onSendData} //p2p 消息成功后的回调
            //onFileOperate={this.props.onFileOperate} //p2p 操作数据库文件
            onErrorChange={event => {
              console.log('直播流-----onErrorChange------   event.code ' + event.code, event);
              // IMILog.logD("王 实时流 onErrorChange : ",event.toString())
              // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
              if(event?.extra?.arg3 == 208) {
                showToast(stringsTo('call_busy_error'))
                this.props.onVoiceCallErrorOccurred && this.props.onVoiceCallErrorOccurred('', true)
                return
              } 
              // else if (event?.extra?.arg3 == 511000) {
              //   this.props.doOnline(false);
              //   if (this.state.recording) {
              //     this._stopRecord();
              //   }
              // } 
              else {
                !this.props.isSleepStatus && this.props.onErrorChange(event)
              }
              do {
                //判断如果是通话报错则此处进行判断是否为占线
                // if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                //   alert('量产5-12——直播流Error-------- 不是流意外关闭');
                //   continue;
                // }

                // if (event.code == 1009) {
                //   // 设备不在线
                //   this.props.onDeviceStatusChange && this.props.onDeviceStatusChange(false); //通知外部调用者设备离线，需要更新相关的UI
                //   return;
                // }

                //iOS的通话异常，已经直连模式下通话占线的code都是12
                if (
                  event.code == 12 ||
                  event.code == 15 ||
                  event.code == 16 ||
                  event.code == 19 ||
                  event.code == -88002 ||
                  event.code == 14
                ) {
                  //IOS的通话异常
                  event.code == 12 || event.code == -88002
                    ? showToast(stringsTo('call_busy_tips'))
                    : showToast(stringsTo('call_connect_error'));
                  this.props.onVoiceCallErrorOccurred && this.props.onVoiceCallErrorOccurred(event.code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                  return;
                } else {
                  //不是通话异常，则是正常的实时流异常，走do-while之后的逻辑
                  //解决ios网络异常通话状态不挂断问题
                  // 2025.1.22直播异常未挂断电话 bug1059
                  if (this.props.onVoiceCallErrorOccurred) {
                    this.props.onVoiceCallErrorOccurred && this.props.onVoiceCallErrorOccurred('', true)
                  }
                  // 直播异常需要手动挂断录屏
                  if (this.state.recording) {
                    this._stopRecord();
                  }
                  IMILog.logI('直播异常', 'event：' + JSON.stringify(event));
                  this.setState({showErrorView: true, showPauseView: false, errorCode: event?.extra?.arg1 + '(' + event?.extra?.arg3 + ')'});
                  this.props?.setStopSleep(true);
                  continue;
                }

                //   if (isAndroid()) {
                //   //休眠第一次10005时  播放,连接两次后自动取消连接
                //   if (event.code == 1005) {
                //     timeErr10005 = timeErr10005 + 1;
                //     if (timeErr10005 < 3) {
                //       this.IMIVideoView && this.IMIVideoView.start();
                //       return;
                //     }
                //   }
                // }
                // //判断是否为通话模式(Android)
                // if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {
                //   continue;
                // }
                // //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                // if (event.extra.arg2 === 'voice intercom existed') {
                //   console.log('其他设备正在通话中，请稍后重试');
                //   showToast(stringsTo('call_busy_tips'));
                //   let code = 12;
                //   this.props.onVoiceCallErrorOccurred && this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                // } else {
                //   console.log('网络异常，通话中断');
                //   showToast(stringsTo('net_connect_error'));
                //   console.log('量产5-12——直播流Error-------- 网络异常，通话中断', event.code);
                //   this.props.onVoiceCallErrorOccurred && this.props.onVoiceCallErrorOccurred(event.code); //code通知外部调用者通话异常中断，需要更新通话相关的UI
                // }
                // return;
              } while (false);
              //   if (this.props.netConnected) {
              //   //todo 断网进入暂停状态，不进入报错显示
              //   this.setState({
              //     isPlaying: false,
              //     showErrorView: true,
              //     showPauseView: false,
              //     errorCode: event.code,
              //   });
              //   this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.ERROR);
              // }
            }}
            onRecordTimeChange={this._onRecordTimeChange}
            //lensCorrect={this.props.lensCorrect}
            // onVideoViewClick={() => {
            //   this.state.isFullScreen && this.state.showFullScreenTools
            //     ? this._onCloseFullScreenTools()
            //     : this._onPressFullScreenTools();
            //   this.props.onVideoClick && this.props.onVideoClick();
            // }}
            onVideoViewClick={() => {
              this.state.isFullScreen && this._onPressFullScreenTools(this.state.showFullScreenTools);
              this._controlShowToolPlayer() //点击视频view，控制显示/隐藏工具栏
            }}
            onVideoZoomScale={data => {
              console.log('onVideoZoomScale---', data);
              if (data) {
                console.log('缩放有值走这里');
                this.videoScaleChanged(data);
              } else {
                console.log('缩放无值走这里');
              }
            }}
          />
          {/*全屏?横屏UI:竖屏UI(navBar)*/}
          <View style={{
              position: 'absolute',
              flexDirection: 'column',
              alignItems: 'center',
              // top:0,
              width: '100%',
              height: '100%',
              
            }} pointerEvents='box-none'>
             {this.state.isFullScreen
              ? this._renderLandscapeScreenVideoViewArea()
              : null}
                {this.props.videoSubView
              ? this.props.videoSubView(this.state.isFullScreen, this.state.showFullScreenTools)
              : null}
              {/* 录屏时间显示放这里 */}
                {this.state.recording && this.state.isPlaying ? this._renderRecordingView() : null}
             
          </View>
          <View
            pointerEvents="box-none"
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              flexDirection: 'column',
              alignItems: 'center',
              // top:this.state.isFullScreen ? 0 : this.props.navigationBarHeight
              //  marginTop: this.state.isFullScreen ? 0 : this.props.navigationBarHeight,
              // paddingTop:this.state.isFullScreen?0:this.props.navigationBarHeight
            }}>
          
            {this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()}
            {this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()}
            {this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()}
            {this._deviceOffLineView()}
            {this._renderSnapshotView()}
            {this.state.positionShow && this._renderPositionView()}
            
          
          </View>
          {this.state.isFullScreen ? null : <View
            pointerEvents="box-none"
            style={{
              position: 'absolute',
              width: '100%',
              flexDirection: 'column',
              alignItems: 'center',
              bottom:0,
              display:this.state.showToolPlayer?'flex':'none'
            }}>
          {this._renderPortraitScreenToolBarArea()}
          </View>}
          
        </View>

        {/*全屏?null:渲染外层传入的UI*/}
        {this.state.isFullScreen ? null : this._renderPortraitScreenPlayerToolBarArea()}

        {/*渲染清晰度选择器，不占UI空间的*/}
        {this._renderLandscapeScreenQualityPopView()}
        {this.showConnectErrorDialog()}
        {this.showDeviceOfflineDialog()}
        {this.renderZoomScaleView()}
       
      </View>
    );
  }

  // 设置定时器
  videoScaleTimer = null;
  // 缩放比例系数变化
  videoScaleChanged(data) {
    // if (!this._canStepIn()) {
    //   return;
    // }
    let scale = data.scaleRatio;
    let newScale = scale.toFixed(1);
    let zoomScale = this.state.zoomScale;

    if (Math.abs(zoomScale - newScale) < 0.1) {
      return;
    }

    // 进行节流操作
    let endTime = Date.now();
    if (endTime - this.startScaleTime < 50) {
      console.log('_onVideoScaleChanged', scale);
      return;
    }
    this.startScaleTime = endTime;
    this._updateScale(newScale);
  }

  _updateScale(scale) {
    if (scale) {
      if (this.angleViewTimeout) {
        // 隔一段时间就需要隐藏
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }
      this.angleViewTimeout = setTimeout(() => {
        this.setState({isShowZoomScale: false});
      }, 3000);
      this.setState({zoomScale: scale, isShowZoomScale: true, showPlayToolBar: scale > 1.03 ? false : true});
    }
  }
  _controlShowToolPlayer(){
    if(!this.props.showPlayItem){
      return null
    }
     this.setState({
      showToolPlayer: !this.state.showToolPlayer,})
       clearTimeout(this.showToolPlayerTimer)
       this.showToolPlayerTimer=null
       this.showToolPlayerTimer=setTimeout(() => {
       this.setState({
       showToolPlayer: false,})
       },5000)
  }
 
  // 缩放比例view
  renderZoomScaleView() {
    if (!this.state.isShowZoomScale) {
      return null;
    }
    return (
      <View
        style={{
          backgroundColor: '#00000099',
          borderRadius: 4,
          position: 'absolute',
          top: this.state.isFullScreen ? 60 : StatusBarHeight + 15,
          // bottom: this.state.isFullScreen ? 206 : 0,
          // left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
          left: this.state.isFullScreen ? 65 : 20,
          width: 50,
          height: 30,
          zIndex: 999,
        }}>
        <Text
          accessibilityLabel={'zoom'}
          numberOfLines={1}
          ellipsizeMode={'tail'}
          style={{lineHeight: 29, fontSize: 15, color: '#FFFFFF', textAlignVertical: 'center', textAlign: 'center'}}>
          {`x${this.state.zoomScale}`}
        </Text>
      </View>
    );
  }
  
  _deviceOffLineView() {
    if (this.props.isOnLine) {
      return null;
    }
    if (LetDevice.isLocalDevice) {
      return null;
    } //支持且处于离线模式(仅062)的设备不显示
    return (
      <TouchableWithoutFeedback onPress={_ => {this.state.isFullScreen && this._onPressFullScreenTools(this.state.showFullScreenTools);}}>
        <View
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'black',
          }}>
          <View style={{alignItems: 'center', justifyContent: 'center'}}>
            <XText
              raw={true}
              style={{
                textAlign: 'center',
                color: '#FFFFFF',
                fontSize: 15,
                minHeight: 20,
                marginBottom: 14
                // backgroundColor:'red',
              }}
              text={`${stringsTo('device_offline_aready')}  ${stringsTo('offlineTime')}${LetDevice.offlineTime}`}
            />

            <XText
              raw={true}
              text={stringsTo('error_help')}
              style={{
                marginTop: 5,
                borderRadius: 7,
                paddingHorizontal: 5,
                minWidth: 100,
                lineHeight: 40,
                backgroundColor: '#4A70A5',
                fontSize: 15,
                color: 'white',
                textAlign: 'center',
                textAlignVertical: 'center',
              }}
              onPress={() => {
                this.setState({isShowOffLineHelp: true});
              }}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
  //连接失败的帮助弹框
  showConnectErrorDialog() {
    let {isPOE} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
    return (
      <Modal
        supportedOrientations={['portrait', 'landscape']}
        animationType="none"
        transparent={true}
        visible={this.state.isShowErrorHelp}
        onRequestClose={() => {
          console.log('onRequestClose is do');
          this.setState({isShowErrorHelp: false});
        }}>
        <ScrollView contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={false}>
          <View
            style={{
              flexDirection: 'column',
              flex: 1,
              justifyContent: 'center',
              backgroundColor: '#00000099',
              alignItems: 'center',
            }}>
            <View
              style={{
                flexDirection: 'column',
                borderRadius: 10,
                marginLeft: 40,
                marginRight: 40,
                width: Screen_Width - 80,
                flexWrap: 'wrap',
                backgroundColor: '#FFFFFF',
                zIndex: 1,
              }}>
              <Text
                accessibilityLabel={'connect_err'}
                numberOfLines={1}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  lineHeight: 60,
                  textAlign: 'center',
                  textAlignVertical: 'center',
                  color: '#333333',
                  fontSize: 17,
                  fontWeight: 'bold',
                }}>
                {stringsTo('connect_err')}
              </Text>

              <Text
                accessibilityLabel={'tit_one'}
                numberOfLines={10}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  textAlign: 'left',
                  color: '#333333',
                  lineHeight: 18,
                  fontSize: 12,
                }}>
                {stringsTo('connect_error_help_one')}
                <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowErrorHelp: false});
                      this.handleNetReload()
                      // IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID, COMMON_REBIND_URL, {
                      //   mac: this.macAddress,
                      // });
                    }}>
                    {stringsTo('offline_help_tip_reconect')}
                  </Text>
                  <Text>{stringsTo('dot_for_modal')}</Text>
              </Text>
              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_two'}
                  numberOfLines={10}
                  style={{
                    marginLeft: 20,
                    marginRight: 20,
                    width: Screen_Width - 120,
                    marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('connect_error_help_two')}
                </Text>
              )}

              <Text
                accessibilityLabel={'tit_the'}
                numberOfLines={10}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  marginTop: 5,
                  textAlign: 'left',
                  color: '#333333',
                  lineHeight: 18,
                  fontSize: 12,
                }}>
                {stringsTo('connect_error_help_three')}
              </Text>

              <Text
                accessibilityLabel={'tit_the'}
                numberOfLines={10}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  marginTop: 5,
                  textAlign: 'left',
                  color: '#333333',
                  lineHeight: 18,
                  fontSize: 12,
                }}>
                {stringsTo('connect_error_help_four')}
                <Text
                  style={{color: '#4A70A5'}}
                  onPress={() => {
                    this.setState({isShowErrorHelp: false});
                    IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_HELP_URL);
                  }}>
                  {stringsTo('offline_help_tip_feed_question')}
                </Text>
                <Text>{stringsTo('dot_for_modal')}</Text>
              </Text>

              <View
                style={{
                  backgroundColor: '#CCCCCC',
                  width: Screen_Width - 80,
                  height: 1,
                  marginTop: 19,
                }}
              />
              <TouchableWithoutFeedback
                accessibilityLabel={'confirm_btn'}
                onPress={() => {
                  this.setState({isShowErrorHelp: false});
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: Screen_Width - 80,
                    height: 50,
                    borderBottomLeftRadius: 10,
                    borderBottomRightRadius: 10,
                  }}>
                  <Text
                    style={{
                      width: '100%',
                      height: '100%',
                      lineHeight: 50,
                      textAlign: 'center',
                      textAlignVertical: 'center',
                      color: '#4A70A5',
                      fontSize: 15,
                      fontWeight: 'bold',
                    }}>
                    {stringsTo('know_button')}
                  </Text>
                </View>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </ScrollView>
      </Modal>
    );
  }

  showDeviceOfflineDialog() {
    let {isPOE} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
    return (
      <Modal
        supportedOrientations={['portrait', 'landscape']}
        animationType="none"
        transparent={true}
        visible={this.state.isShowOffLineHelp}
        onRequestClose={() => {
          this.setState({isShowOffLineHelp: false});
        }}
        onDismiss={() => {
          this.setState({isShowOffLineHelp: false});
        }}>
        <ScrollView contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={false}>
          <View
            style={{
              flexDirection: 'column',
              flex: 1,
              justifyContent: 'center',
              backgroundColor: '#00000099',
              alignItems: 'center',
            }}>
            <View
              style={{
                flexDirection: 'column',
                borderRadius: 10,
                marginLeft: 40,
                marginRight: 40,
                width: Screen_Width - 80,
                flexWrap: 'wrap',
                backgroundColor: '#FFFFFF',
                zIndex: 1,
              }}>
              <Text
                accessibilityLabel={'only_tit'}
                numberOfLines={1}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  lineHeight: 60,
                  textAlign: 'center',
                  textAlignVertical: 'center',
                  color: '#333333',
                  fontSize: 17,
                  fontWeight: 'bold',
                }}>
                {stringsTo('device_offline')}
              </Text>

              <Text
                accessibilityLabel={'tit_one'}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  textAlign: 'left',
                  color: '#333333',
                  lineHeight: 18,
                  fontSize: 12,
                }}>
                {stringsTo('offline_help_tip_one')}
              </Text>
              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_two'}
                  style={{
                    marginLeft: 20,
                    marginRight: 20,
                    width: Screen_Width - 120,
                    marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_help_two')}
                  {/* <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowOffLineHelp: false});
                      this.handleNetReload()
                      // IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID, COMMON_REBIND_URL, {
                      //   mac: this.macAddress,
                      // });
                    }}>
                    {stringsTo('offline_help_tip_reconect')}
                  </Text>
                  <Text>{stringsTo('dot_for_modal')}</Text> */}
                </Text>
              )}

              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_two'}
                  style={{
                    marginLeft: 20,
                    marginRight: 20,
                    width: Screen_Width - 120,
                    // marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_help_two_first')}
                  <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowOffLineHelp: false});
                      this.handleNetReload()
                      // IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID, COMMON_REBIND_URL, {
                      //   mac: this.macAddress,
                      // });
                    }}>
                    {stringsTo('offline_help_tip_reconect')}
                  </Text>
                  <Text>{stringsTo('dot_for_modal')}</Text>
                </Text>
              )}
              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_two'}
                  style={{
                    marginLeft: 20,
                    marginRight: 20,
                    width: Screen_Width - 120,
                    // marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_help_two_two')}
                  <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowOffLineHelp: false});
                      IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_REBIND_URL);
                    }}>
                    {stringsTo('offline_help_tip_bind')}
                  </Text>
                  <Text>{stringsTo('dot_for_modal')}</Text>
                </Text>
              )}
              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_two'}
                  style={{
                    marginLeft: 20,
                    marginRight: 20,
                    width: Screen_Width - 120,
                    // marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_help_two_three')}
                </Text>
              )}

              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_the'}
                  style={{
                    marginLeft: 20,
                    marginRight: 20,
                    width: Screen_Width - 120,
                    marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_three1')}
                  <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowOffLineHelp: false});
                      this.props.navigation.push('NetInfo')
                    }}>
                    {stringsTo('offline_three2')}
                  </Text>
                  {stringsTo('offline_three3')}
                </Text>
              )}

              {/* {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_five'}
                  style={{
                    marginLeft: 30,
                    marginRight: 20,
                    width: Screen_Width - 130,
                    marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_help_tip_the_first')}
                  <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowOffLineHelp: false});
                      this.props.navigation.push('CustomerServicePage');
                    }}>
                    {stringsTo('offline_help_tip_connect_service')}
                  </Text>
                  <Text>{stringsTo('offline_help_tip_the_thd')}</Text>
                </Text>
              )}

              {isPOE ? null : (
                <Text
                  accessibilityLabel={'tit_five'}
                  style={{
                    marginLeft: 30,
                    marginRight: 20,
                    width: Screen_Width - 130,
                    marginTop: 5,
                    textAlign: 'left',
                    color: '#333333',
                    lineHeight: 18,
                    fontSize: 12,
                  }}>
                  {stringsTo('offline_help_tip_the_sec')}
                  <Text
                    style={{color: '#4A70A5'}}
                    onPress={() => {
                      this.setState({isShowOffLineHelp: false});
                      this.handleNetReload()
                      // IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID, COMMON_REBIND_URL, {
                      //   mac: this.macAddress,
                      // });

                      // IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,COMMON_REBIND_URL);
                    }}>
                    {stringsTo('offline_help_tip_reconect')}
                  </Text>
                  <Text>{stringsTo('dot_for_modal')}</Text>
                </Text>
              )} */}

              {/*老项目不支持网络信息*/}
              {/*<Text accessibilityLabel={"tit_five"}*/}
              {/*      style={{*/}
              {/*          marginLeft: 20,*/}
              {/*          marginRight: 20,*/}
              {/*          width: Screen_Width - 120,*/}
              {/*          marginTop: 5,*/}
              {/*          textAlign: 'left',*/}
              {/*          color:'#333333',*/}
              {/*          lineHeight: 18,*/}
              {/*          fontSize: 12,*/}
              {/*      }}>{stringsTo("offline_help_tip_forth")}*/}
              {/*    <Text style={{ color: '#4A70A5' }} accessibilityLabel={"tit_eight_press"} onPress={()=>{*/}
              {/*        this.setState({ isShowOffLineHelp: false });*/}
              {/*        this.props.navigation.push('NetworkInfoPage');*/}
              {/*    }}>{stringsTo("offline_help_tip_rssi")}</Text>*/}
              {/*    <Text style={{ color: '#333333' }}>{stringsTo("offline_help_tip_forth_first")}</Text>*/}
              {/*</Text>*/}

              <Text
                accessibilityLabel={'tit_ten'}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  marginTop: 5,
                  textAlign: 'left',
                  color: '#333333',
                  lineHeight: 18,
                  fontSize: 12,
                  // backgroundColor:'green'
                }}>
                {stringsTo('offline_help_four')}
              </Text>
              <Text
                accessibilityLabel={'tit_ten'}
                style={{
                  marginLeft: 20,
                  marginRight: 20,
                  width: Screen_Width - 120,
                  marginTop: 5,
                  textAlign: 'left',
                  color: '#333333',
                  lineHeight: 18,
                  fontSize: 12,
                  // backgroundColor:'green'
                }}>
                {stringsTo('offline_help_five')}
                <Text
                  style={{color: '#4A70A5'}}
                  accessibilityLabel={'tit_ten_press'}
                  onPress={() => {
                    this.setState({isShowOffLineHelp: false});
                    IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_HELP_URL);
                  }}>
                  {stringsTo('offline_help_tip_feed_question')}
                </Text>
                <Text>{stringsTo('dot_for_modal')}</Text>
              </Text>

              <View
                style={{
                  backgroundColor: '#CCCCCC',
                  width: Screen_Width - 80,
                  height: 1,
                  marginTop: 19,
                }}
              />
              <TouchableWithoutFeedback
                accessibilityLabel={'confirm_btn'}
                onPress={() => {
                  this.setState({isShowOffLineHelp: false});
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: Screen_Width - 80,
                    height: 50,
                    borderBottomLeftRadius: 10,
                    borderBottomRightRadius: 10,
                  }}>
                  <Text
                    style={{
                      width: '100%',
                      height: '100%',
                      lineHeight: 50,
                      textAlign: 'center',
                      textAlignVertical: 'center',
                      color: '#4A70A5',
                      fontSize: 15,
                      fontWeight: 'bold',
                    }}>
                    {stringsTo('know_button')}
                  </Text>
                </View>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </ScrollView>
      </Modal>
    );
  }
}
let styles = StyleSheet.create({
  cameraViewStyles: {
    width: (getScreenWidth() * 16) / 9, //(getScreenWidth())*16/9,  //等比播放 防止拉伸
    height: getScreenWidth(),
  },
  cameraViewStylesNew: {
    width: (getScreenWidth() * 16) / 9, //20220211@byh width改回16/9 高度百分百，宽度100%时会出现拉升
    height: '100%',
  },
  cameraViewFullStyles: {
    flex: 1,
  },
  containFull: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
